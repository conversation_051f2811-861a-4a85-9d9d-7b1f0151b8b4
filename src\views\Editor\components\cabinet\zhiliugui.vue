<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-10 17:02:30
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  backFill: String, // 背景颜色 #fff
  fill: String // #C4EBAD
})
</script>

<template>
  <svg
    t="1728551048797"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="47905"
    width="200"
    height="200"
  >
    <path
      d="M181.504 128L179.2 838.8096l595.5072 68.0448V151.8592z"
      fill="#E9EBEA"
      p-id="47906"
    ></path>
    <path
      d="M556.544 307.456c1.024-0.512 9.1136 0.3072 10.24 0.4096 4.9664 0.3328 8.5248 6.6048 8.0128 14.0288-0.512 7.3984-4.9408 13.1072-9.856 12.7744-1.28-0.1024-8.2688-0.256-9.2928-1.024-3.0464-2.2784 0.896-7.4752 1.28-13.0048 0.4096-5.6832-3.84-11.4432-0.384-13.184z m0-50.432c1.024-0.512 9.1136 0.3328 10.24 0.4096 4.9664 0.3328 8.5248 6.6304 8.0128 14.0288-0.512 7.3984-4.9408 13.1072-9.856 12.7744-1.28-0.1024-8.2688-0.256-9.2928-1.024-3.0464-2.2784 0.896-7.4752 1.28-13.0048 0.4096-5.6832-3.84-11.4432-0.384-13.1584z"
      fill="#55A32F"
      p-id="47907"
    ></path>
    <path
      d="M555.3664 307.0464c4.9408 0.3584 8.4992 6.656 7.9872 14.0288-0.512 7.424-4.9408 13.1328-9.856 12.8-4.9408-0.3584-8.5248-6.656-8.0128-14.0544 0.512-7.3984 4.9408-13.1072 9.8816-12.7744z m0-50.432c4.9408 0.3584 8.4992 6.656 7.9872 14.0288-0.512 7.424-4.9408 13.1328-9.856 12.8-4.9408-0.3584-8.5248-6.656-8.0128-14.0288 0.512-7.424 4.9408-13.1328 9.8816-12.8z"
      fill="#79DE47"
      p-id="47908"
    ></path>
    <path
      d="M199.68 273.28v55.424a11.52 11.52 0 0 0 10.8032 11.52l214.5792 13.3376a11.52 11.52 0 0 0 12.2368-11.4944v-57.344a11.52 11.52 0 0 0-10.9056-11.52l-214.5792-11.4432a11.52 11.52 0 0 0-12.1344 11.52z"
      fill="#72878A"
      p-id="47909"
    ></path>
    <path
      d="M204.4416 273.2544v51.4048a11.52 11.52 0 0 0 10.8288 11.52l209.792 12.9024a11.52 11.52 0 0 0 12.2368-11.52v-53.2992a11.52 11.52 0 0 0-10.9056-11.4944l-209.8176-11.008a11.52 11.52 0 0 0-12.1344 11.52z"
      fill="#A8C2C6"
      p-id="47910"
    ></path>
    <path
      d="M199.68 369.28v55.424a11.52 11.52 0 0 0 10.8032 11.52l214.5792 13.3376a11.52 11.52 0 0 0 12.2368-11.4944v-57.344a11.52 11.52 0 0 0-10.9056-11.52l-214.5792-11.4432a11.52 11.52 0 0 0-12.1344 11.52z"
      fill="#72878A"
      p-id="47911"
    ></path>
    <path
      d="M204.4416 369.2544v51.4048a11.52 11.52 0 0 0 10.8288 11.52l209.792 12.9024a11.52 11.52 0 0 0 12.2368-11.52v-53.2992a11.52 11.52 0 0 0-10.9056-11.4944l-209.8176-11.008a11.52 11.52 0 0 0-12.1344 11.52z"
      fill="#A8C2C6"
      p-id="47912"
    ></path>
    <path
      d="M199.68 557.7984v55.424c0 6.016 4.608 11.008 10.624 11.4688l214.5792 16.6656a11.52 11.52 0 0 0 12.416-11.4688v-57.344a11.52 11.52 0 0 0-10.752-11.4688l-214.5536-14.7712a11.52 11.52 0 0 0-12.3136 11.4944z"
      fill="#72878A"
      p-id="47913"
    ></path>
    <path
      d="M204.4416 558.2848v51.3792a11.52 11.52 0 0 0 10.6752 11.4944l209.792 15.744a11.52 11.52 0 0 0 12.3904-11.4944V572.16a11.52 11.52 0 0 0-10.752-11.52l-209.8176-13.824a11.52 11.52 0 0 0-12.288 11.4944z"
      fill="#A8C2C6"
      p-id="47914"
    ></path>
    <path
      d="M199.68 465.6384v55.424c0 6.016 4.608 11.008 10.624 11.4688l214.5792 16.6656a11.52 11.52 0 0 0 12.416-11.4688v-57.344a11.52 11.52 0 0 0-10.752-11.4688l-214.5536-14.7712a11.52 11.52 0 0 0-12.3136 11.4944z"
      fill="#72878A"
      p-id="47915"
    ></path>
    <path
      d="M204.4416 466.1248v51.3792a11.52 11.52 0 0 0 10.6752 11.4944l209.792 15.744a11.52 11.52 0 0 0 12.3904-11.4944V480a11.52 11.52 0 0 0-10.752-11.52l-209.8176-13.824a11.52 11.52 0 0 0-12.288 11.4944z"
      fill="#A8C2C6"
      p-id="47916"
    ></path>
    <path
      d="M364.9792 76.8L176.64 136.9088l592.8192 23.8592 118.6048-69.1712z"
      fill="#D4D5D5"
      p-id="47917"
    ></path>
    <path
      d="M525.824 215.04l74.0864 4.096a11.52 11.52 0 0 1 10.88 11.8784l-1.792 51.4048a11.52 11.52 0 0 1-12.16 11.0848l-74.88-4.096a11.52 11.52 0 0 1-10.8544-12.1088l2.56-51.3536a11.52 11.52 0 0 1 12.16-10.9312z"
      fill="#576776"
      p-id="47918"
    ></path>
    <path
      d="M524.4672 213.2992l74.0864 4.096a11.52 11.52 0 0 1 10.88 11.904l-1.792 51.4048a11.52 11.52 0 0 1-12.1344 11.0848l-74.9056-4.096a11.52 11.52 0 0 1-10.8544-12.1088l2.5856-51.3536a11.52 11.52 0 0 1 12.1344-10.9312z"
      fill="#C1ECEC"
      p-id="47919"
    ></path>
    <path
      d="M648.576 215.6288l88.1152 3.584a11.52 11.52 0 0 1 11.0592 11.4944v104.448a11.52 11.52 0 0 1-12.2368 11.52l-88.1152-5.5552a11.52 11.52 0 0 1-10.8032-11.4944V227.1232a11.52 11.52 0 0 1 11.9808-11.52z"
      fill="#576776"
      p-id="47920"
    ></path>
    <path
      d="M888.064 91.5968L769.4592 160.768l0.384 754.9952 115.9168-118.8864z"
      fill="#AEAEAE"
      p-id="47921"
    ></path>
    <path
      d="M591.104 308.9152c1.024-0.512 9.1136 0.3072 10.24 0.384 4.9664 0.3584 8.5248 6.6304 8.0128 14.0288-0.512 7.424-4.9408 13.1328-9.856 12.8-1.28-0.1024-8.2688-0.256-9.2928-1.024-3.0464-2.304 0.896-7.5008 1.28-13.0304 0.4096-5.6576-3.84-11.4432-0.384-13.1584z m-71.6544-2.8928c1.0752-0.512 9.1392 0.3072 10.2912 0.384 4.9152 0.3328 8.4992 6.6304 7.9872 14.0288-0.512 7.424-4.9408 13.1328-9.8816 12.8-1.2288-0.1024-8.2432-0.256-9.2672-1.024-3.072-2.304 0.896-7.5008 1.28-13.0304 0.384-5.6832-3.84-11.4432-0.4096-13.1584z"
      fill="#AB4A1E"
      p-id="47922"
    ></path>
    <path
      d="M589.9264 308.48c4.9408 0.3584 8.4992 6.656 7.9872 14.0544-0.512 7.3984-4.9408 13.1072-9.856 12.7744-4.9408-0.3328-8.5248-6.6304-8.0128-14.0288 0.512-7.424 4.9408-13.1328 9.8816-12.8z m-71.6288-2.8672c4.9152 0.3328 8.4992 6.6304 7.9872 14.0288-0.512 7.3984-4.9408 13.1072-9.8816 12.7744-4.9152-0.3584-8.4992-6.6304-7.9872-14.0288 0.512-7.424 4.9408-13.1328 9.8816-12.8z"
      fill="#DD5E25"
      p-id="47923"
    ></path>
  </svg>
</template>
