<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-10 17:11:33
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  backFill: String, // 背景颜色 #fff
  fill: String // #C4EBAD
})
</script>

<template>
  <svg
    t="1728551454693"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="73541"
    width="200"
    height="200"
  >
    <path
      d="M894.4 59.9H148c-18.9 0-34.3 15.4-34.3 34.3v836.3c0 18.9 15.4 34.3 34.3 34.3h746.5c18.9 0 34.3-15.4 34.3-34.3V94.3c-0.1-19-15.5-34.4-34.4-34.4z m-8.6 42.9v167.5H156.5V102.8h729.3zM156.5 921.9V291.7h353.9v621.8h21.4V291.7h353.9v630.2H156.5z"
      p-id="73542"
      :fill="fill"
    ></path>
    <path
      d="M563.3 460.9h21.4v103h-21.4zM220.4 475.8h235.9V351.4H220.4v124.4z m21.4-103h193v81.5h-193v-81.5zM231.1 656.3h214.5v12.9H231.1zM231.1 803.2h214.5v12.9H231.1zM231.1 773.9h214.5v12.9H231.1zM231.1 685.7h214.5v12.9H231.1zM231.1 715.1h214.5V728H231.1zM231.1 744.5h214.5v12.9H231.1zM592 656.3h214.5v12.9H592zM592 803.2h214.5v12.9H592zM592 773.9h214.5v12.9H592zM592 685.7h214.5v12.9H592zM592 715.1h214.5V728H592zM592 744.5h214.5v12.9H592z"
      p-id="73543"
      :fill="fill"
    ></path>
  </svg>
</template>
