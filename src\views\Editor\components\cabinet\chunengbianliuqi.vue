<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-10 18:30:30
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String // #1296db
})
</script>

<template>
  <svg
    t="1728551496522"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="75558"
    width="200"
    height="200"
  >
    <path
      d="M921.6 0H102.4C46.08 0 0 46.08 0 102.4v710.656c0 56.32 46.08 102.4 102.4 102.4h58.88c0 59.392 48.128 108.032 108.032 108.032h484.352c59.392 0 108.032-48.128 108.032-108.032H921.6c56.32 0 102.4-46.08 102.4-102.4V102.4c0-56.32-46.08-102.4-102.4-102.4z m49.152 782.848c0 42.496-34.304 76.8-76.8 76.8h-762.88c-42.496 0-76.8-34.304-76.8-76.8V128.512c0-42.496 34.304-76.8 76.8-76.8h762.368c42.496 0 76.8 34.304 76.8 76.8V783.36h0.512z"
      :fill="fill"
      p-id="75559"
    ></path>
    <path
      d="M864.768 592.896H159.744c-28.16 0-51.2 23.04-51.2 51.2v112.64c0 28.16 23.04 51.2 51.2 51.2H865.28c28.16 0 51.2-23.04 51.2-51.2v-112.64c0-28.16-23.04-51.2-51.712-51.2zM215.552 754.176c-29.696 0-53.76-24.064-53.76-53.76s24.064-53.76 53.76-53.76 53.76 24.064 53.76 53.76-24.064 53.76-53.76 53.76z m269.824-25.088c0 14.336-11.264 25.6-25.6 25.6h-56.32c-14.336 0-25.6-11.264-25.6-25.6v-56.832c0-14.336 11.264-25.6 25.6-25.6h56.832c14.336 0 25.6 11.264 25.6 25.6v56.832h-0.512z m376.832 0c0 14.336-11.264 25.6-25.6 25.6h-272.384c-14.336 0-25.6-11.264-25.6-25.6v-56.832c0-14.336 11.264-25.6 25.6-25.6h272.384c14.336 0 25.6 11.264 25.6 25.6v56.832zM864.768 107.52H159.744c-28.16 0-51.2 23.04-51.2 51.2v328.192c0 28.16 23.04 51.2 51.2 51.2H865.28c28.16 0 51.2-23.04 51.2-51.2v-327.68c0-28.672-23.04-51.712-51.712-51.712zM219.136 408.064h-76.8V239.616h76.8c52.736 0 78.848 28.16 78.848 83.968s-26.112 84.48-78.848 84.48z m237.568-144.384H393.728c-16.896 0-29.184 3.072-36.864 9.216-12.8 9.216-18.944 26.112-18.944 51.2 0 24.576 6.144 41.472 18.944 50.176 8.192 6.656 20.48 10.24 36.864 10.24h62.976v24.064H393.728c-52.736 0-78.848-28.16-78.848-83.968S340.48 240.64 393.728 240.64h62.976v23.04zM487.936 430.08l-20.992-5.12 47.104-208.384 19.968 5.12-46.08 208.384z m211.456-22.016l-17.92-39.936h-96.256l-15.872 39.936H542.72l70.144-168.448h17.92c8.704 0 15.36 2.048 19.968 6.144 4.096 3.584 8.192 9.728 11.776 18.944l61.952 143.36h-25.088z m182.784-144.384H819.2c-16.896 0-29.184 3.072-36.864 9.216-12.8 9.216-18.944 26.112-18.944 51.2 0 24.576 6.144 41.472 18.944 50.176 8.192 6.656 20.48 10.24 36.864 10.24h62.976v24.064H819.2c-52.736 0-78.848-28.16-78.848-83.968S766.464 240.64 819.2 240.64h62.976v23.04z"
      :fill="fill"
      p-id="75560"
    ></path>
    <path
      d="M630.784 262.656h-2.048l-33.792 80.896H670.72l-32.256-74.24c-1.536-4.608-4.608-6.656-7.68-6.656zM257.024 272.384c-8.192-6.144-20.48-9.216-37.888-9.216H166.4v119.808h53.248c16.896 0 29.184-3.072 37.888-10.24 11.776-8.704 17.92-25.088 17.92-50.176 0-23.552-6.144-40.96-18.432-50.176z"
      :fill="fill"
      p-id="75561"
    ></path>
  </svg>
</template>
