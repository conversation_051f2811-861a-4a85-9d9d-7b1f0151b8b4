<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-23 14:13:48
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String // #C4EBAD
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    version="1.1"
    width="234.01524353027344"
    height="237.84246826171875"
    viewBox="0 0 234.01524353027344 237.84246826171875"
  >
    <g>
      <g
        transform="matrix(0.9076363444328308,0.4197574257850647,-0.4197574257850647,0.9076363444328308,29.443573750900214,-14.426197046347625)"
      >
        <path
          d="M104.00604821777344,172.8587724609375C88.83934821777343,172.8587724609375,74.50604821777344,166.8587724609375,63.839348217773434,156.1917724609375C53.17268821777344,145.5250724609375,47.33935521777344,131.1917724609375,47.50602149777344,116.1917724609375C47.50602149777344,101.02507246093751,53.50601821777344,86.85847246093749,64.17264821777344,76.1917724609375C74.83934821777343,65.52511246093749,88.83934821777343,59.6917724609375,103.83934821777343,59.6917724609375L104.17264821777343,59.6917724609375C135.33934821777342,59.6917724609375,160.67254821777345,85.02507246093751,160.67254821777345,116.1917724609375C160.67254821777345,147.1917724609375,135.33934821777342,172.6917724609375,104.00604821777344,172.8587724609375ZM103.83934821777343,63.0251124609375C89.67264821777343,63.0251124609375,76.50604821777344,68.52511246093749,66.50604821777344,78.52507246093751C56.506028217773434,88.52507246093751,50.83935821777344,102.02507246093751,50.83935821777344,116.1917724609375C50.83935821777344,130.3584724609375,56.17268821777344,143.6917724609375,66.33934821777343,153.8584724609375C76.33934821777343,164.0247724609375,89.83934821777343,169.5247724609375,104.00604821777344,169.5247724609375C133.33934821777342,169.3587724609375,157.33954821777343,145.3584724609375,157.33954821777343,116.1917724609375C157.33954821777343,86.85847246093749,133.50604821777344,63.0251124609375,104.17264821777343,63.0251124609375L103.83934821777343,63.0251124609375Z"
          :fill="props.fill"
          style="mix-blend-mode: passthrough"
        />
      </g>
      <g
        transform="matrix(0.9076363444328308,0.4197574257850647,-0.4197574257850647,0.9076363444328308,11.279521044102694,-51.261101441766186)"
      >
        <path
          d="M178.6207733154297,113.333C163.4540733154297,113.333,149.28747331542968,107.333,138.6207733154297,96.5C127.95410331542969,85.8333,122.12077331542969,71.5,122.12077331542969,56.5C122.28744131542969,25.3333,147.78747331542968,0,178.78747331542968,0C209.9540733154297,0,235.2877733154297,25.5,235.2877733154297,56.6667C235.2877733154297,87.6667,209.9540733154297,113.167,178.9540733154297,113.333L178.6207733154297,113.333ZM178.6207733154297,110L178.9540733154297,110C208.1207733154297,109.833,231.9537733154297,85.8333,231.9537733154297,56.6667C231.9537733154297,27.3333,208.1207733154297,3.33333,178.78747331542968,3.33333C149.6207733154297,3.33333,125.62077331542969,27.1667,125.4541133154297,56.5C125.4541133154297,70.6667,130.95411331542968,84.1667,140.9540733154297,94.1667C150.9540733154297,104.333,164.28747331542968,110,178.6207733154297,110Z"
          :fill="props.fill"
          style="mix-blend-mode: passthrough"
        />
      </g>
      <g
        transform="matrix(-0.004429459571838379,0.9999904036521912,-0.9999904036521912,-0.004429459571838379,226.05478534575923,-149.06744331920436)"
      >
        <path
          d="M222.74397419433595,39.864510625L221.56057419433594,37.994140625L220.37727419433594,39.864510625L193.09622419433595,82.984740625L191.83088419433594,84.984740625L251.29037419433593,84.984740625L250.02497419433593,82.984740625L222.74397419433595,39.864510625ZM221.56057419433594,41.734870625L247.65837419433592,82.984740625L195.46289419433595,82.984740625L221.56057419433594,41.734870625Z"
          fill-rule="evenodd"
          :fill="props.fill"
        />
      </g>
      <g
        transform="matrix(-0.0006461822777055204,0.9999997615814209,-0.9999997615814209,-0.0006461822777055204,331.91600826054867,-52.39385845332416)"
      >
        <path
          d="M227.65031574707032,141.52423962890626L226.46691574707032,139.65386962890625L225.28361574707031,141.52423962890626L198.00256574707032,184.64446962890625L196.7372257470703,186.64446962890625L256.1967157470703,186.64446962890625L254.9313157470703,184.64446962890625L227.65031574707032,141.52423962890626ZM226.46691574707032,143.39459962890626L252.5647157470703,184.64446962890625L200.36923574707032,184.64446962890625L226.46691574707032,143.39459962890626Z"
          fill-rule="evenodd"
          :fill="props.fill"
        />
      </g>
      <g
        transform="matrix(-0.5872642397880554,0.8093953728675842,-0.8093953728675842,-0.5872642397880554,226.96616496473234,102.72982743247576)"
      >
        <line
          x1="87.29051208496094"
          y1="108.23345947265625"
          x2="118.26759147644043"
          y2="108.23345947265625"
          fill-opacity="0"
          stroke-opacity="1"
          :stroke="props.fill"
          fill="none"
          stroke-width="2"
        />
      </g>
      <g
        transform="matrix(-0.5088069438934326,-0.8608806729316711,0.8608807325363159,-0.5088068246841431,-8.181987221174495,320.3165755093878)"
      >
        <line
          x1="87.29060363769531"
          y1="161.49249267578125"
          x2="120.81791305541992"
          y2="161.49249267578125"
          fill-opacity="0"
          stroke-opacity="1"
          :stroke="props.fill"
          fill="none"
          stroke-width="2"
        />
      </g>
      <g
        transform="matrix(0.9998856782913208,-0.017892414703965187,0.017892364412546158,0.9998857378959656,-2.4357778332184807,0.7536819730222248)"
      >
        <line
          x1="41.25193786621094"
          y1="135.39862060546875"
          x2="69.50312995910645"
          y2="135.39862060546875"
          fill-opacity="0"
          stroke-opacity="1"
          :stroke="props.fill"
          fill="none"
          stroke-width="2"
        />
      </g>
      <g
        transform="matrix(0.9633679986000061,0.2681834399700165,-0.2681834399700165,0.9633679986000061,30.98730676155992,-29.914124916752826)"
      >
        <path
          d="M181.6607948486328,211.63886279296875C150.32739484863282,211.63886279296875,124.99409484863281,186.13856279296874,124.99409484863281,154.97186279296875C124.99409484863281,123.80516279296876,150.4940948486328,98.47186279296875,181.6607948486328,98.47186279296875C212.82739484863282,98.47186279296875,238.1610948486328,123.97186279296875,238.1610948486328,155.13856279296874C238.1610948486328,186.30516279296876,212.82739484863282,211.63886279296875,181.6607948486328,211.63886279296875ZM181.82739484863282,101.80520279296876C152.4940948486328,101.80520279296876,128.49411484863282,125.63856279296874,128.49411484863282,154.97186279296875C128.49411484863282,184.30516279296876,152.32749484863282,208.30486279296875,181.6607948486328,208.30486279296875C210.9940948486328,208.30486279296875,234.9940948486328,184.47186279296875,234.9940948486328,155.13856279296874C234.8270948486328,125.80516279296876,210.9940948486328,101.80519279296875,181.82739484863282,101.80520279296876Z"
          :fill="props.fill"
          style="mix-blend-mode: passthrough"
        />
      </g>
    </g>
  </svg>
</template>
