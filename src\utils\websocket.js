/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-07-24 12:30:03
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-15 16:43:52
 * @FilePath: \ems_manage\src\utils\webSocket.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
const MAX_RECONNECT_ATTEMPTS = 5

class WebSocketClient {
  constructor(url, options = {}) {
    const {
      isReconnect = true,
      isHeartbeat = true,
      heartbeatMessage = JSON.stringify({
        type: 'ping'
      })
    } = options

    this.url = url // WebSocket连接地址
    this.ws = null // WebSocket实例
    this.isReconnect = isReconnect // 是否自动重连
    this.isHeartbeat = isHeartbeat // 是否开启心跳检测
    this.heartbeatMessage = heartbeatMessage
    this.heartbeatInterval = null // 心跳检测定时器
    this.reconnectAttempts = 0 // 重连尝试次数
    this.maxReconnectAttempts = MAX_RECONNECT_ATTEMPTS // 最大重连次数
    this.isConnected = false // 连接状态
    this.eventListeners = {} // 自定义事件监听器
  }

  connect() {
    if (this.ws) this.ws.close()
    this.ws = new WebSocket(this.url)
    this.ws.onopen = () => {
      this._onOpen()
    }
    this.ws.onmessage = (event) => {
      this._onMessage(event)
    }
    this.ws.onclose = () => {
      this._onClose()
    }
    this.ws.onerror = (error) => {
      this._onError(error)
    }
  }

  _onOpen() {
    this.isConnected = true
    this.reconnectAttempts = 0
    if (this.isHeartbeat)
      this.heartbeatInterval = setInterval(
        this._sendHeartbeat.bind(this),
        30000
      )
    console.log('建立连接')
    this.emit('open')
  }

  _onClose() {
    this.isConnected = false
    if (this.heartbeatInterval) clearInterval(this.heartbeatInterval)
    console.log('连接关闭')
    this.emit('close')
  }

  _onMessage(event) {
    console.log('收到消息')
    this.emit('message', event) // 触发自定义 message 事件
  }

  _onError(event) {
    console.log('连接错误')
    if (this.isReconnect && this.ws.readyState === WebSocket.OPEN)
      this.reconnect()
    this.emit('error', event)
  }

  _sendHeartbeat() {
    if (this.ws && this.ws.readyState === WebSocket.OPEN) {
      this.sendMessage(this.heartbeatMessage) // 发送心跳 ping 消息
    }
  }

  reconnect() {
    if (!this.isReconnect) {
      console.error('重连未开启')
      return
    }
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      setTimeout(() => {
        console.log('尝试重新连接...')
        this.connect()
        this.reconnectAttempts++
      }, this.reconnectAttempts * 2000) // 延迟时间逐渐增加
    } else {
      console.error('重连尝试达到最大次数')
    }
  }

  sendMessage(message) {
    if (this.ws.readyState === WebSocket.OPEN) {
      this.ws.send(message)
    } else {
      console.error('WebSocket连接未打开')
    }
  }

  on(eventName, callback) {
    if (!this.eventListeners[eventName]) {
      this.eventListeners[eventName] = []
    }
    this.eventListeners[eventName].push(callback)
  }

  emit(eventName, data) {
    if (this.eventListeners[eventName]) {
      this.eventListeners[eventName].forEach((callback) => callback(data))
    }
  }

  close() {
    if (!this.ws) return
    this.isConnected = false
    this.ws.close()
    if (this.heartbeatInterval) clearInterval(this.heartbeatInterval)
  }
}

export default WebSocketClient
