import { ref } from 'vue'

export function useIndexedDB() {
  const db = ref(null)

  function openDB(name, version, setup) {
    if (db.value && db.value.readyState === 'open') {
      console.log('数据库已打开，复用')
      return Promise.resolve(db.value)
    }
    return new Promise((resolve, reject) => {
      const request = indexedDB.open(name, version)

      request.onupgradeneeded = (event) => {
        const database = event.target.result
        if (setup) {
          setup(database, event)
        }
      }

      request.onblocked = (event) => {
        console.warn('请关闭其他打开了该站点的标签页！')
      }

      request.onversionchange = (event) => {
        request.close()
        console.warn('此页面的新版本已准备就绪。请重新加载或关闭此标签页！')
      }

      request.onsuccess = () => {
        db.value = request.result
        resolve(db.value)
      }
      request.onerror = () => reject(request.error)
    })
  }

  return { db, openDB }
}
