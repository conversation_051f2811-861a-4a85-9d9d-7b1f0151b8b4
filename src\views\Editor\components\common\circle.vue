<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-23 15:07:51
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  borderFill: String, // 背景颜色 #fff
  fill: String, // #C4EBAD
  opacity: Number
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    version="1.1"
    width="79.75460815429688"
    height="74.8466796875"
    viewBox="0 0 79.75460815429688 74.8466796875"
  >
    <g>
      <ellipse
        cx="39.87730407714844"
        cy="37.42333984375"
        rx="39.87730407714844"
        ry="37.42333984375"
        :fill="props.fill"
        :fill-opacity="props.opacity"
      />
      <ellipse
        cx="39.87730407714844"
        cy="37.42333984375"
        rx="38.87730407714844"
        ry="36.42333984375"
        fill-opacity="0"
        stroke-opacity="1"
        :stroke="props.borderFill"
        fill="none"
        stroke-width="2"
      />
    </g>
  </svg>
</template>
