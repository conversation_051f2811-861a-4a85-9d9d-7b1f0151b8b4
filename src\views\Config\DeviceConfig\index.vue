<script setup>
import { ref, toRefs, getCurrentInstance, computed } from 'vue'
import { useDeviceConfigStore } from '@/store/module/deviceConfig'
import { useGlobalStore } from '@/store/global'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { rebootEMS, rebootEMSs } from '@/api/deviceConfig'
import {
  allDeviceTypes,
  hardwareModelOptions,
  baudRateOptions,
  parityOptions,
  dataBitOptions,
  stopBitOptions
} from '@/constant'
import draggable from 'vuedraggable'
import dayjs from '@/utils/date'
import { random } from 'lodash-es'

import { ElTable, ElTableColumn } from 'element-plus'

const router = useRouter()
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const {
  deviceConfigList,
  isEdit,
  deviceClassData,
  adaptorTypeOptions,
  protocolFileData,
  serialPortData,
  hardwareModelData
} = toRefs(useDeviceConfigStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const headers = ref([
  {
    title: t('设备名称'),
    key: 'device_name',
    sortable: false,
    minWidth: '140px'
  },
  {
    title: t('设备类名'),
    key: 'device_class',
    sortable: false,
    minWidth: '150px'
  },
  // {
  //   title: t('设备类型'),
  //   key: 'device_type',
  //   sortable: false,
  //   minWidth: '150px'
  // },
  {
    title: t('设备序列号(云平台)'),
    key: 'device_ac',
    sortable: false,
    minWidth: '150px'
  },
  {
    title: t('设备模块编号(云平台)'),
    key: 'device_dc',
    sortable: false,
    minWidth: '100px'
  },
  {
    title: t('设备模块类型(云平台)'),
    key: 'device_det',
    sortable: false,
    minWidth: '100px'
  },
  { title: t('操作'), key: 'action', sortable: false, minWidth: '220px' }
])
useDeviceConfigStore().getDeviceClassNames()
useDeviceConfigStore().getAdaptorTypes()
useDeviceConfigStore().getProtocolFiles()
useDeviceConfigStore().getSerialPorts()
useDeviceConfigStore().getHardwareModel()

const dialog = ref(false)
const dialogTitle = ref(t('添加设备'))
const loading = ref(false)
const form = ref({
  // device_id: undefined,
  device_name: undefined,
  device_class: undefined,
  // device_type: undefined,
  device_ac: undefined,
  device_dc: undefined,
  device_det: undefined,
  protocols: [
    {
      type: 'adaptor'
    }
  ]
})
const rules = {
  device_class: [(v) => !!v || t('设备类名')],
  // device_id: [(v) => !!v || t('设备编号')],
  device_name: [(v) => !!v || t('设备名称')]
  // device_type: [(v) => !!v || t('设备类型')]
}
const handleDeviceClassChange = (e) => {
  if (!e) return
  let value = allDeviceTypes.find((item) => item.class == e)
  if (!value) return
  // form.value.device_type = value.type
  form.value.device_det = deviceClassData.value.find(
    (item) => item.title == e
  ).det
  handleDetChange()
  let hardwareVersion = hardwareModelOptions.find(
    (item) => item.label == hardwareModelData.value
  ).value
  let sn = generateSerialNumber(value.typeId, hardwareVersion, random(999))
  form.value.device_ac = sn
}
const generateSerialNumber = (deviceTypeId, hardwareVersion, serialNumber) => {
  // 格式化日期为 YYMMDD
  const formattedDate = dayjs(new Date()).format('YYMMDD')

  // 格式化设备类型ID为 3 位数字
  const formattedDeviceTypeId = String(deviceTypeId).padStart(3, '0')

  // 格式化硬件版本为 1 位数字
  const formattedHardwareVersion = String(hardwareVersion).padStart(1, '0')

  // 格式化流水号为 3 位数字
  const formattedSerialNumber = String(serialNumber).padStart(3, '0')

  // 拼接序列号
  return `ems_${formattedDate}${formattedDeviceTypeId}${formattedHardwareVersion}${formattedSerialNumber}`
}
const handleDetChange = () => {
  if (!form.value.device_det) return
  let existArrs = deviceConfigList.value.filter(
    (item) => item.device_det == form.value.device_det
  )
  form.value.device_dc = `${form.value.device_det}${String(
    existArrs.length
  ).padStart(4, 0)}`
}
const handleAddClick = () => {
  dialogTitle.value = t('添加设备')
  form.value = {
    // device_id: undefined,
    device_name: undefined,
    device_class: undefined,
    // device_type: undefined,
    device_ac: undefined,
    device_dc: undefined,
    device_det: undefined,
    protocols: [
      {
        type: 'adaptor'
      }
    ]
  }
  dialog.value = true
}
// 修改
const handleEditClick = (item) => {
  dialogTitle.value = t('修改设备')
  form.value = {
    ...item
  }
  dialog.value = true
}
const handleRemoveClick = async (item) => {
  let index = deviceConfigList.value.findIndex(
    (item1) => item1.device_name == item.device_name
  )
  if (index !== -1) deviceConfigList.value.splice(index, 1)
  editCount.value++
  snackbar.value = true
  snackbarText.value = t('删除成功')
}
const submit = async () => {
  let protocols = []
  form.value.protocols = form.value.protocols.map((item) => {
    if (item.type == 'adaptor') {
      if (item.adaptor_type == 'modbus-TCP') {
        return {
          type: item.type,
          adaptor_type: item.adaptor_type,
          addr: item.addr,
          ip: item.ip,
          port: item.port,
          adaptor_count: item.adaptor_count,
          delay: item.delay,
          read_file: item.read_file,
          control_file: item.control_file
        }
      } else if (item.adaptor_type == 'modbus-RTU') {
        return {
          type: item.type,
          adaptor_type: item.adaptor_type,
          addr: item.addr,
          device: item.device,
          baud_rate: item.baud_rate,
          parity: item.parity,
          data_bits: item.data_bits,
          stop_bits: item.stop_bits,
          adaptor_count: item.adaptor_count,
          delay: item.delay,
          read_file: item.read_file,
          control_file: item.control_file
        }
      } else {
        return {
          type: item.type,
          adaptor_type: item.adaptor_type,
          adaptor_count: item.adaptor_count,
          delay: item.delay,
          read_file: item.read_file,
          control_file: item.control_file
        }
      }
    } else {
      return {
        type: item.type,
        use_config_file: item.use_config_file
      }
    }
  })
  const { valid } = await proxy.$refs.FormRef.validate()
  if (!valid) return
  let deviceNameIndex = deviceConfigList.value?.findIndex(
    (item) => item.device_name == form.value.device_name
  )
  loading.value = true
  if (dialogTitle.value === t('添加设备')) {
    if (deviceNameIndex != -1) {
      snackbar.value = true
      snackbarText.value = t('设备名称重复了')
      return
    }
    deviceConfigList.value.push({
      // device_id: form.value.device_id,
      device_name: form.value.device_name,
      device_class: form.value.device_class,
      // device_type: form.value.device_type,
      device_ac: form.value.device_ac,
      device_dc: form.value.device_dc,
      device_det: form.value.device_det,
      protocols: form.value.protocols
    })
  } else {
    let index = deviceConfigList.value?.findIndex(
      (item) => item.device_name == form.value.device_name
    )
    deviceConfigList.value.splice(index, 1, {
      // device_id: form.value.device_id,
      device_name: form.value.device_name,
      device_class: form.value.device_class,
      // device_type: form.value.device_type,
      device_ac: form.value.device_ac,
      device_dc: form.value.device_dc,
      device_det: form.value.device_det,
      protocols: form.value.protocols
    })
  }
  try {
    editCount.value++
    snackbar.value = true
    snackbarText.value =
      dialogTitle.value === t('添加设备') ? t('添加成功') : t('修改成功')
    loading.value = false
    dialog.value = false
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
  }
}
const editCount = ref(0)
const handleUseClick = async () => {
  useDialog.value = false
  pwdDialog.value = true
}
const setDeviceFileFn = async () => {
  const res = await useDeviceConfigStore().setDeviceFileFn()
}
const useDialog = ref(false)
const handleBackClick = () => {
  password.value = undefined
  if (editCount.value !== 0) {
    useDialog.value = true
    return
  }
  isEdit.value = false
  router.back()
  useDeviceConfigStore().getFilesData()
}
const handleUseCancelClick = () => {
  password.value = undefined
  useDialog.value = false
  isEdit.value = false
  router.back()
  useDeviceConfigStore().getFilesData()
}
const handleUseConfirm = async () => {
  useDialog.value = false
  pwdDialog.value = true
}
const handleCancelClick = () => {
  loading.value = false
  dialog.value = false
}
// 密码弹框
const pwdDialog = ref(false)
const password = ref()
const eye = ref(false)
const handleEyeClick = () => (eye.value = !eye.value)
const handlePwdCancelClick = () => {
  pwdDialog.value = false
}
const handlePwdConfirm = async () => {
  if (!password.value) {
    snackbar.value = true
    snackbarText.value = t('请输入密码')
    return
  }
  const res = await rebootEMS(JSON.stringify({ password: password.value }))
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  try {
    await setDeviceFileFn()
    const res2 = await rebootEMSs({ name: res.data })
    if (res2.code !== 201) {
      snackbar.value = true
      snackbarText.value = res2.msg
      return
    }
    editCount.value = 0
    snackbar.value = true
    snackbarText.value = t('应用成功')
    handleBackClick()
  } catch (error) {
    console.log(error)
    pwdDialog.value = false
    snackbar.value = true
    snackbarText.value = t('应用失败') + error
  }
}
/**
 * 查看协议信息
 */
const lookDialog = ref(false)
const lookDialogTitle = ref('')
const lookData = ref()
const lookHeaders = ref([
  { title: t('协议类型'), key: 'type', sortable: false, minWidth: '140px' },
  {
    title: t('适配器协议类型'),
    key: 'adaptor_type',
    sortable: false,
    minWidth: '140px'
  },
  {
    title: t('适配器数量'),
    key: 'adaptor_count',
    sortable: false,
    minWidth: '140px'
  },
  { title: t('从站地址'), key: 'addr', sortable: false, minWidth: '140px' },
  {
    title: t('协议文件'),
    key: 'read_file',
    sortable: false,
    minWidth: '140px'
  },
  {
    title: t('控制文件'),
    key: 'control_file',
    sortable: false,
    minWidth: '140px'
  },
  { title: t('读写周期'), key: 'delay', sortable: false, minWidth: '140px' },
  {
    title: t('操作'),
    key: 'action',
    sortable: false,
    minWidth: '200px',
    fixed: 'right'
  }
])
const handleLookClick = (item) => {
  lookData.value = item
  lookDialogTitle.value = item.device_name + t('协议信息')
  lookDialog.value = true
}
/**
 * 协议
 */
const handleProtocolDeleteClick = (index) => {
  if (form.value.protocols.length == 1) {
    snackbar.value = true
    snackbarText.value = t('至少要有一条哦')
    return
  }
  form.value.protocols.splice(index, 1)
}
const handleProtocolAddClick = () => {
  form.value.protocols.push({
    type: 'adaptor'
  })
}
const protocolIndex = ref(0)
const handleProtocolEditClick = (index) => {
  protocolIndex.value = index
  let value = form.value.protocols[index]
  let data = {}
  if (value.type == 'adaptor') {
    data = {
      adaptor_type: 'modbus-TCP',
      adaptor_count: '1',
      addr: '1',
      read_file: undefined,
      control_file: undefined,
      delay: undefined,
      ip: undefined,
      port: '502',
      device: undefined,
      baud_rate: 9600,
      parity: 'N',
      data_bits: '8',
      stop_bits: '1'
    }
  } else {
    data = {
      use_config_file: undefined
    }
  }
  protocolForm.value = {
    ...data,
    ...value
  }
  protocolDialog.value = true
}
const protocolDialog = ref(false)
const protocolLoading = ref(false)
const protocolRules = {
  ip: [(v) => !!v || 'IP'],
  device: [(v) => !!v || t('串口文件路径')],
  adaptor_type: [(v) => !!v || t('适配器类型')],
  adaptor_count: [(v) => !!v || t('适配器数量')],
  read_file: [(v) => !!v || t('XML协议文件')],
  use_config_file: [(v) => !!v || t('故障录波JSON文件')]
}
const protocolForm = ref({
  type: 'adaptor'
})
const handleProtocolCancelClick = () => {
  protocolLoading.value = false
  protocolDialog.value = false
}
const protocolSubmit = async () => {
  const { valid } = await proxy.$refs.protocolFormRef.validate()
  if (!valid) return
  protocolLoading.value = true
  form.value.protocols.splice(protocolIndex.value, 1, {
    ...protocolForm.value
  })
  snackbar.value = true
  snackbarText.value = t('修改成功')
  protocolLoading.value = false
  protocolDialog.value = false
}

/**
 * 键盘
 */
keyboardMode.value = 'en'
const keyboardObj = ref({})
const handleShow = (e, value, obj) => {
  if (isShowKeyboard.value) return
  keyboardObj.value = {
    ...obj
  }
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  console.log(keyboardObj.value)
  if (keyboardObj.value.type == 1) {
    password.value = keyboardInputValue.value
  } else if (keyboardObj.value.type == 2) {
    protocolForm.value[keyboardObj.value.prop] = keyboardInputValue.value
  } else if (keyboardObj.value.type == 3) {
    form.value[keyboardObj.value.prop] = keyboardInputValue.value
  }
  showKeyboard.value = false
  keyboardDialog.value = false
}

const dragChange = (e) => {
  editCount.value++
}
const drag = ref(false)
</script>

<template>
  <div class="w-100 px-2">
    <v-card class="pa-4 w-100 rounded-lg no-scrollbar" elevation="4">
      <div class="mb-4">
        <v-btn height="48px" color="primary" @click="handleAddClick">{{
          $t('添加设备')
        }}</v-btn>
        <v-btn
          height="48px"
          color="primary"
          @click="handleBackClick"
          class="ml-2"
          >{{ $t('返回') }}</v-btn
        >
        <v-btn
          height="48px"
          color="primary"
          @click="handleUseClick"
          class="ml-2"
          >{{ $t('应用') }}</v-btn
        >
      </div>
      <v-data-table
        :headers="headers"
        :items="deviceConfigList"
        hide-default-footer
        items-per-page="-1"
      >
        <thead>
          <tr>
            <th scope="col">{{ $t('设备名称') }}</th>
            <th scope="col">{{ $t('设备类名') }}</th>
            <th scope="col">{{ $t('设备类型') }}</th>
            <th scope="col">{{ $t('设备序列号(云平台)') }}</th>
            <th scope="col">{{ $t('设备模块编号(云平台)') }}</th>
            <th scope="col">{{ $t('设备模块类型(云平台)') }}</th>
            <th scope="col">{{ $t('操作') }}</th>
          </tr>
        </thead>
        <!-- </template> -->
        <draggable
          v-model="deviceConfigList"
          tag="tbody"
          :component-data="{
            tag: 'ul',
            type: 'transition-group',
            name: !drag ? 'flip-list' : null
          }"
          :animation="200"
          group="description"
          :disabled="false"
          ghostClass="ghost"
          @start="drag = true"
          @end="drag = false"
          item-key="device_name"
          @change="dragChange"
        >
          <template #item="{ element }">
            <tr :data-id="element.device_name">
              <td>{{ element.device_name }}</td>
              <td>{{ element.device_class }}</td>
              <!-- <td>{{ element.device_type }}</td> -->
              <td>{{ element.device_ac }}</td>
              <td>{{ element.device_dc }}</td>
              <td>{{ element.device_det }}</td>
              <td>
                <v-btn class="ml-2" @click="handleEditClick(element)">{{
                  $t('修改')
                }}</v-btn>
                <v-btn class="ml-2" @click="handleRemoveClick(element)">{{
                  $t('删除')
                }}</v-btn>
              </td>
            </tr>
          </template>
        </draggable>
      </v-data-table>

      <v-dialog v-model="dialog" width="auto">
        <v-card width="740" class="pa-4 rounded-lg">
          <v-card-title class="text-center mb-6">{{
            dialogTitle
          }}</v-card-title>
          <v-sheet class="mx-auto w-full">
            <v-form fast-fail @submit.prevent="submit" ref="FormRef">
              <v-row>
                <v-col cols="6">
                  <v-text-field
                    v-model="form.device_name"
                    :label="$t('设备名称')"
                    :placeholder="$t('设备名称')"
                    variant="outlined"
                    clearable
                    hide-details
                    :rules="rules.device_name"
                    @click:control="
                      handleShow($event, form.device_name, {
                        type: 3,
                        prop: 'device_name'
                      })
                    "
                  ></v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-select
                    v-model="form.device_class"
                    item-value="value"
                    clearable
                    :label="$t('设备类名')"
                    :placeholder="$t('设备类名')"
                    :items="deviceClassData"
                    variant="outlined"
                    class="w-100 mr-4 mt-2"
                    hide-details
                    :rules="rules.device_class"
                    @update:modelValue="handleDeviceClassChange"
                  ></v-select>
                </v-col>
                <!-- <v-col cols="6">
                  <v-select
                    v-model="form.device_type"
                    item-value="value"
                    clearable
                    :label="$t('设备类型')"
                    :placeholder="$t('设备类型')"
                    :items="deviceTypeOptions"
                    variant="outlined"
                    class="w-100 mr-4 mt-2"
                    hide-details
                    :rules="rules.device_type"
                  ></v-select>
                </v-col> -->
                <v-col cols="6">
                  <v-text-field
                    v-model="form.device_ac"
                    :label="$t('设备序列号(云平台)')"
                    :placeholder="$t('设备序列号(云平台)')"
                    variant="outlined"
                    clearable
                    hide-details
                    @click:control="
                      handleShow($event, form.device_ac, {
                        type: 3,
                        prop: 'device_ac'
                      })
                    "
                  ></v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field
                    v-model="form.device_dc"
                    :label="$t('设备模块编号(云平台)')"
                    :placeholder="$t('设备模块编号(云平台)')"
                    variant="outlined"
                    clearable
                    hide-details
                    @click:control="
                      handleShow($event, form.device_dc, {
                        type: 3,
                        prop: 'device_dc'
                      })
                    "
                  ></v-text-field>
                </v-col>
                <v-col cols="6">
                  <v-text-field
                    v-model="form.device_det"
                    :label="$t('设备模块类型(云平台)')"
                    :placeholder="$t('设备模块类型(云平台)')"
                    variant="outlined"
                    clearable
                    hide-details
                    @click:control="
                      handleShow($event, form.device_det, {
                        type: 3,
                        prop: 'device_det'
                      })
                    "
                    @update:modelValue="handleDetChange"
                  ></v-text-field>
                </v-col>
                <v-col cols="12">
                  <el-table
                    :data="form.protocols"
                    border
                    style="width: 100%"
                    class="protocol"
                  >
                    <el-table-column
                      :prop="$t('协议类型')"
                      :label="$t('协议类型')"
                      align="center"
                    >
                      <template #default="scoped">
                        <v-select
                          v-model="scoped.row.type"
                          item-value="id"
                          :items="[
                            { id: 'adaptor', title: 'adaptor' },
                            { id: 'fault_record', title: 'fault_record' }
                          ]"
                          variant="outlined"
                          class="w-100"
                          hide-details
                        ></v-select>
                      </template>
                    </el-table-column>
                    <el-table-column
                      :label="$t('操作')"
                      width="300"
                      align="center"
                    >
                      <template #default="scoped">
                        <v-tooltip :text="$t('添加')" location="top">
                          <template v-slot:activator="{ props }">
                            <v-btn
                              icon="mdi-plus-circle"
                              variant="plain"
                              v-bind="props"
                              style="width: 24px; height: 24px"
                              @click="handleProtocolAddClick(scoped.$index)"
                            ></v-btn>
                          </template>
                        </v-tooltip>
                        <v-tooltip :text="$t('修改')" location="top">
                          <template v-slot:activator="{ props }">
                            <v-btn
                              icon="mdi-pencil-circle"
                              variant="plain"
                              v-bind="props"
                              style="width: 24px; height: 24px"
                              @click="handleProtocolEditClick(scoped.$index)"
                              class="mx-2"
                            ></v-btn>
                          </template>
                        </v-tooltip>
                        <v-tooltip :text="$t('删除')" location="top">
                          <template v-slot:activator="{ props }">
                            <v-btn
                              icon="mdi-close-circle"
                              variant="plain"
                              v-bind="props"
                              style="width: 24px; height: 24px"
                              @click="handleProtocolDeleteClick(scoped.$index)"
                            ></v-btn>
                          </template>
                        </v-tooltip>
                      </template>
                    </el-table-column>
                  </el-table>
                </v-col>
              </v-row>
              <div class="d-flex justify-center mt-6">
                <v-btn
                  class="mt-2 mr-4 px-8"
                  height="50"
                  @click="handleCancelClick"
                  >{{ $t('取消') }}</v-btn
                >
                <v-btn
                  class="mt-2 px-8"
                  type="submit"
                  height="50"
                  :loading="loading"
                  color="primary"
                  >{{ $t('确定') }}</v-btn
                >
              </div>
            </v-form>
          </v-sheet>
        </v-card>
      </v-dialog>

      <v-dialog v-model="lookDialog" width="auto">
        <v-card width="740" class="pa-4 rounded-lg">
          <v-card-title class="text-center mb-6">{{
            lookDialogTitle
          }}</v-card-title>
          <v-data-table
            :headers="lookHeaders"
            :items="lookData.protocols"
            :hide-default-footer="true"
          >
            <template v-slot:item.action="{ item }">
              <v-btn class="ml-2" @click="handleEditClick(item)">{{
                $t('修改')
              }}</v-btn>
              <v-btn class="ml-2" @click="handleRemoveClick(item)">{{
                $t('删除')
              }}</v-btn>
            </template>
          </v-data-table>
        </v-card>
      </v-dialog>

      <v-dialog v-model="protocolDialog" width="auto">
        <v-card width="740" class="pa-4 rounded-lg">
          <v-card-title class="text-center mb-6">{{
            $t('协议文件')
          }}</v-card-title>
          <v-sheet class="mx-auto w-full">
            <v-form
              fast-fail
              @submit.prevent="protocolSubmit"
              ref="protocolFormRef"
            >
              <v-row>
                <v-col cols="12">
                  <v-select
                    v-model="protocolForm.type"
                    item-value="id"
                    :items="[
                      { id: 'adaptor', title: 'adaptor' },
                      { id: 'fault_record', title: 'fault_record' }
                    ]"
                    :label="$t('协议类型')"
                    :placeholder="$t('协议类型')"
                    variant="outlined"
                    class="w-100"
                    hide-details
                  ></v-select>
                </v-col>
                <template v-if="protocolForm.type == 'adaptor'">
                  <v-col cols="6">
                    <v-select
                      v-model="protocolForm.adaptor_type"
                      item-value="value"
                      clearable
                      :label="$t('适配器类型')"
                      :placeholder="$t('适配器类型')"
                      :items="adaptorTypeOptions"
                      variant="outlined"
                      class="w-100 mr-4 mt-2"
                      hide-details
                      :rules="protocolRules.adaptor_type"
                    ></v-select>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      v-model="protocolForm.adaptor_count"
                      :label="$t('适配器数量')"
                      :placeholder="$t('适配器数量')"
                      variant="outlined"
                      clearable
                      hide-details
                      :rules="protocolRules.adaptor_count"
                      @click:control="
                        handleShow($event, protocolForm.adaptor_count, {
                          type: 2,
                          prop: 'adaptor_count'
                        })
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col
                    cols="6"
                    v-if="
                      protocolForm.adaptor_type == 'modbus-TCP' ||
                      protocolForm.adaptor_type == 'modbus-RTU'
                    "
                  >
                    <v-text-field
                      v-model="protocolForm.addr"
                      :label="$t('从站地址')"
                      :placeholder="$t('从站地址')"
                      variant="outlined"
                      clearable
                      hide-details
                      @click:control="
                        handleShow($event, protocolForm.addr, {
                          type: 2,
                          prop: 'addr'
                        })
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="6">
                    <v-text-field
                      v-model="protocolForm.delay"
                      label="读写周期"
                      placeholder="读写周期"
                      variant="outlined"
                      clearable
                      hide-details
                      @click:control="
                        handleShow($event, protocolForm.delay, {
                          type: 2,
                          prop: 'delay'
                        })
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col
                    cols="6"
                    v-if="protocolForm.adaptor_type == 'modbus-TCP'"
                  >
                    <v-text-field
                      v-model="protocolForm.ip"
                      :label="$t('设备IP')"
                      :placeholder="$t('设备IP')"
                      variant="outlined"
                      clearable
                      hide-details
                      :rules="protocolRules.ip"
                      @click:control="
                        handleShow($event, protocolForm.ip, {
                          type: 2,
                          prop: 'ip'
                        })
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col
                    cols="6"
                    v-if="protocolForm.adaptor_type == 'modbus-TCP'"
                  >
                    <v-text-field
                      v-model="protocolForm.port"
                      :label="$t('设备端口')"
                      :placeholder="$t('设备端口')"
                      variant="outlined"
                      clearable
                      hide-details
                      @click:control="
                        handleShow($event, protocolForm.port, {
                          type: 2,
                          prop: 'port'
                        })
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col
                    cols="12"
                    v-if="protocolForm.adaptor_type == 'modbus-RTU'"
                  >
                    <v-select
                      v-model="protocolForm.device"
                      item-value="value"
                      clearable
                      :label="$t('串口文件路径')"
                      :placeholder="$t('串口文件路径')"
                      :items="serialPortData"
                      variant="outlined"
                      class="w-100 mr-4 mt-2"
                      hide-details
                      :rules="protocolRules.device"
                    ></v-select>
                  </v-col>
                  <v-col
                    cols="6"
                    v-if="protocolForm.adaptor_type == 'modbus-RTU'"
                  >
                    <v-select
                      v-model="protocolForm.baud_rate"
                      item-value="value"
                      clearable
                      :label="$t('串口波特率')"
                      :placeholder="$t('串口波特率')"
                      :items="baudRateOptions"
                      variant="outlined"
                      class="w-100 mr-4 mt-2"
                      hide-details
                    ></v-select>
                  </v-col>
                  <v-col
                    cols="6"
                    v-if="protocolForm.adaptor_type == 'modbus-RTU'"
                  >
                    <v-select
                      v-model="protocolForm.parity"
                      item-value="value"
                      clearable
                      :label="$t('校验位')"
                      :placeholder="$t('校验位')"
                      :items="parityOptions"
                      variant="outlined"
                      class="w-100 mr-4 mt-2"
                      hide-details
                    ></v-select>
                  </v-col>
                  <v-col
                    cols="6"
                    v-if="protocolForm.adaptor_type == 'modbus-RTU'"
                  >
                    <v-select
                      v-model="protocolForm.data_bits"
                      item-value="value"
                      clearable
                      :label="$t('数据位')"
                      :placeholder="$t('数据位')"
                      :items="dataBitOptions"
                      variant="outlined"
                      class="w-100 mr-4 mt-2"
                      hide-details
                    ></v-select>
                  </v-col>
                  <v-col
                    cols="6"
                    v-if="protocolForm.adaptor_type == 'modbus-RTU'"
                  >
                    <v-select
                      v-model="protocolForm.stop_bits"
                      item-value="value"
                      clearable
                      :label="$t('停止位')"
                      :placeholder="$t('停止位')"
                      :items="stopBitOptions"
                      variant="outlined"
                      class="w-100 mr-4 mt-2"
                      hide-details
                    ></v-select>
                  </v-col>
                  <v-col cols="12">
                    <v-select
                      v-model="protocolForm.read_file"
                      item-value="value"
                      clearable
                      :label="$t('XML协议文件')"
                      :placeholder="$t('XML协议文件')"
                      :items="protocolFileData.xml"
                      variant="outlined"
                      class="w-100 mr-4 mt-2"
                      hide-details
                      :rules="protocolRules.read_file"
                    ></v-select>
                  </v-col>
                  <v-col cols="12">
                    <v-select
                      v-model="protocolForm.control_file"
                      item-value="value"
                      clearable
                      :label="$t('JSON控制文件')"
                      :placeholder="$t('JSON控制文件')"
                      :items="protocolFileData.json"
                      variant="outlined"
                      class="w-100 mr-4 mt-2"
                      hide-details
                    ></v-select>
                  </v-col>
                </template>
                <template v-else>
                  <v-col cols="12">
                    <v-select
                      v-model="protocolForm.use_config_file"
                      item-value="value"
                      clearable
                      :label="$t('故障录波JSON文件')"
                      :placeholder="$t('故障录波JSON文件')"
                      :items="protocolFileData.json"
                      variant="outlined"
                      class="w-100 mr-4 mt-2"
                      hide-details
                      :rules="protocolRules.use_config_file"
                    ></v-select>
                  </v-col>
                </template>
              </v-row>
              <div class="d-flex justify-center mt-6">
                <v-btn
                  class="mt-2 mr-4 px-8"
                  height="50"
                  @click="handleProtocolCancelClick"
                  >{{ $t('取消') }}</v-btn
                >
                <v-btn
                  class="mt-2 px-8"
                  type="submit"
                  height="50"
                  :loading="protocolLoading"
                  color="primary"
                  >{{ $t('确定') }}</v-btn
                >
              </div>
            </v-form>
          </v-sheet>
        </v-card>
      </v-dialog>
      <v-dialog v-model="useDialog" width="auto">
        <v-card width="540" class="pa-4 rounded-lg">
          <v-card-title>{{ $t('系统提示') }}</v-card-title>
          <v-card-text>{{ $t('是否应用？') }}</v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="grey-darken-1"
              variant="text"
              @click="handleUseCancelClick"
            >
              {{ $t('取消') }}
            </v-btn>
            <v-btn color="primary" variant="text" @click="handleUseConfirm">
              {{ $t('确定') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
      <v-dialog v-model="pwdDialog" width="auto">
        <v-card width="540" class="pa-4 rounded-lg">
          <v-card-title>{{ $t('系统提示') }}</v-card-title>
          <v-card-text class="my-0">
            <div class="mb-2">{{ $t('请输入密码') }}</div>
            <v-text-field
              v-model="password"
              :rules="[(v) => !!v || $t('密码必填')]"
              variant="outlined"
              class="mb-2"
              :type="eye ? 'text' : 'password'"
              :append-inner-icon="eye ? 'mdi-eye' : 'mdi-eye-closed'"
              @click:appendInner.stop="handleEyeClick()"
              @click:control="
                handleShow($event, password, { type: 1, prop: 'password' })
              "
              hide-details
            ></v-text-field>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="grey-darken-1"
              variant="text"
              @click="handlePwdCancelClick"
            >
              {{ $t('取消') }}
            </v-btn>
            <v-btn color="primary" variant="text" @click="handlePwdConfirm">
              {{ $t('确定') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-card>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-table) {
  --el-table-border-color: rgba(118, 118, 118, 0.6);
  --el-table-border: 1px solid rgba(118, 118, 118, 0.6);
  --el-table-text-color: #000;
  --el-table-header-text-color: #000;
  border-radius: 2px;
  .cell {
    font-weight: 400;
  }
  .el-table__empty-text {
    color: #000;
  }
}
</style>
