{"name": "ems_manage", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@vueuse/core": "^11.1.0", "@xterm/addon-fit": "^0.10.0", "@xterm/xterm": "^5.5.0", "autofit.js": "^3.1.3", "axios": "^1.7.2", "dayjs": "^1.11.12", "echarts": "5.4.3", "element-plus": "^2.8.5", "interactjs": "^1.10.27", "maotu": "^0.3.6-beta.3", "mathjs": "^14.2.1", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^4.0.1", "rollup-plugin-visualizer": "^5.12.0", "vkbeautify": "^0.99.3", "vue": "^3.4.29", "vue-i18n": "^9.13.1", "vue-router": "^4.4.0", "vue3-ace-editor": "^2.2.4", "vuedraggable": "^4.1.0", "vuetify": "^3.9.0"}, "devDependencies": {"@element-plus/nuxt": "^1.0.10", "@mdi/font": "^7.4.47", "@vitejs/plugin-legacy": "^5.4.1", "@vitejs/plugin-vue": "^5.0.5", "@vue/runtime-core": "^3.4.38", "sass": "^1.77.7", "terser": "^5.31.6", "unocss": "^0.61.9", "unplugin-element-plus": "^0.8.0", "vite": "^5.3.1", "vite-plugin-cdn-import": "^1.0.1", "vite-plugin-compression": "^0.5.1"}}