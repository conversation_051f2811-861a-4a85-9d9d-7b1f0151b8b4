<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-27 15:33:04
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-04-23 17:59:43
 * @FilePath: \ems_manage\src\views\Dashboard\coms\info-card.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { toRefs, ref, computed, watch } from 'vue'
import { useDeviceStore } from '@/store/module/device'
import { useConfigStore } from '@/store/module/config'
import { useI18n } from 'vue-i18n'

const { treeData, pageInfo, currentAlarmCount } = toRefs(useDeviceStore())
const loading = ref(false)
const { systemStatus } = toRefs(useConfigStore())
const { t } = useI18n()

const getSystemStatusCom = computed(() => {
  let data = {
    text: '--',
    color: '#333'
  }
  let status = systemStatus.value
  if (status == '0') {
    data = {
      text: t('系统关闭'),
      color: 'rgb(var(--v-theme-error))'
    }
  } else if (status == '1') {
    data = {
      text: t('系统开启'),
      color: 'rgb(var(--v-theme-success))'
    }
  } else if (status == '2') {
    data = {
      text: t('策略停止'),
      color: 'rgb(var(--v-theme-error))'
    }
  } else if (status == '3') {
    data = {
      text: t('策略运行'),
      color: 'rgb(var(--v-theme-success))'
    }
  } else {
    data = {
      text: '--',
      color: '#333'
    }
  }

  return data
})
const statusObj = ref({
  text: '--',
  color: '#333'
})
const setStatusFn = () => {
  let status = systemStatus.value
  if (status == '0') {
    statusObj.value = {
      text: t('系统关闭'),
      color: 'rgb(var(--v-theme-error))'
    }
  } else if (status == '1') {
    statusObj.value = {
      text: t('系统开启'),
      color: 'rgb(var(--v-theme-success))'
    }
  } else if (status == '2') {
    statusObj.value = {
      text: t('策略停止'),
      color: 'rgb(var(--v-theme-error))'
    }
  } else if (status == '3') {
    statusObj.value = {
      text: t('策略运行'),
      color: 'rgb(var(--v-theme-success))'
    }
  } else {
    statusObj.value = {
      text: '--',
      color: '#333'
    }
  }
}
setStatusFn()
watch(
  () => systemStatus.value,
  () => {
    setStatusFn()
  }
)
</script>

<template>
  <v-card
    height="170px"
    elevation="4"
    class="rounded-lg"
    :disabled="loading"
    :loading="loading"
  >
    <template v-slot:loader="{ isActive }">
      <v-progress-linear
        :active="isActive"
        color="primary"
        height="4"
        indeterminate
      ></v-progress-linear>
    </template>
    <div class="d-flex flex-no-wrap justify-space-between h-full">
      <div>
        <v-card-title> {{ $t('设备概括') }} </v-card-title>
        <div class="text-subtitle-2 ml-4 mt-2">
          {{ $t('设备总数') }}：<span class="font-600"
            >{{ treeData.length }} {{ $t('个') }}</span
          >
        </div>
        <div class="text-subtitle-2 ml-4 mt-4 mb-4">
          {{ $t('系统状态') }}：
          <span
            class="font-600"
            :style="{
              color: getSystemStatusCom?.color
                ? getSystemStatusCom?.color
                : '#333'
            }"
            >{{
              getSystemStatusCom?.text ? getSystemStatusCom?.text : '--'
            }}</span
          >
        </div>
        <div class="text-subtitle-2 ml-4 mt-5px">
          {{ $t('告警总数') }}：<span class="font-600"
            >{{ currentAlarmCount }} {{ $t('条') }}</span
          >
        </div>
      </div>

      <div class="d-flex align-center mr-4">
        <img
          src="../../../assets/img/run.webp"
          style="width: 120px; height: 120px"
        />
      </div>
    </div>
  </v-card>
</template>

<style lang="scss" scoped></style>
