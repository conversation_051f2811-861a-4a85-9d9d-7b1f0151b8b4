<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-29 14:53:50
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-03-10 15:16:52
 * @FilePath: \ems_manage\src\views\Param\strategy.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import {
  ref,
  toRefs,
  getCurrentInstance,
  onMounted,
  nextTick,
  computed
} from 'vue'
import { useGlobalStore } from '@/store/global'
import { useParamStore } from '@/store/module/param'
import { useI18n } from 'vue-i18n'
import { cloneDeep } from 'lodash-es'
import { generateUUID, setLeftWidth } from '@/utils'

import Line from './line.vue'
import { ElTimePicker } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: String
  }
})
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { strategyData } = toRefs(useParamStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  keyboardRange,
  isShowKeyboard
} = toRefs(useGlobalStore())
const getData = () => {
  useParamStore()
    .getStrategyDataFn(JSON.stringify({ name: props.modelValue }))
    .then((res) => {
      sendForm.value = strategyData.value
      nextTick(() => {
        setLeftWidth('left')
      })
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
getData()

const dialog = ref(false)
const dialogTitle = ref(t('添加方案'))
const form = ref({
  template_id: undefined,
  template_name: undefined,
  time_slots: [
    {
      start_time: '',
      end_time: '',
      power_set: '',
      enable: '1',
      switchType: '2'
    }
  ]
})
const loading = ref(false)
const submit = async () => {
  const { valid } = await proxy.$refs.addForm.validate()
  if (!valid) return
  if (dialogTitle.value == t('添加方案')) {
    sendForm.value.template_config.templates.push(form.value)
  } else {
    sendForm.value.template_config.templates[operTemplateIndex.value] =
      form.value
  }
  loading.value = true
  try {
    const res = await useParamStore().updateStrategyConfigFn(
      JSON.stringify({
        name: props.modelValue,
        config: sendForm.value
      })
    )
    snackbar.value = true
    snackbarText.value =
      dialogTitle.value == t('添加方案') ? t('添加成功') : t('修改成功')
    loading.value = false
    dialog.value = false
    getData()
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
    dialog.value = false
  }
}
const handleDeleteClick = (item, index) => {
  operTemplateIndex.value = index
  sendForm.value.template_config.templates.splice(index, 1)
  useParamStore()
    .updateStrategyConfigFn(
      JSON.stringify({
        name: props.modelValue,
        config: sendForm.value
      })
    )
    .then((res) => {
      snackbar.value = true
      snackbarText.value = t('删除成功')
      getData()
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
const handleCancelClick = () => {
  loading.value = false
  dialog.value = false
}
const handleAddClick = () => {
  form.value = {
    template_name: undefined,
    time_slots: [
      {
        start_time: '',
        end_time: '',
        power_set: '',
        enable: '1',
        switchType: '2'
      }
    ],
    template_id: generateUUID()
  }
  dialogTitle.value = t('添加方案')
  dialog.value = true
  nextTick(() => {
    setLeftWidth('dia-left')
  })
}
const operTemplateIndex = ref()
const handleEditClick = (item, index) => {
  operTemplateIndex.value = index
  form.value = {
    ...item,
    time_slots: cloneDeep(item.time_slots)
  }
  dialogTitle.value = t('修改方案')
  dialog.value = true
  nextTick(() => {
    setLeftWidth('dia-left')
  })
}
const handleDeleteTimeClick = (index) => {
  if (form.value.time_slots.length == 1) {
    snackbar.value = true
    snackbarText.value = t('至少要有一条哦')
    return
  }
  form.value.time_slots.splice(index, 1)
}
const handleAddTimeClick = () => {
  if (form.value.time_slots.length == 12) {
    snackbar.value = true
    snackbarText.value = t('最多只能添加12条哦')
    return
  }
  form.value.time_slots.push({
    enable: '1',
    end_time: '',
    power_set: '',
    start_time: '',
    switchType: '2'
  })
  nextTick(() => {
    setLeftWidth('dia-left')
  })
}

/**
 * 下发策略
 */
const sendForm = ref({
  name: undefined,
  enable: '1',
  type: 'daily',
  peakcut_config: {
    monthly: {
      month_1: undefined,
      month_2: undefined,
      month_3: undefined,
      month_4: undefined,
      month_5: undefined,
      month_6: undefined,
      month_7: undefined,
      month_8: undefined,
      month_9: undefined,
      month_10: undefined,
      month_11: undefined,
      month_12: undefined
    },
    weekly: {
      week_1: undefined,
      week_2: undefined,
      week_3: undefined,
      week_4: undefined,
      week_5: undefined,
      week_6: undefined,
      week_7: undefined
    },
    daily: undefined
  },
  template_config: {
    templates: [
      {
        template_id: undefined,
        template_name: undefined,
        time_slots: [
          {
            enable: '1',
            start_time: '',
            end_time: '',
            power_set: '',
            switchType: '2'
          }
        ]
      }
    ]
  }
})
const sendLoading = ref(false)
const sendSubmit = async () => {
  const { valid } = await proxy.$refs.sendFormRef.validate()
  if (!valid) return
  sendLoading.value = true
  try {
    const res = await useParamStore().updateStrategyConfigFn(
      JSON.stringify({
        name: props.modelValue,
        config: sendForm.value
      })
    )
    snackbar.value = true
    snackbarText.value = t('执行成功')
    sendLoading.value = false
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    sendLoading.value = false
  }
}
const weeks = ref([
  t('星期一'),
  t('星期二'),
  t('星期三'),
  t('星期四'),
  t('星期五'),
  t('星期六'),
  t('星期日')
])
const modeOptions = ref([
  {
    id: 'peakcut',
    title: t('削峰填谷')
  }
])

onMounted(() => {
  setLeftWidth('left')
})

/**
 * 时间限制
 */
const makeRange = (start, end) => {
  const result = []
  for (let i = start; i <= end; i++) {
    result.push(i)
  }
  return result
}
const disabledHours = (index, type) => {
  return () => {
    if (index == 0) {
      if (type == 'startTime') {
        return []
      } else {
        let startHour = form.value.time_slots[index]?.start_time.split(':')[0]
        let startTime = form.value.time_slots[index]?.start_time.split(':')[1]
        let start = 0
        if (startTime == 59) start = startHour
        else start = startHour - 1
        return makeRange(0, start)
      }
    } else {
      if (type == 'startTime') {
        let startHour = form.value.time_slots[index - 1]?.end_time.split(':')[0]
        let startTime = form.value.time_slots[index - 1]?.end_time.split(':')[1]
        let start = 0
        if (startTime == 59) start = startHour
        else start = startHour - 1
        return makeRange(0, start)
      } else {
        let startHour = form.value.time_slots[index]?.start_time.split(':')[0]
        let startTime = form.value.time_slots[index]?.start_time.split(':')[1]
        let start = 0
        if (startTime == 59) start = startHour
        else start = startHour - 1
        return makeRange(0, start)
      }
    }
  }
}
const disabledMinutes = (index, type) => {
  return () => {
    if (index == 0) {
      if (type == 'startTime') {
        return []
      } else {
        let startTime = form.value.time_slots[index]?.start_time.split(':')[1]
        if (startTime == 59) return []
        else return makeRange(0, startTime)
      }
    } else {
      if (type == 'startTime') {
        let startTime = form.value.time_slots[index - 1]?.end_time.split(':')[1]
        if (startTime == 59) return []
        else return makeRange(0, startTime)
      } else {
        let startTime = form.value.time_slots[index]?.start_time.split(':')[1]
        if (startTime == 59) return []
        else return makeRange(0, startTime)
      }
    }
  }
}

/**
 * 键盘
 */
const handleShow = (e, value, prop, mode) => {
  if (isShowKeyboard.value) return
  keyboardMode.value = mode
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  let props = currentInput.value.split('.')
  if (props.length == 1) {
    form.value[currentInput.value] = keyboardInputValue.value
  } else {
    form.value.time_slots[props[0]][props[1]] = keyboardInputValue.value
  }
  keyboardDialog.value = false
}
</script>

<template>
  <v-form fast-fail @submit.prevent="sendSubmit" ref="sendFormRef" class="mt-6">
    <div class="flex">
      <div
        class="text-body-1 pb-34px pt-3 pr-4 flex justify-end align-center left"
      >
        {{ $t('策略模式') }}
      </div>
      <v-col cols="5" class="pl-0 pr-2">
        <v-select
          v-model="sendForm.name"
          clearable
          item-value="id"
          :items="modeOptions"
          variant="outlined"
          class="w-100 mr-4"
        ></v-select>
      </v-col>
    </div>
    <div class="flex">
      <div
        class="text-body-1 pb-34px pt-3 pr-4 flex justify-end align-center left"
      >
        {{ $t('策略方式') }}
      </div>
      <v-col cols="5" class="pl-0 pr-2">
        <v-radio-group inline v-model="sendForm.type">
          <v-radio :label="$t('月')" value="monthly"></v-radio>
          <v-radio :label="$t('周')" value="weekly" class="mx-4"></v-radio>
          <v-radio :label="$t('日')" value="daily"></v-radio>
        </v-radio-group>
      </v-col>
    </div>
    <div class="flex">
      <div
        class="text-body-1 pb-34px pt-3 pr-4 left"
        :class="[sendForm.type == 'daily' && 'flex align-center justify-end']"
      >
        {{ $t('策略配置') }}
      </div>
      <v-col
        cols="10"
        class="pl-0 pr-2 flex flex-wrap"
        v-if="sendForm.type == 'monthly'"
      >
        <div class="flex py-0 w-100 flex-wrap">
          <div
            class="flex flex-column mr-8 mb-4"
            v-for="item in 12"
            :key="item"
            style="width: 160px"
          >
            <div class="week w-100">{{ $t(`${item}月`) }}</div>
            <div class="week-data w-100 flex justify-center align-center pa-2">
              <v-select
                v-model="sendForm.peakcut_config.monthly[`month_${item}`]"
                item-value="template_id"
                item-title="template_name"
                clearable
                :items="sendForm.template_config.templates"
                variant="outlined"
                class="w-100 week-select"
              ></v-select>
            </div>
          </div>
        </div>
      </v-col>
      <v-col
        cols="10"
        class="pl-0 pr-2 flex flex-wrap"
        v-if="sendForm.type == 'weekly'"
      >
        <div class="flex py-0 w-100 flex-wrap">
          <div
            class="flex flex-column mr-6 mb-4"
            v-for="(item, index) in weeks"
            :key="item"
            style="width: 160px"
          >
            <div class="week w-100">
              {{ item }}
            </div>
            <div class="week-data w-100 flex justify-center align-center pa-2">
              <v-select
                v-model="sendForm.peakcut_config.weekly[`week_${index + 1}`]"
                item-value="template_id"
                item-title="template_name"
                clearable
                :items="sendForm.template_config.templates"
                variant="outlined"
                class="w-100 week-select"
              ></v-select>
            </div>
          </div>
        </div>
      </v-col>
      <v-col cols="5" class="pl-0 pr-2" v-if="sendForm.type == 'daily'">
        <v-select
          v-model="sendForm.peakcut_config.daily"
          item-value="template_id"
          item-title="template_name"
          clearable
          :items="sendForm.template_config.templates"
          variant="outlined"
          class="w-100 mr-4"
        ></v-select>
      </v-col>
    </div>
    <div class="flex">
      <div
        class="text-body-1 pb-34px pt-3 pr-4 flex justify-end align-center left"
      >
        {{ $t('是否启用策略') }}
      </div>
      <v-col cols="5" class="pl-0 pr-2">
        <v-radio-group inline v-model="sendForm.enable">
          <v-radio :label="$t('启用')" value="1"></v-radio>
          <v-radio :label="$t('停止')" value="0" class="mx-4"></v-radio>
        </v-radio-group>
      </v-col>
    </div>
    <div class="flex">
      <div class="left"></div>
      <v-btn
        class="mt-2 px-8"
        type="submit"
        height="50"
        :loading="sendLoading"
        color="primary"
        >{{ $t('确定') }}</v-btn
      >
    </div>
  </v-form>
  <v-divider class="my-6" color="secondary"></v-divider>
  <div class="d-flex justify-between align-center">
    <div class="text-h6">{{ $t('策略方案') }}</div>
  </div>
  <div class="flex mt-6">
    <div
      class="text-body-1 pb-34px pt-3 pr-4 flex justify-end align-center left"
    >
      {{ $t('模式选择') }}
    </div>
    <v-col cols="5" class="pl-0 pr-2">
      <v-select
        v-model="sendForm.name"
        clearable
        item-value="id"
        :items="modeOptions"
        variant="outlined"
        class="w-100 mr-4"
      ></v-select>
    </v-col>
  </div>
  <div class="flex">
    <div class="text-body-1 pb-34px pt-3 pr-4 left">
      {{ $t('方案列表') }}
    </div>
    <v-col cols="10" class="pl-0 pr-2 flex flex-wrap">
      <v-hover
        v-for="(item, index) in sendForm.template_config.templates"
        :key="item.template_id"
      >
        <template v-slot:default="{ isHovering, props }">
          <v-card
            height="300px"
            class="rounded-lg mr-4 mb-4"
            variant="outlined"
            v-bind="props"
            :elevation="isHovering ? 4 : 0"
            style="width: 300px"
          >
            <template #title>
              <div class="flex w-100 justify-space-between align-center">
                <div class="text-body-1 flex align-center">
                  <img
                    src="../../assets/img/jfpg.png"
                    style="width: 20px"
                    class="mr-2"
                  />
                  <span>{{ item.template_name }}</span>
                </div>
                <div>
                  <v-tooltip text="" location="top">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        icon="mdi-pencil"
                        variant="plain"
                        v-bind="props"
                        @click="handleEditClick(item, index)"
                      ></v-btn>
                    </template>
                  </v-tooltip>
                  <v-tooltip :text="$t('删除')" location="top">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        icon="mdi-delete"
                        variant="plain"
                        v-bind="props"
                        @click="handleDeleteClick(item, index)"
                      ></v-btn>
                    </template>
                  </v-tooltip>
                </div>
              </div>
            </template>
            <v-divider></v-divider>
            <div class="flex align-center justify-center">
              <div
                style="
                  border-radius: 50%;
                  width: 12px;
                  height: 12px;
                  background-color: #93ce07;
                "
                class="mr-1"
              ></div>
              <span style="font-size: 14px" class="mr-2">{{ $t('放电') }}</span>
              <div
                style="
                  border-radius: 50%;
                  width: 12px;
                  height: 12px;
                  background-color: #fbdb0f;
                "
                class="mr-1"
              ></div>
              <span style="font-size: 14px">{{ $t('充电') }}</span>
            </div>
            <Line
              :data="item.time_slots"
              style="width: 100%; height: calc(100% - 68px - 12px)"
              class="pl-2"
            />
          </v-card> </template
      ></v-hover>
      <v-hover>
        <template v-slot:default="{ isHovering, props }">
          <v-card
            height="300px"
            class="rounded-lg"
            variant="outlined"
            v-bind="props"
            :elevation="isHovering ? 4 : 0"
            @click="handleAddClick"
            style="width: 300px"
          >
            <v-tooltip :text="$t('添加')" location="top">
              <template v-slot:activator="{ props }">
                <v-btn
                  icon="mdi-plus"
                  variant="plain"
                  v-bind="props"
                  size="60"
                  class="h-100 w-100 plus"
                ></v-btn>
              </template>
            </v-tooltip>
          </v-card> </template
      ></v-hover>
    </v-col>
  </div>

  <v-dialog v-model="dialog" width="auto">
    <v-card width="740" class="pa-4 rounded-lg">
      <v-card-title class="text-center mb-6">{{ dialogTitle }}</v-card-title>
      <v-sheet class="mx-auto w-full">
        <v-form fast-fail @submit.prevent="submit" ref="addForm">
          <div class="flex">
            <div
              class="text-body-1 pb-34px pt-3 pr-4 flex justify-end align-center dia-left"
            >
              {{ $t('方案名称') }}
            </div>
            <v-col cols="10" class="pl-0 pr-2">
              <v-text-field
                v-model="form.template_name"
                :rules="[(v) => !!v || $t('方案名称必填')]"
                variant="outlined"
                :placeholder="$t('方案名称')"
                @click:control="
                  handleShow($event, form.template_name, 'template_name', 'en')
                "
              ></v-text-field>
            </v-col>
          </div>

          <template v-for="(item, index) in form.time_slots" :key="index">
            <div class="flex">
              <div
                cols="2"
                class="text-body-1 pb-34px pt-3 pr-4 flex justify-end align-center dia-left"
              >
                {{ $t('时段') }}{{ index + 1 }}
              </div>
              <v-col cols="10" class="flex pl-0 pr-2">
                <v-col cols="2.5" class="px-0">
                  <el-time-picker
                    v-model="item.start_time"
                    format="HH:mm"
                    value-format="HH:mm"
                    :disabled-hours="disabledHours(index, 'startTime')"
                    :clearable="false"
                    :placeholder="$t('开始时间')"
                    class="w-100"
                  />
                </v-col>
                <v-col cols="2.5" class="px-0 mx-2">
                  <el-time-picker
                    v-model="item.end_time"
                    format="HH:mm"
                    value-format="HH:mm"
                    :disabled-hours="disabledHours(index, 'endTime')"
                    :clearable="false"
                    :placeholder="$t('结束时间')"
                    class="w-100"
                  />
                </v-col>
                <v-col cols="2.5" class="px-0">
                  <v-text-field
                    v-model="item.power_set"
                    :rules="[(v) => !!v || $t('功率必填')]"
                    variant="outlined"
                    :placeholder="$t('功率')"
                    @click:control="
                      handleShow(
                        $event,
                        item.power_set,
                        `${index}.power_set`,
                        'di_git'
                      )
                    "
                  >
                    <template #append-inner> kW </template>
                  </v-text-field>
                </v-col>
                <v-col cols="2.5" class="px-0">
                  <v-select
                    v-model="item.switchType"
                    item-value="id"
                    :placeholder="$t('设置')"
                    :items="[
                      {
                        id: '0',
                        title: $t('待机')
                      },
                      {
                        id: '1',
                        title: $t('开机')
                      },
                      {
                        id: '2',
                        title: $t('关机')
                      }
                    ]"
                    variant="outlined"
                  ></v-select>
                </v-col>
                <v-col cols="2.5" class="px-0 pl-2">
                  <v-switch
                    v-model="item.enable"
                    :label="item.enable == '0' ? $t('不使能') : $t('使能')"
                    false-value="0"
                    true-value="1"
                    hide-details
                    color="primary"
                  />
                </v-col>
                <v-col
                  cols="1"
                  class="px-0 pb-34px flex align-center justify-start"
                >
                  <v-tooltip :text="$t('添加')" location="top">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        icon="mdi-plus-circle"
                        variant="plain"
                        v-bind="props"
                        style="width: 24px; height: 24px"
                        @click="handleAddTimeClick"
                      ></v-btn>
                    </template>
                  </v-tooltip>
                  <v-tooltip :text="$t('删除')" location="top">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        icon="mdi-close-circle"
                        variant="plain"
                        v-bind="props"
                        style="width: 24px; height: 24px"
                        @click="handleDeleteTimeClick(index)"
                      ></v-btn>
                    </template>
                  </v-tooltip>
                </v-col>
              </v-col>
            </div>
          </template>
          <div class="d-flex justify-center">
            <v-btn
              class="mt-2 mr-4 px-8"
              height="50"
              @click="handleCancelClick"
              >{{ $t('取消') }}</v-btn
            >
            <v-btn
              class="mt-2 px-8"
              type="submit"
              height="50"
              :loading="loading"
              color="primary"
              >{{ $t('确定') }}</v-btn
            >
          </div>
        </v-form>
      </v-sheet>
    </v-card>
  </v-dialog>
</template>

<style lang="scss" scoped>
:deep(.plus) {
  border-radius: 0 !important;
  .v-icon {
    font-size: 50px;
  }
}
:deep(.v-card--variant-outlined) {
  border: thin solid #ccc;
}
/* :deep(.v-divider) {
  border: 1 solid #ccc !important;
} */
:deep(.time) {
  .v-field__input {
    justify-content: center !important;
  }
}
:deep(.week-select) {
  .v-input__details {
    display: none;
  }
}
.week {
  height: 42px;
  background-color: #0093b6;
  text-align: center;
  line-height: 42px;
  color: #fff;
}
.week-data {
  border: 1px solid #ccc;
  border-top: none;
}
:deep(.v-table) {
  table {
    border-collapse: collapse;
  }
  td,
  th {
    border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  }
}
.left {
  flex: 0 0 auto; /* 不可拉伸，宽度不变 */
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容显示出来 */
  text-align: right;
}
.dia-left {
  flex: 0 0 auto; /* 不可拉伸，宽度不变 */
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容显示出来 */
  text-align: right;
}
:deep(.el-input__wrapper) {
  height: auto;
  padding: 16px;
  box-shadow: 0 0 0 1px rgba(118, 118, 118, 0.6);
  .el-input__prefix {
    color: #000;
  }
  .el-input__inner {
    color: #000;
    height: 26px;
    font-size: 16px;
  }
}
:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 1px #000;
}
:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 1px #000;
}
:deep(.el-date-editor.el-input, .el-date-editor.el-input__wrapper) {
  height: auto;
}
</style>
