<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-11 11:31:31
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  outline: String, // 背景颜色 #fff
  fill: String, // #C4EBAD
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width="16"
    height="16"
    viewBox="0 0 16 16"
  >
    <defs>
      <clipPath id="master_svg0_278_5283">
        <rect x="0" y="0" width="16" height="16" rx="0" />
      </clipPath>
    </defs>
    <g clip-path="url(#master_svg0_278_5283)">
      <g>
        <path
          d="M13.65596748046875,3.27204703125L2.34399748046875,3.27204703125C1.41200048046875,3.27204703125,0.64666748046875,4.03604703125,0.64666748046875,4.96937703125L0.64666748046875,12.88800703125C0.64666748046875,13.82000703125,1.41066748046875,14.58540703125,2.34399748046875,14.58540703125L13.65596748046875,14.58540703125C14.58796748046875,14.58540703125,15.35336748046875,13.82140703125,15.35336748046875,12.88800703125L15.35336748046875,4.96804703125C15.35336748046875,4.03604703125,14.58936748046875,3.27204703125,13.65596748046875,3.27204703125ZM2.91066748046875,1.57470881939L5.17333748046875,1.57470881939C5.48533748046875,1.57470881939,5.73866748046875,1.8280420312499999,5.73866748046875,2.14004203125C5.73866748046875,2.45204203125,5.48533748046875,2.7053770312500003,5.17333748046875,2.7053770312500003L2.91066748046875,2.7053770312500003C2.59866748046875,2.7053770312500003,2.34533748046875,2.45204203125,2.34533748046875,2.14004203125C2.34459748046875,1.82751103125,2.59813748046875,1.57396900625,2.91066748046875,1.57470881939ZM10.82666748046875,1.57470881939L13.08936748046875,1.57470881939C13.40136748046875,1.57470881939,13.65466748046875,1.8280420312499999,13.65466748046875,2.14004203125C13.65466748046875,2.45204203125,13.40136748046875,2.7053770312500003,13.08936748046875,2.7053770312500003L10.82666748046875,2.7053770312500003C10.51466748046875,2.7053770312500003,10.26133748046875,2.45204203125,10.26133748046875,2.14004203125C10.26266748046875,1.8280420312499999,10.51599748046875,1.57470870018,10.82666748046875,1.57470881939Z"
          :fill="fill"
          fill-opacity="1"
          style="mix-blend-mode: passthrough"
        />
      </g>
      <g>
        <path
          d="M2.91064453125,8.361283203125L7.43464453125,8.361283203125L7.43464453125,9.491953203125L2.91064453125,9.491953203125L2.91064453125,8.361283203125ZM11.39331453125,6.665283203125L10.26264453125,6.665283203125L10.26264453125,8.361283203125L8.566644531249999,8.361283203125L8.566644531249999,9.491953203125L10.26264453125,9.491953203125L10.26264453125,11.189283203125001L11.39331453125,11.189283203125001L11.39331453125,9.493283203125L13.09064453125,9.493283203125L13.09064453125,8.362613203125001L11.39331453125,8.362613203125001L11.39331453125,6.665283203125Z"
          :fill="outline"
          fill-opacity="1"
          style="mix-blend-mode: passthrough"
        />
      </g>
    </g>
  </svg>
</template>
