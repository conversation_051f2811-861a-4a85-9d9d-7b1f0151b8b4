<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-06 14:45:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-29 12:27:44
 * @FilePath: \ems_manage\src\views\Dashboard\lineEchart.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { computed, toRefs } from 'vue'
import dayjs from '@/utils/date'
import { useStatisticsStore } from '@/store/module/statistics'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const props = defineProps({
  type: Number
})

const { powerData, powerQueryInfo } = toRefs(useStatisticsStore())

// const generateTimes = () => {
//   const times = []
//   const datas1 = []
//   const datas2 = []
//   let current = dayjs().startOf('day')

//   while (current.isBefore(dayjs().endOf('day'))) {
//     times.push(current.format('HH') + '时')
//     datas1.push(Math.round(Math.random() * 100))
//     datas2.push(Math.round(Math.random() * 100))
//     current = current.add(1, 'hour')
//   }

//   return {
//     time: times,
//     data1: datas1,
//     data2: datas2
//   }
// }

const options = computed(() => {
  const set = new Set()
  let times = []
  if (props.type == 0) {
    let current = dayjs().startOf('day')
    while (current.isBefore(dayjs().endOf('day'))) {
      set.add(t(current.format('HH') + '时'))
      current = current.add(1, 'hour')
    }
    times = [...set]
  } else if (props.type == 1) {
    let current = dayjs(powerQueryInfo.value.date).startOf('month')
    while (current.isBefore(dayjs().endOf('month'))) {
      set.add(t(current.format('DD') + '日'))
      current = current.add(1, 'day')
    }
    times = [...set]
  } else if (props.type == 2) {
    times = [
      t('1月'),
      t('2月'),
      t('3月'),
      t('4月'),
      t('5月'),
      t('6月'),
      t('7月'),
      t('8月'),
      t('9月'),
      t('10月'),
      t('11月'),
      t('12月')
    ]
  } else if (props.type == 3) {
    let current = dayjs().startOf('day')
    while (current.isBefore(dayjs().endOf('day'))) {
      set.add(current.format('HH:mm'))
      current = current.add(1, 'minute')
    }
    times = [...set]
  }
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      type: 'plain',
      textStyle: {
        fontSize: 12,
        fontWeight: 400
        // color: 'rgba(0, 0, 0, .9)',
        // lineHeight: 18
      },
      itemGap: 30,
      top: 5
    },
    grid: {
      left: '3%',
      right: '2%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      axisLine: {
        lineStyle: {
          // color: '#fff'
        }
      },
      axisTick: {
        //y轴刻度线
        show: true
      },
      splitLine: {
        //分割线
        show: false, //控制分割线是否显示
        lineStyle: {
          //分割线的样式
          color: 'rgba(81, 82, 85, 0.3)',
          width: 1,
          type: 'solid'
        }
      },
      data: times
    },
    yAxis: {
      type: 'value',
      name: `${t('单位')}：kWh`,
      axisLine: {
        lineStyle: {
          // color: '#fff'
        }
      },
      splitLine: {
        //分割线
        show: false, //控制分割线是否显示
        lineStyle: {
          //分割线的样式
          color: 'rgba(81, 82, 85, 0.3)',
          width: 1,
          type: 'solid'
        }
      }
    },
    dataZoom: {
      type: 'inside'
    },
    series: [
      {
        name: t('放电量'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 15,
        data: powerData.value?.chartData?.p,
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('充电量'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 15,
        color: 'rgba(196, 235, 173, 1)',
        data: powerData.value?.chartData?.n,
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      }
    ]
  }
})
</script>

<template>
  <div>
    <BaseEchart
      width="100%"
      height="100%"
      :options="options"
      ref="myChart"
    ></BaseEchart>
  </div>
</template>

<style lang="scss" scoped></style>
