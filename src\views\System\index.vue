<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-04-24 12:13:53
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-08-27 11:12:03
 * @FilePath: \ems_manage\src\views\System\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, computed, toRefs, getCurrentInstance } from 'vue'
import { useDisplay, useLocale } from 'vuetify'
import { useUserStore } from '@/store/module/user'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useGlobalStore } from '@/store/global'
import { useConfigStore } from '@/store/module/config'
import { initLocale } from '@/locale'
import { setLanguageNo, setLanguage } from '@/api/global'

import { LangSelectVersion } from '@/components/lang-select'

const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard,
  langFileNameArr
} = toRefs(useGlobalStore())
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const router = useRouter()
const { userInfo, menuInfo } = toRefs(useUserStore())
const { version, cycleTime, timeZoneList, localTimeZone } = toRefs(
  useConfigStore()
)

useConfigStore()
  .getVersionInfoFn()
  .catch((err) => {
    snackbar.value = true
    snackbarText.value = err
  })

/**
 * 键盘
 */
const handleShowKeyboard = () => {
  if (!isShowKeyboard.value) {
    snackbarText.value = t('键盘已开启')
  } else {
    snackbarText.value = t('键盘已关闭')
  }
  snackbar.value = true
}
keyboardMode.value = 'di_git'
const handleShow = (e, value, prop) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  cycleTime.value = keyboardInputValue.value
  keyboardDialog.value = false
}

/**
 * 时区
 */
const timeZone = ref()
const timeZoneLoading = ref(false)
const getTime = () => {
  useConfigStore()
    .getTimeZoneListFn()
    .then(() => {
      timeZone.value = localTimeZone.value
    })
}
getTime()
const handleTimeConfirm = () => {
  timeZoneLoading.value = true
  useConfigStore()
    .setTimeZoneFn({
      timezone: timeZone.value
    })
    .then(async () => {
      snackbar.value = true
      snackbarText.value = t('设置成功')
      await useConfigStore().getLocalTimeFn()
      timeZone.value = localTimeZone.value
      timeZoneLoading.value = false
    })
    .catch((err) => {
      snackbar.value = true
      snackbarText.value = err
      timeZoneLoading.value = false
    })
}

/**
 * 语言文件导入
 */
const languageDialog = ref(false)
const languageFileName = ref('')
const languageContent = ref('')
const importLoading = ref(false)
const overwriteMode = ref(false) // false: 不覆盖, true: 覆盖

// 打开语言文件导入对话框
const openLanguageDialog = () => {
  languageDialog.value = true
  languageFileName.value = ''
  languageContent.value = ''
  overwriteMode.value = false
}

// 关闭语言文件导入对话框
const closeLanguageDialog = () => {
  languageDialog.value = false
  languageFileName.value = ''
  languageContent.value = ''
  overwriteMode.value = false
}

const handleLanguageImport = async () => {
  if (!languageContent.value) {
    snackbar.value = true
    snackbarText.value = t('请输入语言文件内容')
    return
  }

  importLoading.value = true

  let formData = new FormData()
  formData.append('languageFileName', languageFileName.value.trim())
  formData.append('languageContent', languageContent.value)

  try {
    if (overwriteMode.value) {
      // 覆盖模式 - 使用 PUT 方法
      await setLanguage(formData)
    } else {
      // 不覆盖模式 - 使用 POST 方法
      await setLanguageNo(formData)
    }

    snackbar.value = true
    snackbarText.value = t('语言文件导入成功')
    useGlobalStore().getLanguageFn(languageFileName.value)
    closeLanguageDialog()
  } catch (err) {
    snackbar.value = true
    snackbarText.value = err
  } finally {
    importLoading.value = false
  }
}
/**
 * 文件上传
 */
const fileInput = ref(null)
const isDragOver = ref(false)

// 打开文件选择对话框
const openFileDialog = () => {
  fileInput.value?.click()
}

// 处理文件选择
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    readFileContent(file)
  }
}

// 处理拖拽进入
const handleDragOver = (event) => {
  event.preventDefault()
  isDragOver.value = true
}

// 处理拖拽离开
const handleDragLeave = () => {
  isDragOver.value = false
}

// 处理文件拖拽
const handleFileDrop = (event) => {
  event.preventDefault()
  isDragOver.value = false

  const files = event.dataTransfer.files
  if (files.length > 0) {
    readFileContent(files[0])
  }
}

// 读取文件内容
const readFileContent = (file) => {
  const reader = new FileReader()
  reader.onload = (e) => {
    languageContent.value = new Blob([e.target.result], {
      type: 'application/octet-stream'
    })
    if (!languageFileName.value) {
      languageFileName.value = file.name
    }
  }
  reader.onerror = () => {
    snackbar.value = true
    snackbarText.value = t('文件读取失败')
  }
  reader.readAsArrayBuffer(file)
}
</script>

<template>
  <div class="pa-6 h-100 overflow-hidden d-flex w-100">
    <v-card
      class="pa-4 h-100 w-100 rounded-lg px-4 overflow-auto no-scrollbar"
      elevation="4"
    >
      <div class="d-flex justify-between align-center py-2 mb-4">
        <div class="text-h6">{{ $t('系统信息') }}</div>
      </div>
      <v-card-text class="flex justify-center align-center h-50">
        <div>
          <div class="text-subtitle-1 mb-3">
            {{ $t('软件版本') }}：<span class="color-primary">{{
              version
            }}</span>
          </div>
          <div class="text-subtitle-1 flex align-center mb-2">
            {{ $t('系统语言') }}：
            <div class="cont">
              <lang-select-version />
            </div>
          </div>
          <div class="text-subtitle-1 flex align-center mb-2">
            {{ $t('系统键盘') }}：
            <div class="cont">
              <v-radio-group
                v-model="isShowKeyboard"
                inline
                @update:modelValue="handleShowKeyboard"
                hide-details
              >
                <v-radio :label="$t('开启')" :value="false"></v-radio>
                <v-radio :label="$t('关闭')" :value="true"></v-radio>
              </v-radio-group>
            </div>
          </div>
          <div class="text-subtitle-1 flex align-center mb-2">
            {{ $t('循环时长') }}：
            <div class="cont">
              <v-text-field
                v-model="cycleTime"
                variant="outlined"
                label=""
                hide-details
                @click:control="handleShow($event, cycleTime)"
              ></v-text-field>
            </div>
            &nbsp; ms
          </div>
          <div class="text-subtitle-1 flex align-center mb-2">
            {{ $t('设置时区') }}：
            <div class="cont">
              <v-autocomplete
                v-model="timeZone"
                item-value="value"
                clearable
                :items="timeZoneList"
                variant="outlined"
                hide-details
              ></v-autocomplete>
            </div>
            <v-btn :loading="timeZoneLoading" @click="handleTimeConfirm">{{
              $t('确定')
            }}</v-btn>
          </div>
          <div class="text-subtitle-1 flex align-center mb-2">
            {{ $t('语言文件') }}：
            <div class="cont">
              <v-btn
                color="primary"
                variant="outlined"
                @click="openLanguageDialog"
              >
                {{ $t('导入语言文件') }}
              </v-btn>
            </div>
          </div>
        </div>
      </v-card-text>
    </v-card>

    <!-- 语言文件导入对话框 -->
    <v-dialog v-model="languageDialog" max-width="600px" persistent>
      <v-card>
        <v-card-title class="text-h6">
          {{ $t('导入语言文件') }}
        </v-card-title>

        <v-card-text>
          <v-autocomplete
            v-model="languageFileName"
            :label="$t('语言文件名')"
            clearable
            :items="langFileNameArr"
            variant="outlined"
            hide-details
          ></v-autocomplete>

          <div class="flex align-center">
            <v-switch
              v-model="overwriteMode"
              :label="overwriteMode ? $t('覆盖模式') : $t('不覆盖模式')"
              color="warning"
              hide-details
            ></v-switch>
            <div class="text-caption text-grey mt-1 ml-6">
              {{
                overwriteMode
                  ? $t('如果文件已存在将被覆盖')
                  : $t('如果文件已存在将导入失败')
              }}
            </div>
          </div>

          <div class="file-upload-area">
            <input
              ref="fileInput"
              type="file"
              accept=".ems"
              style="display: none"
              @change="handleFileSelect"
            />

            <div
              class="drop-zone"
              :class="{ 'drag-over': isDragOver }"
              @drop="handleFileDrop"
              @dragover.prevent="handleDragOver"
              @dragleave="handleDragLeave"
              @click="openFileDialog"
            >
              <v-icon size="48" color="grey">mdi-cloud-upload</v-icon>
              <div class="text-h6 mt-2">
                {{ $t('拖拽文件到此处或点击选择文件') }}
              </div>
              <div class="text-caption text-grey">
                {{ $t('支持 .ems 格式') }}
              </div>
            </div>

            <!-- <v-textarea
              v-model="languageContent"
              :label="$t('语言文件内容')"
              :placeholder="$t('请粘贴完整的语言文件内容...')"
              variant="outlined"
              rows="10"
              required
              :rules="[(v) => !!v || $t('语言文件内容不能为空')]"
              class="mt-4"
            ></v-textarea> -->
          </div>
        </v-card-text>

        <v-card-actions>
          <v-spacer></v-spacer>
          <v-btn
            color="grey"
            variant="text"
            @click="closeLanguageDialog"
            :disabled="importLoading"
          >
            {{ $t('取消') }}
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            @click="handleLanguageImport"
            :loading="importLoading"
          >
            {{ $t('导入') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
.cont {
  width: 250px;
  margin-right: 10px;
}

.file-upload-area {
  .drop-zone {
    border: 2px dashed #ccc;
    border-radius: 8px;
    padding: 40px 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    background-color: #fafafa;

    &:hover {
      border-color: #1976d2;
      background-color: #f5f5f5;
    }

    &.drag-over {
      border-color: #1976d2;
      background-color: #e3f2fd;
    }
  }
}
</style>
