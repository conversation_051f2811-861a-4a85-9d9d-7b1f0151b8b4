/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-04-16 11:07:31
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-25 09:43:10
 * @FilePath: \ems_manage\src\api\deviceConfig.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from './index'

// 获取配置目录下的所有文件名
export const deviceFiles = () => {
  return request({
    url: '/deviceTable/files',
    method: 'get'
  })
}

// 获取<string>文件的内容
export const getDeviceFile = (queryInfo) => {
  return request({
    url: `/deviceTable/file/${queryInfo.fileName}`,
    method: 'get'
  })
}

// 写入<string>文件内容 PUT 管理员可以写任意一个文件，operator只能写device_table.xml
export const setDeviceFile = (data) => {
  return request({
    url: `/deviceTable/file/${data.fileName}`,
    method: 'put',
    data: JSON.stringify({
      content: data.content
    })
  })
}

// 对devices_table.xml进行备份操作 POST 如果文件存在会返回失败 body里要提供fileName，fileName提供备份文件名
export const backupDeviceTable = (data) => {
  return request({
    url: '/deviceTable/file/devices_table.xml/backup',
    method: 'post',
    data
  })
}

// 对devices_table.xml进行备份操作 PUT 强制覆盖 body里要提供fileName，fileName提供备份文件名
export const forceDeviceTable = (data) => {
  return request({
    url: '/deviceTable/file/devices_table.xml/backup',
    method: 'put',
    data
  })
}

// 指定文件对device_table.xml进行恢复备份操作 PUT body里要提供fileName，fileName提供备份的文件名
export const restoreDeviceTable = (data) => {
  return request({
    url: '/deviceTable/file/devices_table.xml/restore',
    method: 'put',
    data
  })
}

// 获取所有协议文件名
export const protocolFiles = () => {
  return request({
    url: '/protocol/files',
    method: 'get'
  })
}

// 获取所有设备类名
export const deviceClassNames = () => {
  return request({
    url: '/deviceTable/classNames',
    method: 'get'
  })
}

// 获取所有设备类名
export const adaptorTypes = () => {
  return request({
    url: '/deviceTable/adaptorTypes',
    method: 'get'
  })
}

// 删除备份文件
export const deleteFile = (queryInfo) => {
  return request({
    url: `/deviceTable/file/${queryInfo.fileName}`,
    method: 'delete'
  })
}

// 重启程序
export const rebootEMS = (data) => {
  return request({
    url: '/rebootEMS',
    method: 'post',
    data
  })
}
export const rebootEMSs = (queryInfo) => {
  return request({
    url: `/rebootEMS/${queryInfo.name}`,
    method: 'get'
  })
}

// 获取设备型号
export const hardwareModel = () => {
  return request({
    url: '/hardwareModel',
    method: 'get'
  })
}

// 获取串口信息
export const serialPorts = () => {
  return request({
    url: '/serialPorts',
    method: 'get'
  })
}

// 获取数据库表
export const databaseTables = () => {
  return request({
    url: '/database/tables',
    method: 'get'
  })
}

// 删除数据库表
export const deleteDatabaseTable = (queryInfo) => {
  return request({
    url: `/database/table/${queryInfo.tableName}`,
    method: 'delete'
  })
}
// 数据库连接状态
export const databaseState = () => {
  return request({
    url: 'database/state',
    method: 'get'
  })
}

// 获取存储周期
export const getStorageCycle = () => {
  return request({
    url: '/database/config',
    method: 'get'
  })
}

// 设置存储周期
export const setStorageCycle = (data) => {
  return request({
    url: '/database/config',
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}
