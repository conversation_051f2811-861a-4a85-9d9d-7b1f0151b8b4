<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-06 14:45:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-15 09:50:23
 * @FilePath: \ems_manage\src\views\Dashboard\lineEchart.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, watch, computed } from 'vue'
import dayjs, { formatDate } from '@/utils/date'

const lineData = defineModel('lineData', {
  default: {
    times: [],
    datas: [],
    currency: '',
    name: '',
    date: ''
  }
})
const scheduleData = defineModel('scheduleData', {
  default: {
    times: [],
    datas: [],
    currency: '',
    name: '',
    date: ''
  }
})
const powerData = defineModel('powerData', {
  default: () => []
})

const options = computed(() => {
  let series = []
  const timeSet = new Set()

  lineData.value.times.forEach((time) => timeSet.add(time))
  powerData.value.forEach((item) => {
    item.times.forEach((time) => timeSet.add(time))
  })
  const sortedTimes = Array.from(timeSet)
    .map((time) => ({
      time: time,
      timestamp: new Date(time).getTime()
    }))
    .sort((a, b) => a.timestamp - b.timestamp)
    .map((item) => item.time) // 只保留时间字符串

  if (!lineData.value.charge_equal_discharge) {
    series.push(
      {
        name: lineData.value?.name + '_charge ' + 'Price',
        type: 'line',
        symbol: 'none',
        data: lineData.value?.times.map((item2, index2) => {
          return [item2, lineData.value?.datas[index2].charge_price_amount]
        }),
        // data: sortedTimes.map((time) => {
        //   const foundIndex = lineData.value?.times.findIndex(
        //     (item) => item === time
        //   )
        //   return [
        //     time,
        //     foundIndex != -1
        //       ? lineData.value?.datas[foundIndex].charge_price_amount
        //       : null
        //   ]
        // }),
        date: lineData.value?.date,
        currency: lineData.value?.currency,
        step: 'end',
        yAxisIndex: 1,
        color: '#ffb980'
      },
      {
        name: lineData.value?.name + '_discharge ' + 'Price',
        type: 'line',
        symbol: 'none',
        data: lineData.value?.times.map((item2, index2) => {
          return [item2, lineData.value?.datas[index2].discharge_price_amount]
        }),
        // data: sortedTimes.map((time) => {
        //   const foundIndex = lineData.value?.times.findIndex(
        //     (item) => item === time
        //   )
        //   return [
        //     time,
        //     foundIndex != -1
        //       ? lineData.value?.datas[foundIndex].discharge_price_amount
        //       : null
        //   ]
        // }),
        date: lineData.value?.date,
        currency: lineData.value?.currency,
        step: 'end',
        yAxisIndex: 1,
        color: '#dc69aa'
      }
    )
  } else {
    series.push({
      name: lineData.value?.name + '_' + 'Price',
      type: 'line',
      symbol: 'none',
      data: lineData.value?.times.map((item2, index2) => {
        return [item2, lineData.value?.datas[index2]]
      }),
      // data: sortedTimes.map((time) => {
      //   const foundIndex = lineData.value?.times.findIndex(
      //     (item) => item === time
      //   )
      //   return [
      //     time,
      //     foundIndex != -1 ? lineData.value?.datas[foundIndex] : null
      //   ]
      // }),
      date: lineData.value?.date,
      currency: lineData.value?.currency,
      step: 'end',
      yAxisIndex: 1,
      color: '#ffb980'
    })
  }
  powerData.value.forEach((item) => {
    let data = item.times.map((item2, index2) => {
      return [item2, item.datas[index2]]
    })
    let name = `${item.device_name}_${item.point_id}`
    series.push({
      name,
      type: 'line',
      symbol: 'none',
      data,
      unit: item.unit,
      step: 'end',
      yAxisIndex: 0
    })
  })
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      padding: [5, 10]
    },
    legend: {
      type: 'scroll',
      textStyle: {
        fontSize: 12,
        fontWeight: 400
      },
      itemGap: 30,
      top: 5
    },
    axisPointer: {
      link: [
        {
          xAxisIndex: 'all'
        }
      ]
    },
    grid: [
      {
        left: 70,
        right: 40,
        top: '20%'
      }
    ],
    xAxis: [
      {
        name: 'UTC',
        nameLocation: 'start',
        nameGap: 30,
        type: 'time',
        axisLine: {
          lineStyle: {
            // color: '#fff'
          }
        },
        axisTick: {
          //y轴刻度线
          show: true
        },
        splitLine: {
          //分割线
          show: false, //控制分割线是否显示
          lineStyle: {
            //分割线的样式
            color: 'rgba(81, 82, 85, 0.3)',
            width: 1,
            type: 'solid'
          }
        }
      }
    ],
    yAxis: [
      {
        name: `kW`,
        type: 'value',
        axisLine: {
          lineStyle: {
            // color: '#fff'
          }
        },
        splitLine: {
          //分割线
          show: false, //控制分割线是否显示
          lineStyle: {
            //分割线的样式
            color: 'rgba(81, 82, 85, 0.3)',
            width: 1,
            type: 'solid'
          }
        },
        nameTextStyle: {
          align: 'right'
        },
        minInterval: 5,
        // nameLocation: 'start',
        alignTicks: true
      },
      {
        name: lineData.value?.currency,
        type: 'value',
        axisLine: {
          lineStyle: {
            // color: '#fff'
          }
        },
        splitLine: {
          //分割线
          show: false, //控制分割线是否显示
          lineStyle: {
            //分割线的样式
            color: 'rgba(81, 82, 85, 0.3)',
            width: 1,
            type: 'solid'
          }
        },
        nameTextStyle: {
          align: 'right'
        },
        minInterval: 5,
        // nameLocation: 'start',
        alignTicks: true
      }
    ],
    dataZoom: [
      {
        show: true,
        realtime: true,
        start: 0,
        type: 'slider'
      },
      {
        type: 'inside',
        realtime: true,
        start: 0
      }
    ],
    series: [
      {
        name: 'Power schedule',
        type: 'line',
        symbol: 'none',
        data: scheduleData.value?.times.map((item2, index2) => {
          return [item2, scheduleData.value?.datas[index2]]
        }),
        // data: sortedTimes.map((time) => {
        //   const foundIndex = scheduleData.value?.times.findIndex(
        //     (item) => item === time
        //   )
        //   return [
        //     time,
        //     foundIndex != -1 ? scheduleData.value?.datas[foundIndex] : null
        //   ]
        // }),
        step: 'end',
        yAxisIndex: 0
      },
      ...series
    ]
  }
})
</script>

<template>
  <div>
    <BaseEchart
      width="100%"
      height="100%"
      :options="options"
      ref="myChart"
    ></BaseEchart>
  </div>
</template>

<style lang="scss" scoped></style>
