/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-27 10:00:31
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-02-12 09:24:50
 * @FilePath: \ems_manage\src\api\log.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from './index'
export const getLogData = (queryInfo) => {
  return request({
    url: '/operationLog',
    method: 'post',
    data: JSON.stringify(queryInfo)
  })
}

export const storeOperationLog = (data) => {
  return request({
    url: '/storeOperationLog',
    method: 'post',
    data
  })
}