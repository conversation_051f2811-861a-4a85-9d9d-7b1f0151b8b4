<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-25 18:41:41
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-22 20:12:36
 * @FilePath: \ems_manage\src\views\Config\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, toRefs } from 'vue'
import { useUserStore } from '@/store/module/user'

import Pwd from './pwd.vue'

const { userInfo } = toRefs(useUserStore())

const tab = ref(1)
</script>

<template>
  <div class="pa-6 w-100 overflow-hidden d-flex flex-column">
    <div class="px-2">
      <v-card class="pa-4 w-100 rounded-lg no-scrollbar mb-4" elevation="4">
        <div class="d-flex justify-between align-center">
          <div class="text-h6">{{ $t('用户设置') }}</div>
          <div class="d-flex align-center"></div>
        </div>
      </v-card>
    </div>
    <v-tabs v-model="tab" color="secondary" class="px-3">
      <v-tab :value="1">{{ $t('修改密码') }}</v-tab>
      <v-tab :value="2" v-if="userInfo.permission_level == 4">{{
        $t('重置密码')
      }}</v-tab>
    </v-tabs>

    <v-tabs-window v-model="tab" class="mt-4">
      <v-tabs-window-item :value="1"
        ><Pwd typeText="edit"
      /></v-tabs-window-item>
      <v-tabs-window-item :value="2"
        ><Pwd typeText="reset"
      /></v-tabs-window-item>
    </v-tabs-window>
  </div>
</template>

<style lang="scss" scoped>
:deep(.v-window) {
  overflow: visible;
}
</style>
