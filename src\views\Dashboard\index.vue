<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-10 11:08:47
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-18 11:27:09
 * @FilePath: \ems_manage\src\views\Dashboard\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { toRefs, shallowRef, ref, computed, onUnmounted } from 'vue'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'
import { useDisplay } from 'vuetify'
import { useDeviceStore } from '@/store/module/device'
import { getHomeCardData, getLocalTime } from '@/api/config'
import dayjs from '@/utils/date'
// import { buildMode } from '../../public/envConfig'

import InfoCard from './coms/info-card.vue'
import StatusCard from './coms/status-card.vue'
import CDCard from './coms/cd-card.vue'
import FaultCard from './coms/fault-card.vue'
import LineCard from './coms/line-card.vue'
import Prolongation from './prolongation.vue'

const { mobile } = useDisplay()
const { homeCardData, homeConfig, cycleTime } = toRefs(useConfigStore())
const coms = shallowRef({
  system_info: InfoCard,
  system_status: StatusCard,
  'charge-discharge': CDCard,
  fault: FaultCard,
  line: LineCard
})
const { snackbar, snackbarText } = toRefs(useGlobalStore())
const rightData = computed(() => {
  return homeCardData.value
    .filter((item) => item.is_display == '1')
    .sort((a, b) => a.sort - b.sort)
})
const cardCalcDatas = ref([])

const time = ref()
const isUnmounted = ref(false) // 标志位
const getAlarmData = async () => {
  const res5 = await useDeviceStore().getFaultDataFn({
    pageSize: 10,
    pageIndex: 1
  })
}
const getData = async () => {
  try {
    // 获取首页配置信息
    const res1 = await useConfigStore().getWebConfigFn({ key: 'homeCard' })
    // 获取系统状态信息
    const res2 = await useConfigStore().getSystemStatusFn()
    let data = []
    let cardCalcData = []
    const date = dayjs().format('YYYY-MM-DD')
    rightData.value.forEach((item) => {
      cardCalcData.push({
        project: item.project,
        chart_color: item.chart_color,
        chart_type: item.chart_type,
        formula: item.formula,
        unit: item.unit,
        title: item.title,
        card_id: item.card_id
      })
      item.project.forEach((item1) => {
        data.push({
          device: item.device,
          point_id: item1.point_id,
          date
        })
      })
    })
    const result = await getHomeCardData(JSON.stringify(data))
    if (result.code !== 200) return new error(result.msg)
    if (result.data.cards && result.data.cards?.length) {
      cardCalcData.forEach((item) => {
        item.project.forEach((item1) => {
          let datas = result.data.cards.filter(
            (item2) => item2.point_id === item1.point_id
          )
          item1.data = datas.map((item3) => (item3.value ? item3.value : null))
          item1.unit = datas.length ? datas[0].units : ''
          item1.times = datas.map((item3) =>
            item3.record_time ? item3.record_time : null
          )
          item1.title = datas.length ? datas[0].point_name : ''
        })
      })
      cardCalcDatas.value = cardCalcData
    }
    // 获取项目信息
    const res3 = await useConfigStore().getSystemInfoFn()
    // 获取当前告警总数
    const res4 = await useDeviceStore().getCurrentAlarmFn()
    // 获取告警数据
    const res5 = await useDeviceStore().getFaultDataFn({
      pageSize: 10,
      pageIndex: 1
    })
    time.value = setInterval(() => {
      if (isUnmounted.value) {
        // 如果组件已卸载，则不再执行逻辑
        clearInterval(time.value)
        return
      }
      getAlarmData()
      useConfigStore().getSystemStatusFn()
    }, cycleTime.value)
  } catch (error) {
    console.log(error)
    snackbar.value = true
    snackbarText.value = error
  }
}

getData()

onUnmounted(() => {
  isUnmounted.value = true
  if (time.value) clearInterval(time.value)
})
</script>

<template>
  <div
    class="pa-4 overflow-auto d-flex flex-wrap"
    :class="[!mobile && 'h-100']"
  >
    <v-col cols="12" lg="9" md="12" sm="12" xs="12" class="h-100">
      <v-row gutter="20" class="h-100">
        <v-col
          cols="12"
          md="4"
          lg="4"
          sm="12"
          xs="12"
          v-for="item in homeConfig.top"
          :key="item.id"
        >
          <v-card height="170px" elevation="4" class="rounded-lg">
            <component :is="coms[item.type]" :item="item"></component>
          </v-card>
        </v-col>
        <v-col
          cols="12"
          md="12"
          lg="12"
          sm="12"
          xs="12"
          style="height: calc(100% - 170px)"
        >
          <v-card
            elevation="4"
            class="rounded-lg"
            style="background: #edf1f5; height: 100%"
          >
            <Prolongation></Prolongation>
          </v-card>
        </v-col>
      </v-row>
    </v-col>
    <v-col cols="12" lg="3" md="12" sm="12" xs="12" class="h-100">
      <v-card
        elevation="4"
        class="rounded-lg overflow-auto"
        :style="{ height: !mobile ? '100%' : '100vh' }"
        v-if="cardCalcDatas.length"
      >
        <div
          style="height: 33.3%"
          v-for="item in cardCalcDatas"
          :key="item.card_id"
        >
          <LineCard :item="item"></LineCard>
        </div>
      </v-card>
      <v-card
        elevation="4"
        class="rounded-lg overflow-auto"
        :style="{ height: !mobile ? '100%' : '100vh' }"
        v-else
      >
        <v-empty-state :headline="$t('无数据')"></v-empty-state>
      </v-card>
    </v-col>
  </div>
</template>

<style lang="scss" scoped>
:deep(.v-card-title) {
  font-size: 16px;
}
.device-state-on {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #28c79c;
  margin-right: 5px;
}
.device-state-off {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  background: #ff6b6b;
  margin-right: 5px;
}
:root {
  --e150e972: 100%;
}
</style>
