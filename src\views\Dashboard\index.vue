<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-10 11:08:47
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-18 11:27:09
 * @FilePath: \ems_manage\src\views\Dashboard\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { toRefs, shallowRef, ref, computed, onUnmounted } from 'vue'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'
import { useDisplay } from 'vuetify'
import { useDeviceStore } from '@/store/module/device'
import { getHomeCardData, getLocalTime } from '@/api/config'
import dayjs from '@/utils/date'
// import { buildMode } from '../../public/envConfig'

import InfoCard from './coms/info-card.vue'
import StatusCard from './coms/status-card.vue'
import CDCard from './coms/cd-card.vue'
import FaultCard from './coms/fault-card.vue'
import LineCard from './coms/line-card.vue'
import Prolongation from './prolongation.vue'

const { mobile } = useDisplay()
const { homeCardData, homeConfig, cycleTime } = toRefs(useConfigStore())
const coms = shallowRef({
  system_info: InfoCard,
  system_status: StatusCard,
  'charge-discharge': CDCard,
  fault: FaultCard,
  line: LineCard
})
const { snackbar, snackbarText } = toRefs(useGlobalStore())
const rightData = computed(() => {
  return homeCardData.value
    .filter((item) => item.is_display == '1')
    .sort((a, b) => a.sort - b.sort)
})
const cardCalcDatas = ref([])

const time = ref()
const isUnmounted = ref(false) // 标志位
const getAlarmData = async () => {
  const res5 = await useDeviceStore().getFaultDataFn({
    pageSize: 10,
    pageIndex: 1
  })
}
const getData = async () => {
  try {
    // 获取首页配置信息
    const res1 = await useConfigStore().getWebConfigFn({ key: 'homeCard' })
    // 获取系统状态信息
    const res2 = await useConfigStore().getSystemStatusFn()
    let data = []
    let cardCalcData = []
    const date = dayjs().format('YYYY-MM-DD')
    rightData.value.forEach((item) => {
      cardCalcData.push({
        project: item.project,
        chart_color: item.chart_color,
        chart_type: item.chart_type,
        formula: item.formula,
        unit: item.unit,
        title: item.title,
        card_id: item.card_id
      })
      item.project.forEach((item1) => {
        data.push({
          device: item.device,
          point_id: item1.point_id,
          date
        })
      })
    })
    const result = await getHomeCardData(JSON.stringify(data))
    if (result.code !== 200) return new error(result.msg)
    if (result.data.cards && result.data.cards?.length) {
      cardCalcData.forEach((item) => {
        item.project.forEach((item1) => {
          let datas = result.data.cards.filter(
            (item2) => item2.point_id === item1.point_id
          )
          item1.data = datas.map((item3) => (item3.value ? item3.value : null))
          item1.unit = datas.length ? datas[0].units : ''
          item1.times = datas.map((item3) =>
            item3.record_time ? item3.record_time : null
          )
          item1.title = datas.length ? datas[0].point_name : ''
        })
      })
      cardCalcDatas.value = cardCalcData
    }
    // 获取项目信息
    const res3 = await useConfigStore().getSystemInfoFn()
    // 获取当前告警总数
    const res4 = await useDeviceStore().getCurrentAlarmFn()
    // 获取告警数据
    const res5 = await useDeviceStore().getFaultDataFn({
      pageSize: 10,
      pageIndex: 1
    })
    time.value = setInterval(() => {
      if (isUnmounted.value) {
        // 如果组件已卸载，则不再执行逻辑
        clearInterval(time.value)
        return
      }
      getAlarmData()
      useConfigStore().getSystemStatusFn()
    }, cycleTime.value)
  } catch (error) {
    console.log(error)
    snackbar.value = true
    snackbarText.value = error
  }
}

getData()

onUnmounted(() => {
  isUnmounted.value = true
  if (time.value) clearInterval(time.value)
})
</script>

<template>
  <div
    class="hmi-dashboard overflow-auto d-flex flex-wrap"
    :class="[!mobile && 'h-100']"
  >
    <v-col cols="12" lg="9" md="12" sm="12" xs="12" class="h-100">
      <v-row class="h-100 hmi-spacing-md">
        <v-col
          cols="12"
          md="4"
          lg="4"
          sm="12"
          xs="12"
          v-for="item in homeConfig.top"
          :key="item.id"
          class="hmi-card-col"
        >
          <v-card
            :height="mobile ? '160px' : '180px'"
            elevation="4"
            class="rounded-lg hmi-info-card"
          >
            <component :is="coms[item.type]" :item="item"></component>
          </v-card>
        </v-col>
        <v-col
          cols="12"
          md="12"
          lg="12"
          sm="12"
          xs="12"
          :style="{
            height: mobile ? 'calc(100% - 160px)' : 'calc(100% - 180px)'
          }"
        >
          <v-card
            elevation="4"
            class="rounded-lg hmi-main-chart"
            style="background: #edf1f5; height: 100%"
          >
            <Prolongation></Prolongation>
          </v-card>
        </v-col>
      </v-row>
    </v-col>
    <v-col cols="12" lg="3" md="12" sm="12" xs="12" class="h-100">
      <v-card
        elevation="4"
        class="rounded-lg overflow-auto hmi-side-panel"
        :style="{ height: !mobile ? '100%' : '100vh' }"
        v-if="cardCalcDatas.length"
      >
        <div
          :style="{ height: mobile ? '200px' : '33.3%' }"
          v-for="item in cardCalcDatas"
          :key="item.card_id"
          class="hmi-line-card-wrapper"
        >
          <LineCard :item="item"></LineCard>
        </div>
      </v-card>
      <v-card
        elevation="4"
        class="rounded-lg overflow-auto hmi-side-panel"
        :style="{ height: !mobile ? '100%' : '100vh' }"
        v-else
      >
        <v-empty-state
          :headline="$t('无数据')"
          class="hmi-empty-state"
        ></v-empty-state>
      </v-card>
    </v-col>
  </div>
</template>

<style lang="scss" scoped>
.hmi-dashboard {
  padding: 16px;

  /* HMI屏幕优化 */
  @media screen and (max-width: 1280px) and (min-width: 1024px) {
    padding: 12px;
  }

  @media screen and (max-width: 1024px) {
    padding: 8px;
  }
}

.hmi-card-col {
  padding: 8px !important;

  @media screen and (max-width: 1024px) {
    padding: 4px !important;
  }
}

.hmi-info-card {
  transition: all 0.3s ease;

  &:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  }

  :deep(.v-card-title) {
    font-size: 18px !important;
    font-weight: 600;
    padding: 16px 20px 8px;

    @media screen and (max-width: 1024px) {
      font-size: 16px !important;
      padding: 12px 16px 6px;
    }
  }

  :deep(.v-card-text) {
    padding: 8px 20px 16px;

    @media screen and (max-width: 1024px) {
      padding: 6px 16px 12px;
    }
  }
}

.hmi-main-chart {
  :deep(.v-card-text) {
    padding: 16px !important;
    height: calc(100% - 32px);

    @media screen and (max-width: 1024px) {
      padding: 12px !important;
      height: calc(100% - 24px);
    }
  }
}

.hmi-side-panel {
  :deep(.v-card-text) {
    padding: 12px !important;

    @media screen and (max-width: 1024px) {
      padding: 8px !important;
    }
  }
}

.hmi-line-card-wrapper {
  border-bottom: 1px solid rgba(0, 0, 0, 0.1);

  &:last-child {
    border-bottom: none;
  }

  :deep(.v-card) {
    box-shadow: none !important;
    border-radius: 0 !important;
  }
}

.hmi-empty-state {
  :deep(.v-empty-state__headline) {
    font-size: 18px !important;
    color: rgba(0, 0, 0, 0.6);
  }
}

.device-state-on {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #28c79c;
  margin-right: 8px;

  @media screen and (max-width: 1024px) {
    width: 10px;
    height: 10px;
    margin-right: 6px;
  }
}

.device-state-off {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #ff6b6b;
  margin-right: 8px;

  @media screen and (max-width: 1024px) {
    width: 10px;
    height: 10px;
    margin-right: 6px;
  }
}

:root {
  --e150e972: 100%;
}
</style>
