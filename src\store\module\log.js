/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-27 10:03:12
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-15 18:02:08
 * @FilePath: \ems_manage\src\store\module\log.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  getLogData,
  storeOperationLog
} from '@/api/log'
import { i18n } from '@/locale'

export const useLogStore = defineStore(
  'log',
  () => {
    const pageInfo = ref({
      total: 0,
      pageSize: 10,
      pageIndex: 1
    })
    const logQueryInfo = ref({})
    const logData = ref([])
    const getLogDataFn = async (queryInfo) => {
      logData.value = []
      const res = await getLogData({
        pageSize: pageInfo.value.pageSize,
        pageIndex: pageInfo.value.pageIndex,
        ...logQueryInfo.value,
        ...queryInfo
      })
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
      pageInfo.value.total = res.data.count
      logData.value = res.data.logs?.length ? res.data.logs.map(item => {
        return {
          ...item,
          ...JSON.parse(item.set_value)
        }
      }) : []
    }

    const storeOperationLogFn = async (data) => {
      const res = await storeOperationLog(data)
      if (res.code !== 200) {
        snackbar = true
        snackbarText = res.msg
        return new Error(res.msg)
      }
    }

    return {
      pageInfo,
      logQueryInfo,
      logData,
      getLogDataFn,
      storeOperationLogFn
    }
  },
  // {
  //   persist: true
  // }
)
