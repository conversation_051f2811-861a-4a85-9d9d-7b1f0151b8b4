<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-01-09 14:31:44
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-22 08:54:09
 * @FilePath: \ems_manage\src\components\empty\src\empty.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import Empty from '@/assets/img/empty.webp'

const model = defineModel({ default: 'No Data.', type: String })
</script>

<template>
  <div class="w-100 h-100">
    <v-empty-state
      :image="Empty"
      :title="model"
      class="pb-20 cont"
    ></v-empty-state>
  </div>
</template>

<style lang="scss" scoped>
:deep(.cont) {
  .v-img {
    height: 500px !important;
  }
}
</style>
