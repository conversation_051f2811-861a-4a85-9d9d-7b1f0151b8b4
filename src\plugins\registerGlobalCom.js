/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-25 16:34:15
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-21 20:09:05
 * @FilePath: \ems_manage\src\plugins\registerGlobalCom.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// import Ac from '@/views/Editor/components/ac.vue'
// import Cell from '@/views/Editor/components/cell.vue'
// import Device from '@/views/Editor/components/device.vue'
// import Diesel from '@/views/Editor/components/diesel.vue'
// import Load from '@/views/Editor/components/load.vue'
// import Pv from '@/views/Editor/components/pv.vue'

import Chunengbianliuqi from '@/views/Editor/components/cabinet/chunengbianliuqi.vue'
import Guangfunibianqi from '@/views/Editor/components/cabinet/guangfunibianqi.vue'
import Pcsgui from '@/views/Editor/components/cabinet/pcsgui.vue'
import Dianwang from '@/views/Editor/components/cabinet/dianwang.vue'
import Guangfu from '@/views/Editor/components/cabinet/guangfu.vue'
import Fangzi from '@/views/Editor/components/cabinet/fangzi.vue'
import Dianchi from '@/views/Editor/components/cabinet/dianchi.vue'
import DeviceName from '@/views/Editor/components/cabinet/deviceName.vue'
import Duanluqi from '@/views/Editor/components/cabinet/duanluqi.vue'
import DuanluqiClose from '@/views/Editor/components/cabinet/duanluqiClose.vue'
import ThreePhase from '@/views/Editor/components/cabinet/ThreePhase.vue'
import Transformer from '@/views/Editor/components/cabinet/transformer.vue'
import Generator from '@/views/Editor/components/cabinet/generator.vue'
import ThreePhase1 from '@/views/Editor/components/cabinet/ThreePhase1.vue'
import Circle from '@/views/Editor/components/common/circle.vue'
import ThreePhase2 from '@/views/Editor/components/cabinet/ThreePhase2.vue'

export function registerGlobalCom(app) {
  // app.component('FlowAc', Ac)
  // app.component('FlowCell', Cell)
  // app.component('FlowDevice', Device)
  // app.component('FlowDiesel', Diesel)
  // app.component('FlowLoad', Load)
  // app.component('FlowPv', Pv)
  app.component('Chunengbianliuqi', Chunengbianliuqi)
  app.component('Guangfunibianqi', Guangfunibianqi)
  app.component('Pcsgui', Pcsgui)
  app.component('Dianwang', Dianwang)
  app.component('Guangfu', Guangfu)
  app.component('Fangzi', Fangzi)
  app.component('Dianchi', Dianchi)
  app.component('DeviceName', DeviceName)
  app.component('Duanluqi', Duanluqi)
  app.component('ThreePhase', ThreePhase)
  app.component('Transformer', Transformer)
  app.component('DuanluqiClose', DuanluqiClose)
  app.component('Generator', Generator)
  app.component('ThreePhase1', ThreePhase1)
  app.component('Circle', Circle)
  app.component('ThreePhase2', ThreePhase2)
}