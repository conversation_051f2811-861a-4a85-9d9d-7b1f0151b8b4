<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-27 15:33:04
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-11-05 17:58:44
 * @FilePath: \ems_manage\src\views\Dashboard\coms\info-card.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { toRefs, ref, computed } from 'vue'
import { useDeviceStore } from '@/store/module/device'
import dayjs from '@/utils/date'

import Empty from '@/assets/img/empty.webp'

const { faultData } = toRefs(useDeviceStore())

const loading = ref(false)
// loading.value = true
// useDeviceStore()
//   .getFaultDataFn({
//     // date: dayjs(new Date()).format('YYYY-MM-DD'),
//     pageSize: 10,
//     pageIndex: 1
//   })
//   .then(() => {
//     loading.value = false
//   })
//   .catch(() => {
//     loading.value = false
//   })

const timeFaultData = computed(() => {
  return faultData.value.filter((item) => !item.end_time)
})
</script>

<template>
  <v-card
    height="170px"
    elevation="4"
    class="rounded-lg"
    :disabled="loading"
    :loading="loading"
  >
    <template v-slot:loader="{ isActive }">
      <v-progress-linear
        :active="isActive"
        color="primary"
        height="4"
        indeterminate
      ></v-progress-linear>
    </template>
    <v-empty-state
      :headline="$t('无告警')"
      :text="$t('恭喜，您的设备很安全。')"
      v-if="!timeFaultData.length"
    ></v-empty-state>
    <v-carousel
      show-arrows="hover"
      height="100%"
      hide-delimiter-background
      hide-delimiters
      cycle
      interval="3000"
      v-else
    >
      <v-carousel-item
        v-for="(item, index) in timeFaultData"
        :key="`${item.faultObj}_${index}`"
      >
        <div class="d-flex flex-no-wrap justify-space-between h-full">
          <div>
            <v-card-title> {{ $t('告警信息') }} </v-card-title>

            <v-card-subtitle
              >{{ item.device_name }}_{{ item.module_name }}</v-card-subtitle
            >
            <v-card-text class="font-600">
              {{ item.content }}
            </v-card-text>

            <div class="text-caption mx-4 mb-2">
              {{ item.start_time }}
            </div>
          </div>

          <div class="d-flex align-center mr-4">
            <img
              src="../../../assets/img/alarm.webp"
              style="width: 100px; height: 100px"
            />
          </div>
        </div>
      </v-carousel-item>
    </v-carousel>
  </v-card>
</template>

<style lang="scss" scoped></style>
