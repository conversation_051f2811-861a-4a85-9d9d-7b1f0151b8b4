<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-27 15:33:04
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-04-28 08:59:53
 * @FilePath: \ems_manage\src\views\Dashboard\coms\info-card.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, computed, toRefs } from 'vue'
import { getDeviceData } from '@/api/device'
import dayjs from '@/utils/date'
import { useGlobalStore } from '@/store/global'
import { useI18n } from 'vue-i18n'
import { isEmpty } from 'lodash-es'
import { evaluate } from 'mathjs'

const props = defineProps({
  item: Object
})
const { snackbar, snackbarText } = toRefs(useGlobalStore())
const { t } = useI18n()
const lineItem = ref(props.item)
const times = ref([])
const yAxis = ref({})
const series = ref([])
const colors = ref([
  '#3fb1e3',
  '#6be6c1',
  '#c4ebad',
  '#f8b52f',
  '#626c91',
  '#96dee8'
])

const getPointDataFn = async (pointId) => {
  let times = []
  let datas = []
  let unit = ''
  const res = await getDeviceData({
    deviceId: props.item.device,
    pointId,
    date: dayjs(new Date()).format('YYYY-MM-DD')
  })
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return {
      times,
      datas,
      unit
    }
  }
  if (!res.data.length) {
    return {
      times,
      datas,
      unit
    }
  }
  res.forEach((item) => {
    if (item.modules.length) {
      // 有模块
      item.modules.forEach((module) => {
        let data = module.points[0].analog
        times = data.map((item) => item.record_time)
        datas = data.map((item) => item.value)
        unit = data[0].units
      })
    } else {
      if (!item.points || !item.points.length) return
      let data = item.points[0].analog
      times = data.map((item) => item.record_time)
      datas = data.map((item) => item.value)
      unit = data[0].units
    }
  })

  return {
    times,
    datas,
    unit
  }
}
// 使用 Promise.all 来处理所有请求
const isShowEmpty = ref(false)
// Promise.all(
//   lineItem.value.project.map((proItem) => getPointDataFn(proItem.point_id))
// )
//   .then((results) => {
//     // 所有请求都成功完成
//     // console.log('All requests completed successfully:', results)
//     // 在这里执行其他操作
//     results.forEach((item, index) => {
//       lineItem.value.project[index].data = item.datas
//       lineItem.value.project[index].unit = item.unit
//       times.value = item.times.length ? item.times : times.value
//       if (index == 0)
//         lineItem.value.project[index].color = lineItem.value.chart_color
//       else lineItem.value.project[index].color = colors.value[index]
//     })
//     if (!times.value.length) isEmpty.value = true

//     let seriess = []
//     let yAxiss = {
//       type: 'value',
//       name: '',
//       axisLine: {
//         lineStyle: {
//           // color: '#fff'
//         }
//       },
//       splitLine: {
//         //分割线
//         show: true, //控制分割线是否显示
//         lineStyle: {
//           //分割线的样式
//           color: 'rgba(81, 82, 85, 0.3)',
//           width: 1,
//           type: 'solid'
//         }
//       }
//     }
//     if (lineItem.value.conbination == '0') {
//       if (lineItem.value.chart_type == 'line') {
//         if (lineItem.value.project.length == 1) {
//           seriess.push({
//             name: lineItem.value.title,
//             type: 'line',
//             symbol: 'none',
//             color: lineItem.value.chart_color,
//             data: lineItem.value.project[0].data,
//             lineStyle: {
//               width: 1
//             }
//           })
//           yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//         } else {
//           lineItem.value.project.forEach((item) => {
//             seriess.push({
//               name: lineItem.value.title,
//               type: 'line',
//               symbol: 'none',
//               color: item.color,
//               data: item.data,
//               lineStyle: {
//                 width: 1
//               }
//             })
//           })
//           yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//         }
//       } else if (lineItem.value == 'bar') {
//         if (lineItem.value.project.length == 1) {
//           seriess.push({
//             name: lineItem.value.title,
//             barGap: 0,
//             smooth: true,
//             type: 'bar',
//             barWidth: 15,
//             data: lineItem.value.project[0].data,
//             color: lineItem.value.chart_color,
//             animationDuration: 2800,
//             animationEasing: 'cubicInOut'
//           })
//           yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//         } else {
//           lineItem.value.project.forEach((item) => {
//             seriess.push({
//               name: lineItem.value.title,
//               barGap: 0,
//               smooth: true,
//               type: 'bar',
//               barWidth: 15,
//               data: item.data,
//               color: item.color,
//               animationDuration: 2800,
//               animationEasing: 'cubicInOut'
//             })
//           })
//           yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//         }
//       }
//     } else if (lineItem.value.conbination == '1') {
//       function sumArrays(arrays) {
//         let index = 0
//         arrays.forEach((item, arrIndex) => {
//           if (item.length) index = arrIndex
//         })
//         return arrays[index].map((_, index) => {
//           return arrays.reduce(
//             (sum, array) =>
//               Number(sum !== undefined ? sum : 0) +
//               Number(array[index] !== undefined ? array[index] : 0),
//             0
//           )
//         })
//       }
//       let sumArr = sumArrays(
//         lineItem.value.project.map((item) => {
//           return item.data
//         })
//       )
//       if (lineItem.value.chart_type == 'line') {
//         seriess.push({
//           name: lineItem.value.title,
//           type: 'line',
//           symbol: 'none',
//           color: lineItem.value.chart_color,
//           data: sumArr,
//           lineStyle: {
//             width: 1
//           }
//         })
//         yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//       } else if (lineItem.value == 'bar') {
//         seriess.push({
//           name: lineItem.value.title,
//           barGap: 0,
//           smooth: true,
//           type: 'bar',
//           barWidth: 15,
//           data: sumArr,
//           color: lineItem.value.chart_color,
//           animationDuration: 2800,
//           animationEasing: 'cubicInOut'
//         })
//         yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//       }
//     }
//     yAxis.value = yAxiss
//     series.value = seriess
//   })
//   .catch((error) => {
//     isEmpty.value = true
//     // 如果任何一个请求失败，catch 会被触发
//     console.error('One or more requests failed:', error)
//   })
lineItem.value.project.forEach((item, index) => {
  if (!item.data.length && !item.times.length) isShowEmpty.value = true
  else isShowEmpty.value = false
  times.value = item.times.length ? item.times : times.value
  if (index == 0) item.color = lineItem.value.chart_color
  else item.color = colors.value[index]
})
let seriess = []
let yAxiss = {
  type: 'value',
  name: '',
  axisLine: {
    lineStyle: {
      // color: '#fff'
    }
  },
  splitLine: {
    //分割线
    show: true, //控制分割线是否显示
    lineStyle: {
      //分割线的样式
      color: 'rgba(81, 82, 85, 0.3)',
      width: 1,
      type: 'solid'
    }
  }
}

// 1、数据组合，相加、平均值
// 2、按照用户填的计算公式来计算
// function sumArraysAndAverage(arrays) {
//   // 确定结果数组的最大长度
//   const maxLength = Math.max(...arrays.map((arr) => arr.length))

//   // 创建一个数组来存储每个索引位置的总和
//   const sums = new Array(maxLength).fill(0)

//   // 对每个索引位置进行求和
//   for (let i = 0; i < maxLength; i++) {
//     sums[i] = arrays.reduce((sum, array) => {
//       return sum + (array[i] !== undefined ? array[i] : 0)
//     }, 0)
//   }

//   // 计算总和的平均值
//   const totalSum = sums.reduce((acc, val) => acc + val, 0)
//   const average = totalSum / maxLength

//   return { sums, average }
// }
// if (lineItem.value.conbination == '0') {
//   if (lineItem.value.chart_type == 'line') {
//     if (lineItem.value.project.length == 1) {
//       seriess.push({
//         name: lineItem.value.title,
//         type: 'line',
//         symbol: 'none',
//         color: lineItem.value.chart_color,
//         data: lineItem.value.project[0].data,
//         lineStyle: {
//           width: 1
//         }
//       })
//       yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//     } else {
//       lineItem.value.project.forEach((item) => {
//         seriess.push({
//           name: item.title ? item.title : lineItem.value.title,
//           type: 'line',
//           symbol: 'none',
//           color: item.color,
//           data: item.data,
//           lineStyle: {
//             width: 1
//           }
//         })
//       })
//       yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//     }
//   } else if (lineItem.value == 'bar') {
//     if (lineItem.value.project.length == 1) {
//       seriess.push({
//         name: lineItem.value.title,
//         barGap: 0,
//         smooth: true,
//         type: 'bar',
//         barWidth: 15,
//         data: lineItem.value.project[0].data,
//         color: lineItem.value.chart_color,
//         animationDuration: 2800,
//         animationEasing: 'cubicInOut'
//       })
//       yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//     } else {
//       lineItem.value.project.forEach((item) => {
//         seriess.push({
//           name: item.title ? item.title : lineItem.value.title,
//           barGap: 0,
//           smooth: true,
//           type: 'bar',
//           barWidth: 15,
//           data: item.data,
//           color: item.color,
//           animationDuration: 2800,
//           animationEasing: 'cubicInOut'
//         })
//       })
//       yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//     }
//   }
// } else if (lineItem.value.conbination == '1') {
//   let sumArr = sumArraysAndAverage(
//     lineItem.value.project.map((item) => {
//       return item.data
//     })
//   ).sums
//   if (lineItem.value.chart_type == 'line') {
//     seriess.push({
//       name: lineItem.value.title,
//       type: 'line',
//       symbol: 'none',
//       color: lineItem.value.chart_color,
//       data: sumArr,
//       lineStyle: {
//         width: 1
//       }
//     })
//     yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//   } else if (lineItem.value == 'bar') {
//     seriess.push({
//       name: lineItem.value.title,
//       barGap: 0,
//       smooth: true,
//       type: 'bar',
//       barWidth: 15,
//       data: sumArr,
//       color: lineItem.value.chart_color,
//       animationDuration: 2800,
//       animationEasing: 'cubicInOut'
//     })
//     yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//   }
// } else if (lineItem.value.conbination == '2') {
//   let averageArr = sumArraysAndAverage(
//     lineItem.value.project.map((item) => {
//       return item.data
//     })
//   ).average
//   if (lineItem.value.chart_type == 'line') {
//     seriess.push({
//       name: lineItem.value.title,
//       type: 'line',
//       symbol: 'none',
//       color: lineItem.value.chart_color,
//       data: averageArr,
//       lineStyle: {
//         width: 1
//       }
//     })
//     yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//   } else if (lineItem.value == 'bar') {
//     seriess.push({
//       name: lineItem.value.title,
//       barGap: 0,
//       smooth: true,
//       type: 'bar',
//       barWidth: 15,
//       data: sumArr,
//       color: lineItem.value.chart_color,
//       animationDuration: 2800,
//       animationEasing: 'cubicInOut'
//     })
//     yAxiss.name = `${t('单位')}：${lineItem.value.project[0].unit}`
//   }
// }
// 2、计算公式
const unknown = ['x', 'y', 'z', 't', 'u', 'v', 'w', 'p', 'q', 'r', 's']
const calculateFormula = (points) => {
  // 确定结果数组的最大长度
  const maxLength = Math.max(...points.map((arr) => arr.length))

  // 创建一个数组来存储每个索引位置的总和
  const result = new Array(maxLength).fill(0)
  // 替换公式中的变量为实际数据
  let parsedFormula = lineItem.value.formula
  try {
    for (let i = 0; i < maxLength; i++) {
      let obj = {}
      for (let j = 0; j < points.length; j++) {
        obj[unknown[j]] = points[j][i]
      }
      result[i] = evaluate(parsedFormula, obj).toFixed(2)
    }
    return result
  } catch (error) {
    console.error('公式计算错误:', error)
    return null
  }
}
if (isEmpty(lineItem.value.formula)) {
  if (lineItem.value.chart_type == 'line') {
    if (lineItem.value.project.length == 1) {
      seriess.push({
        name: lineItem.value.title,
        type: 'line',
        symbol: 'none',
        color: lineItem.value.chart_color,
        data: lineItem.value.project[0].data,
        lineStyle: {
          width: 1
        }
      })
      yAxiss.name = `${t('单位')}：${lineItem.value.unit}`
    } else {
      lineItem.value.project.forEach((item) => {
        seriess.push({
          name: item.title ? item.title : lineItem.value.title,
          type: 'line',
          symbol: 'none',
          color: item.color,
          data: item.data,
          lineStyle: {
            width: 1
          }
        })
      })
      yAxiss.name = `${t('单位')}：${lineItem.value.unit}`
    }
  } else if (lineItem.value == 'bar') {
    if (lineItem.value.project.length == 1) {
      seriess.push({
        name: lineItem.value.title,
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 15,
        data: lineItem.value.project[0].data,
        color: lineItem.value.chart_color,
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      })
      yAxiss.name = `${t('单位')}：${lineItem.value.unit}`
    } else {
      lineItem.value.project.forEach((item) => {
        seriess.push({
          name: item.title ? item.title : lineItem.value.title,
          barGap: 0,
          smooth: true,
          type: 'bar',
          barWidth: 15,
          data: item.data,
          color: item.color,
          animationDuration: 2800,
          animationEasing: 'cubicInOut'
        })
      })
      yAxiss.name = `${t('单位')}：${lineItem.value.unit}`
    }
  }
} else {
  const res = calculateFormula(
    lineItem.value.project.map((item) => {
      return item.data
    })
  )
  if (lineItem.value.chart_type == 'line') {
    seriess.push({
      name: lineItem.value.title,
      type: 'line',
      symbol: 'none',
      color: lineItem.value.chart_color,
      data: res,
      lineStyle: {
        width: 1
      }
    })
    yAxiss.name = `${t('单位')}：${lineItem.value.unit}`
  } else if (lineItem.value == 'bar') {
    seriess.push({
      name: lineItem.value.title,
      barGap: 0,
      smooth: true,
      type: 'bar',
      barWidth: 15,
      data: res,
      color: lineItem.value.chart_color,
      animationDuration: 2800,
      animationEasing: 'cubicInOut'
    })
    yAxiss.name = `${t('单位')}：${lineItem.value.unit}`
  }
}
yAxis.value = yAxiss
series.value = seriess
const options = computed(() => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      type: 'plain',
      textStyle: {
        fontSize: 12,
        fontWeight: 400
        // color: 'rgba(0, 0, 0, .9)',
        // lineHeight: 18
      },
      itemGap: 30,
      top: 5
    },
    grid: {
      left: '3%',
      right: '2%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      axisLine: {
        lineStyle: {
          // color: '#fff'
        }
      },
      axisTick: {
        //y轴刻度线
        show: true
      },
      splitLine: {
        //分割线
        show: true, //控制分割线是否显示
        lineStyle: {
          //分割线的样式
          color: 'rgba(81, 82, 85, 0.3)',
          width: 1,
          type: 'solid'
        }
      },
      type: 'category',
      boundaryGap: false,
      data: times.value
    },
    yAxis: yAxis.value,
    dataZoom: {
      type: 'inside'
    },
    series: series.value
  }
})
</script>

<template>
  <v-card-title> {{ lineItem.title }} </v-card-title>
  <div style="height: 86%" class="mr-4 ml-1 mt--15px">
    <template v-if="!isShowEmpty">
      <BaseEchart
        width="100%"
        height="100%"
        :options="options"
        ref="myChart"
      ></BaseEchart>
    </template>
    <template v-else>
      <v-empty-state :headline="$t('无数据')"></v-empty-state>
    </template>
  </div>
</template>

<style lang="scss" scoped></style>
