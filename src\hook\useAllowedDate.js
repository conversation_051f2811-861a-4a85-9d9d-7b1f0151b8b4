import { toRefs } from 'vue'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'
import dayjs from '@/utils/date'

export const useAllowedDate = () => {
  const { localTime } = toRefs(useConfigStore())
  const {
    snackbar,
    snackbarText
  } = toRefs(useGlobalStore())
  const allowedDates = (val) => Date.parse(val) < dayjs(localTime.value).valueOf()
  const getLocalTime = async () => {
    const res = await useConfigStore().getLocalTimeFn()
    if (res?.msg) {
      snackbar.value = true
      snackbarText.value = res.msg
    }
  }
  getLocalTime()

  return {
    allowedDates
  }
}