/* 导入HMI优化样式 */
@import './assets/styles/hmi.css';

:root {
  font-family: Open Sans, Inter,
    sans-serif,
    -apple-system,
    blinkmacsystemfont,
    Segoe UI,
    roboto,
    Helvetica Neue,
    arial,
    sans-serif,
    "Apple Color Emoji",
    "Segoe UI Emoji",
    Segoe UI Symbol !important;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

html {
  overflow: hidden !important;
}

body {
  margin: 0;
  display: flex;
  place-items: center;
  min-height: 100vh;
}

#app {
  width: 100%;
  height: 100vh;
}

.rounded-ls {
  border-radius: 16px !important;
}

.line1 {
  white-space: nowrap;
  /* 确保文本不会换行 */
  overflow: hidden;
  /* 隐藏超出容器的文本 */
  text-overflow: ellipsis;
  /* 当文本超出容器时显示省略号 */
}

.color-primary {
  color: #0093b6;
}

.no-scrollbar::-webkit-scrollbar {
  height: 0;
  width: 0;
}

::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background-color: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background-color: #c0c0c0;
  border-radius: 3px;
}

.el-popper {
  z-index: 200343 !important;
}


/* .el-cascader-node.in-active-path, .el-cascader-node.is-active, .el-cascader-node.is-selectable.in-checked-path {
  color: #000 !important;
}
.el-checkbox__input.is-checked .el-checkbox__inner {
  background-color: #000 !important;
  border-color: #000 !important;
}
.el-checkbox__input.is-indeterminate .el-checkbox__inner {
  background-color: #000 !important;
  border-color: #000 !important;
} */

/* .start-date .el-date-table-cell__text {
   background-color: #000 !important;
 }

.end-date .el-date-table-cell__text {
   background-color: #000 !important;
 } */

.v-menu {
  z-index: 4000003 !important;
}