<!-- LogTerminal.vue -->
<script setup>
import {
  onMounted,
  onBeforeUnmount,
  watch,
  ref,
  toRefs,
  computed,
  nextTick
} from 'vue'
import { Terminal } from '@xterm/xterm'
import '@xterm/xterm/css/xterm.css'
import { FitAddon } from '@xterm/addon-fit'
import WebSocketClient from '@/utils/websocket'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'
import { useI18n } from 'vue-i18n'
import { throttle } from 'lodash-es'

const { snackbar, snackbarText } = toRefs(useGlobalStore())
const { logData, logWs } = toRefs(useConfigStore())
const { t } = useI18n()

// 定义模型，接收日志消息数组
const terminal = ref(null)
const fitAddon = ref(null)
const isUserScrolling = ref(false)
const lastRow = ref(0)

// 初始化
onMounted(() => {
  webSocketInit()
  initTerminal()

  const container = document.getElementById('terminal-container')
  if (container) {
    const resizeObserver = new ResizeObserver(() => {
      if (fitAddon.value) {
        fitAddon.value.fit() // 自动适配新尺寸
      }
    })
    resizeObserver.observe(container)

    // 存储 observer 以便销毁
    window.__xtermResizeObserver = resizeObserver
  }
})

/**
 * 初始化终端
 */
const initTerminal = () => {
  terminal.value = new Terminal({
    fontSize: 14,
    theme: {
      background: '#000000',
      foreground: '#ffffff',
      black: '#000000',
      red: '#ff5555',
      green: '#50fa7b',
      yellow: '#f1fa8c',
      blue: '#8be9fd',
      magenta: '#ff79c6',
      cyan: '#8be9fd',
      white: '#bbbbbb'
    },
    lineHeight: 1,
    scrollback: 20000, // 支持大量日志
    convertEol: true // 自动换行
  })

  fitAddon.value = new FitAddon()
  // terminal.value.loadAddon(fitAddon.value)
  fitAddon.value.activate(terminal.value)

  terminal.value.open(document.getElementById('terminal-container'))
  fitAddon.value.fit()

  // 监听滚动事件
  terminal.value.element?.addEventListener('scroll', handleScroll)
}

/**
 * 初始化webSocket
 */
const logModuleOptions = ref([])
const defaultLevel = ref()
// 日志等级
const logLevelData = ref([
  {
    value: 0,
    title: 'trace'
  },
  {
    value: 1,
    title: 'debug'
  },
  {
    value: 2,
    title: 'info'
  },
  {
    value: 3,
    title: 'warn'
  },
  {
    value: 4,
    title: 'error'
  },
  {
    value: 5,
    title: 'critical'
  }
])

// 新增状态变量
const refreshing = ref(false)

// 计算属性
const allModulesEnabled = computed(() => {
  return (
    logModuleOptions.value.length > 0 &&
    logModuleOptions.value.every((item) => item.enabled)
  )
})

// 获取连接状态相关方法
const getConnectionStatus = () => {
  if (!logWs.value) return t('未连接')
  return logWs.value.isConnected ? t('已连接') : t('未连接')
}

const getConnectionColor = () => {
  if (!logWs.value) return 'grey'
  return logWs.value.isConnected ? 'success' : 'grey'
}

const getConnectionIcon = () => {
  if (!logWs.value) return 'mdi-wifi-off'
  return logWs.value.isConnected ? 'mdi-wifi' : 'mdi-wifi-sync'
}

// 新增方法
const refreshStatus = () => {
  refreshing.value = true
  try {
    getModuleList()
    getCurrentStatus()
  } finally {
    refreshing.value = false
  }
}

const toggleAllModules = () => {
  const targetState = !allModulesEnabled.value
  logModuleOptions.value.forEach((item) => {
    if (item.enabled !== targetState) {
      item.enabled = targetState
      changeModule(targetState, item.name)
    }
  })
}

const clearTerminal = () => {
  if (terminal.value) {
    terminal.value.clear()
    logData.value = []
    lastRow.value = 0
  }
}
const webSocketInit = () => {
  let host = location.host
  let url = ''
  if (import.meta.env.MODE == 'development') {
    url = '/ws/realtime/logs'
  } else {
    url = `ws://${host}/api/realtime/logs`
  }

  if (logWs.value) logWs.value.close()
  logWs.value = new WebSocketClient(url, {
    isHeartbeat: false
  })

  logWs.value.connect()

  logWs.value.on('open', () => {
    snackbar.value = true
    snackbarText.value = t('建立连接')
    // 获取初始状态
    setTimeout(() => {
      getModuleList()
      getCurrentStatus()
    }, 100)
  })

  logWs.value.on('message', (event) => {
    try {
      let data = JSON.parse(event.data)

      let keys = Object.keys(data)

      // 处理模块列表响应
      if (keys.includes('modules') && !keys.includes('defaultLevel')) {
        logModuleOptions.value = data.modules.map((item) => ({
          name: item,
          title: item,
          enabled: false,
          level: defaultLevel.value || 2
        }))
        return
      }

      // 处理状态响应
      if (keys.includes('modules') && keys.includes('defaultLevel')) {
        defaultLevel.value = data.defaultLevel
        logModuleOptions.value = data.modules.map((moduleItem) => ({
          name: moduleItem.name,
          title: moduleItem.name,
          enabled: moduleItem.enabled || false,
          level:
            moduleItem.level !== undefined
              ? moduleItem.level
              : defaultLevel.value
        }))
        return
      }

      // 处理旧格式兼容
      if (data.logLevelSet) {
        defaultLevel.value = data.logLevelSet.logLevel
        return
      }
    } catch (error) {
      // 非JSON数据，当作日志处理
      logData.value.push(event.data)
    }
  })

  logWs.value.on('close', () => {
    snackbar.value = true
    snackbarText.value = t('连接关闭')
  })

  logWs.value.on('error', (error) => {
    snackbar.value = true
    snackbarText.value = t('连接出错啦') + ': ' + error
  })
}
// 获取模块列表
const getModuleList = () => {
  logWs.value.sendMessage(JSON.stringify({ cmd: 'list-modules' }))
}
// 获取当前状态（会话维度）
const getCurrentStatus = () => {
  logWs.value.sendMessage(JSON.stringify({ cmd: 'status' }))
}
// 启用/禁用某个模块（仅当前会话）
const changeModule = (isEnable, moduleName) => {
  logWs.value.sendMessage(
    JSON.stringify({
      cmd: isEnable ? 'enable' : 'disable',
      module: moduleName
    })
  )
}
// 设置某个模块的会话级别（仅当前会话）
const setModuleLevel = (level, moduleName) => {
  if (level !== null && level !== undefined && moduleName) {
    logWs.value.sendMessage(
      JSON.stringify({ cmd: 'set', module: moduleName, level })
    )
  }
}
// 设置当前会话的默认等级（仅当前会话）
const setDefaultLevel = (level) => {
  if (level !== null && level !== undefined) {
    logWs.value.sendMessage(JSON.stringify({ cmd: 'set-default', level }))
  }
}
// 监听 model 变化，添加新日志
// 节流函数
const throttledWrite = throttle((logs) => {
  if (!terminal.value) return
  const text = logs.join('')
  terminal.value.write(text)
}, 100)

// 监听日志
watch(
  () => logData.value,
  (logs) => {
    if (!terminal.value) return

    const newLogs = logs.slice(lastRow.value)
    if (newLogs.length === 0) return

    lastRow.value = logs.length

    // 批量节流写入
    // throttledWrite(newLogs)
    const text = newLogs.join('')
    terminal.value.write(text)

    // 只有用户在底部时才滚动
    if (!isUserScrolling.value) return
    terminal.value.scrollToBottom()
  },
  { deep: true }
)
// 处理滚动事件
function handleScroll() {
  const term = terminal.value
  if (!term) return

  // 当前滚动位置
  const scrollTop = term.element?.firstChild.scrollTop
  const scrollHeight = term.element?.firstChild.scrollHeight
  const clientHeight = term.element?.clientHeight

  // 计算是否接近底部（允许 10px 容差）
  const isAtBottom = scrollHeight - scrollTop - clientHeight < 10

  isUserScrolling.value = !isAtBottom
}

onBeforeUnmount(() => {
  if (window.__xtermResizeObserver) {
    window.__xtermResizeObserver.disconnect()
    delete window.__xtermResizeObserver
  }
  if (fitAddon.value) fitAddon.value.dispose()
  if (terminal.value) terminal.value?.dispose()
})

// 添加控制面板显示状态
const showControlPanel = ref(true)

const handleSelectFocus = () => {
  nextTick(() => {
    const overlay = document.querySelector('.v-menu')
    overlay.style.zIndex = '4000002'
  })
}
</script>

<template>
  <div class="log-container h-100 flex flex-row-reverse">
    <!-- 控制面板 -->
    <v-card
      v-show="showControlPanel"
      class="ml-4 log-control h-100 overflow-auto"
      elevation="2"
    >
      <v-card-title class="py-3 flex align-center">
        <v-icon icon="mdi-cog" class="mr-2"></v-icon>
        {{ $t('日志控制面板') }}
        <v-spacer></v-spacer>
        <v-btn
          icon="mdi-close"
          size="small"
          variant="text"
          @click="showControlPanel = false"
        ></v-btn>
      </v-card-title>
      <div class="px-4 pb-1">
        <!-- 默认日志等级 -->
        <!-- <div class="mb-3 flex w-100">
          <v-select
            v-model="defaultLevel"
            item-title="title"
            item-value="value"
            clearable
            :label="$t('会话默认日志等级')"
            :placeholder="$t('选择默认等级')"
            :items="logLevelData"
            variant="outlined"
            density="compact"
            hide-details
            @update:modelValue="setDefaultLevel"
            class="w-50 mr-2"
            prepend-icon="mdi-tune"
          />
        </div> -->

        <!-- 模块控制 -->
        <div class="mb-2">
          <div class="text-subtitle-2 mb-2 d-flex align-center">
            <v-icon icon="mdi-view-module" size="small" class="mr-1"></v-icon>
            {{ $t('模块控制') }}
            <v-spacer></v-spacer>
            <v-btn
              size="x-small"
              variant="text"
              @click="toggleAllModules"
              class="text-caption"
            >
              {{ allModulesEnabled ? $t('全部禁用') : $t('全部启用') }}
            </v-btn>
          </div>

          <div
            class="flex flex-wrap justify-between"
            v-if="logModuleOptions.length > 0"
          >
            <div
              v-for="item in logModuleOptions"
              :key="item.name"
              :class="{ 'border-primary': item.enabled }"
              class="px-4 pb-3 pt-0 mb-2 module-item rounded-lg border-2 cursor-pointer border-dark border-solid w-100"
            >
              <v-switch
                v-model="item.enabled"
                :label="item.name"
                color="primary"
                density="compact"
                hide-details
                @update:modelValue="(val) => changeModule(val, item.name)"
                class="mb-2"
              ></v-switch>

              <v-select
                v-model="item.level"
                item-title="title"
                item-value="value"
                :label="$t('模块等级')"
                :items="logLevelData"
                variant="outlined"
                density="compact"
                hide-details
                :disabled="!item.enabled"
                @update:modelValue="(val) => setModuleLevel(val, item.name)"
                class="log-select"
                @update:focused="handleSelectFocus"
              >
              </v-select>
            </div>
          </div>
          <v-alert
            v-else
            type="info"
            variant="tonal"
            density="compact"
            class="text-caption"
          >
            {{ $t('暂无可用模块，请检查连接状态') }}
          </v-alert>
        </div>
      </div>
    </v-card>

    <!-- 日志终端 -->
    <v-card elevation="2" class="log-terminal-card">
      <v-card-title class="py-3 d-flex align-center">
        <v-icon icon="mdi-console" class="mr-2"></v-icon>
        {{ $t('实时日志') }}
        <v-chip
          :color="getConnectionColor()"
          variant="elevated"
          size="small"
          class="ml-2"
        >
          <v-icon start :icon="getConnectionIcon()"></v-icon>
          {{ getConnectionStatus() }}
        </v-chip>
        <v-spacer></v-spacer>

        <!-- 控制面板切换按钮 -->
        <v-btn
          :icon="showControlPanel ? 'mdi-chevron-right' : 'mdi-chevron-left'"
          size="small"
          variant="text"
          @click="showControlPanel = !showControlPanel"
          class="mr-2"
        >
        </v-btn>
        <v-btn
          icon="mdi-refresh"
          size="small"
          variant="text"
          @click="refreshStatus"
          class="mr-2"
          :loading="refreshing"
        ></v-btn>
        <v-btn
          icon="mdi-delete-sweep"
          size="small"
          variant="text"
          @click="clearTerminal"
        ></v-btn>
      </v-card-title>
      <v-divider></v-divider>
      <div class="log-terminal-container">
        <div id="terminal-container" class="terminal-wrapper"></div>
      </div>
    </v-card>
  </div>
</template>

<style scoped>
.log-control {
  width: 30%;
  transition: all 0.3s ease;
}

.log-terminal-card {
  flex: 1;
  display: flex;
  flex-direction: column;
  min-height: 0;
  transition: all 0.3s ease;
}

.log-terminal-container {
  flex: 1;
  border-radius: 0 0 8px 8px;
  overflow: hidden;
  background-color: #000;
}

.terminal-wrapper {
  width: 100%;
  height: 100%;
}

/* 隐藏 xterm 默认的滚动条 */
.xterm-viewport {
  scrollbar-width: none;
}

.xterm-viewport::-webkit-scrollbar {
  display: none;
}

.border-primary {
  border-color: rgb(var(--v-theme-primary)) !important;
  border-width: 2px !important;
}
</style>
