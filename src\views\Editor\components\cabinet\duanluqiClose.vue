<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-23 10:09:41
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String, // #C4EBAD
  backFill: String
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width="37.05804443359375"
    height="37.05804443359375"
    viewBox="0 0 37.05804443359375 37.05804443359375"
  >
    <defs>
      <clipPath id="master_svg0_280_5136">
        <rect
          x="0"
          y="0"
          width="37.05804443359375"
          height="37.05804443359375"
          rx="0"
        />
      </clipPath>
    </defs>
    <g clip-path="url(#master_svg0_280_5136)">
      <g>
        <rect
          x="13.48162841796875"
          y="5.77685546875"
          width="10.09478759765625"
          height="25.549684524536133"
          rx="0"
          :fill="props.backFill"
        />
      </g>
      <g>
        <path
          d="M23.57642841796875,5.7457799609375L18.96413841796875,5.7457799609375L18.96413841796875,0.6527099609375L18.09389841796875,0.6527099609375L18.09389841796875,5.7457799609375L13.48162841796875,5.7457799609375L13.48162841796875,31.2612099609375L18.09389841796875,31.2612099609375L18.09389841796875,36.3325099609375L18.96413841796875,36.3325099609375L18.96413841796875,31.2612099609375L23.57642841796875,31.2612099609375L23.57642841796875,5.7457799609375ZM22.70615841796875,30.3909099609375L14.35186741796875,30.3909099609375L14.35186741796875,6.6160199609375L22.70615841796875,6.6160199609375L22.70615841796875,30.3909099609375Z"
          :fill="props.fill"
          style="mix-blend-mode: passthrough"
        />
      </g>
    </g>
  </svg>
</template>
