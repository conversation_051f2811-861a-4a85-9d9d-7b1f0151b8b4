<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-07 10:23:28
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-25 08:51:06
 * @FilePath: \ems_manage\src\views\Analyse\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import {
  ref,
  computed,
  watch,
  toRefs,
  getCurrentInstance,
  onMounted
} from 'vue'
import dayjs from '@/utils/date'
import { useStatisticsStore } from '@/store/module/statistics'
import { useDeviceStore } from '@/store/module/device'
import { useGlobalStore } from '@/store/global'
import { useI18n } from 'vue-i18n'
import { useDisplay } from 'vuetify'
import { useAllowedDate } from '@/hook/useAllowedDate'
import { debounce } from 'lodash-es'

import LineEchart from './lineEchart.vue'
import { ElTree, ElLoading } from 'element-plus'

const { allowedDates } = useAllowedDate()
const { proxy } = getCurrentInstance()
const { mobile } = useDisplay()
const { t } = useI18n()
const { dataQueryInfo } = toRefs(useStatisticsStore())
const { treeData } = toRefs(useDeviceStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())

useDeviceStore().getDeviceListFn()

/**
 * 设备
 */
const search = ref(null)
const caseSensitive = ref(false)
watch(search, (val) => {
  proxy.$refs.treeRef.filter(val)
})

const filterNode = (value, data) => {
  const searchValue = caseSensitive.value ? value : value?.toLowerCase()
  if (!searchValue) return true
  return data.title.includes(searchValue)
}

/**
 * 多选
 */
const isEmpty = ref(true)
const tableData = ref([])
const currentPointArr = ref([])
const getLoading = ref()
const noBtRequestParam = ref([])
const getResultFn = (list) => {
  let listData = list.filter((item) => {
    let keys = Object.keys(item)
    let isModule = keys.includes('module')
    return !isModule || (isModule && !item.module)
  })
  paramNum.value = listData.length
  if (paramNum.value === 0) {
    snackbar.value = true
    snackbarText.value = t('请选择参数')
    return
  }
  tableData.value = []
  currentPointArr.value = []
  let btRequest = []
  let btRequestParam = []
  noBtRequestParam.value = []
  getLoading.value = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  listData.forEach((proItem) => {
    noBtRequestParam.value.push({
      ...proItem,
      device_id: proItem.device_id,
      point_id: proItem.id,
      date: dataQueryInfo.value.date
    })
  })
  // 具体请求
  Promise.all([
    useStatisticsStore().getPointDataFn(
      JSON.stringify(
        noBtRequestParam.value.map((item) => {
          return {
            device_id: item.device_id,
            point_id: item.id,
            date: item.date
          }
        })
      )
    )
  ])
    .then((results) => {
      // 所有请求都成功完成
      console.log('All requests completed successfully:', results)
      // 在这里执行其他操作
      let arr = results.filter(
        (item) =>
          !item?.datas?.length &&
          !item?.times?.length &&
          !item?.tableData?.length
      )
      if (arr.length == results.length) isEmpty.value = true
      else isEmpty.value = false
      results.forEach((item, resIndex) => {
        if (item?.noBt == 1) {
          let title = null
          if (item?.tableData.length) {
            item?.tableData.forEach((item1, index1) => {
              let param = noBtRequestParam.value.find(
                (param) =>
                  param.id == item.pointId[index1].pointId &&
                  param.device_name == item.pointId[index1].deviceName
              )
              let title = `${param.device_name}${
                param.module_name !== null ? '_' + param.module_name + '_' : '_'
              } ${param.title}`
              tableData.value.push({
                ...item1,
                title
              })
            })
          }
          if (item?.datas.length) {
            item?.datas.forEach((item2, index2) => {
              let param = noBtRequestParam.value.find(
                (param) =>
                  param.id == item.pointId[index2].pointId &&
                  param.device_name == item.pointId[index2].deviceName
              )
              currentPointArr.value.push({
                ...param,
                datas: item2,
                times: item?.times[index2],
                unit: item.pointId[index2].unit
              })
            })
          }
        } else {
          // 表格数据
          let param = btRequestParam.value.find(
            (param) =>
              param.point_id == item.pointId.pointId &&
              param.device_id == item.pointId.deviceId
          )
          let title = `${param.device_name}${
            param.clusterName !== null ? '_' + param.clusterName : '_'
          }${param.packName !== null ? '_' + param.packName : ''}${
            param.pointName !== null ? '_' + param.pointName : '_'
          } ${param.title}`
          tableData.value.push({
            ...(item?.tableData.length ? item.tableData[0] : []),
            title
          })
          currentPointArr.value.push({
            ...param,
            datas: item?.datas,
            times: item?.times
          })
        }
      })
      getLoading.value.close()
    })
    .catch((error) => {
      isEmpty.value = true
      // 如果任何一个请求失败，catch 会被触发
      console.error('One or more requests failed:', error)
      getLoading.value.close()
    })
}
const defaultCheckedKeys = ref([])
const clickDeal = (currentObj, treeStatus) => {
  let listData = treeStatus.checkedNodes.filter((item) => {
    let keys = Object.keys(item)
    let isModule = keys.includes('module')
    return !isModule || (isModule && !item.module)
  })
  paramNum.value = listData.length
  // console.log(treeStatus.checkedNodes)
  // getResultFn(treeStatus.checkedNodes)
  // // 用于：父子节点严格互不关联时，父节点勾选变化时通知子节点同步变化，实现单向关联。
  // let selected = treeStatus.checkedKeys.indexOf(currentObj?.id) // -1未选中
  // // 选中
  // if (selected !== -1) {
  //   // 子节点只要被选中父节点就被选中
  //   // selectedParent(currentObj)
  //   // 统一处理子节点为相同的勾选状态
  //   uniteChildSame(currentObj, true)
  // } else {
  //   // 未选中 处理子节点全部未选中
  //   if (currentObj?.children?.length !== 0) {
  //     uniteChildSame(currentObj, false)
  //   }
  // }
}
// 统一处理子节点为相同的勾选状态
const uniteChildSame = (treeList, isSelected) => {
  proxy.$refs.treeRef.setChecked(treeList.id, isSelected, true)
  for (let i = 0; i < treeList?.children?.length; i++) {
    uniteChildSame(treeList?.children[i], isSelected)
  }
}
// 统一处理父节点为选中
const selectedParent = (currentObj) => {
  let currentNode = proxy.$refs.treeRef.getNode(currentObj)
  if (currentNode.parent.key !== undefined) {
    proxy.$refs.treeRef.setChecked(currentNode.parent, true, true)
    selectedParent(currentNode.parent)
  }
}
const clusterData = ref([])
const loadNode = (node, resolve) => {
  let keys = Object.keys(node.data)
  let isModule = keys.includes('module')
  if (node.level == 0) {
    return resolve([
      {
        id: 0,
        title: t('采集器')
      }
    ])
  }
  if (node.level == 1) {
    return resolve(
      treeData.value.map((item) => {
        return {
          ...item,
          children: item.children ? item.children : []
        }
      })
    )
  }
  // 不是电池
  if (isModule && !node.data.module) {
    useStatisticsStore()
      .getTreePointDataFn({ deviceId: node.data.device_id })
      .then((res) => {
        resolve(res)
      })
  } else if (isModule && node.data.module) {
    return resolve(node.data.children)
  } else {
    resolve([])
  }
}
const customNodeClass = (data) => {
  if (data?.leaf == undefined) {
    return 'no-check'
  } else {
    return null
  }
}

const date = ref([])
const formatDate = ref()
const isShowDate = ref(false)
date.value = new Date()
formatDate.value = dayjs(date.value).format('YYYY-MM-DD')
dataQueryInfo.value.date = formatDate.value
const handleDateChange = (e) => {
  let checkData = proxy.$refs.treeRef.getCheckedNodes()
  if (checkData.length == 0) {
    snackbar.value = true
    snackbarText.value = t('请先选择一个正确的设备')
    return
  }
  formatDate.value = dayjs(e).format('YYYY-MM-DD')
  isShowDate.value = false
  dataQueryInfo.value.date = formatDate.value
  getResultFn(checkData)
}

const headers = ref([
  { title: t('数据名称'), align: 'center', key: 'title' },
  {
    title: t('最大值'),
    align: 'center',
    children: [
      {
        title: t('数值'),
        align: 'center',
        key: 'max'
      },
      {
        title: t('发生日期'),
        align: 'center',
        key: 'maxTime'
      }
    ]
  },
  {
    title: t('最小值'),
    align: 'center',
    children: [
      {
        title: t('数值'),
        align: 'center',
        key: 'min'
      },
      {
        title: t('发生日期'),
        align: 'center',
        key: 'minTime'
      }
    ]
  }
])

/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value) => {
  if (isShowKeyboard.value) return
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  search.value = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}

/**
 * 抽屉
 */
const drawer = ref(true)
drawer.value = mobile.value ? false : true
/**
 * 导出
 */
const dialog = ref(false)
const form = ref({})
const isShowDiaDate = ref(false)
const handleDateDiaChange = (e) => {
  form.value.date = dayjs(e).format('YYYY-MM-DD')
  isShowDiaDate.value = false
}
const handleExport = () => {
  form.value = {
    deviceId: undefined,
    clusterId: undefined,
    date: dayjs(date.value).format('YYYY-MM-DD')
  }
  isBT.value = false
  dialog.value = true
}
const handleCancelDiaClick = () => {
  form.value = {
    deviceId: undefined,
    clusterId: undefined,
    date: dayjs(date.value).format('YYYY-MM-DD'),
    formatDate: dayjs(date.value).format('YYYY-MM-DD')
  }
  isBT.value = false
  dialog.value = false
}
const loading = ref(false)
const clusterExportList = ref([])
const getClusterExportListFn = () => {
  useStatisticsStore()
    .batteryClusterFn({
      deviceId: form.value.deviceId
    })
    .then((res) => {
      res.forEach((item) => {
        if (item.cluster_name.indexOf('#') != -1) {
          let id = item.cluster_name.split('#')[0]
          item.id = id
        } else {
          item.id =
            item.cluster_name == 'Container' || item.cluster_name == '集装箱'
              ? 12
              : 11
        }
      })
      clusterExportList.value = res
    })
}
const isBT = ref(false)
const handleDeviceExportChange = () => {
  let value = treeData.value.find((item) => item.id == form.value.deviceId)
  if (value.device_class == 'BMSDevice' || value.device_class == 'BAUDevice') {
    isBT.value = true
    getClusterExportListFn()
  } else {
    isBT.value = false
  }
}
const handleConfirmDiaClick = () => {
  if (!form.value.deviceId) {
    snackbar.value = true
    snackbarText.value = t('请选择设备')
    return
  }
  if (!form.value.date) {
    snackbar.value = true
    snackbarText.value = t('请选择日期')
    return
  }
  loading.value = true
  let value = treeData.value.find((item) => item.id == form.value.deviceId)
  useStatisticsStore()
    .analyseExportFn({
      deviceId: form.value.deviceId,
      date: form.value.date,
      deviceName: value.title,
      clusterId: form.value.clusterId
    })
    .then((res) => {
      loading.value = false
      dialog.value = false
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}

/**
 * 刷新
 */
const handleRefresh = () => {
  let checkData = proxy.$refs.treeRef.getCheckedNodes()
  getResultFn(checkData)
}
const refreshChange = debounce(handleRefresh, 200)

/**
 * 清空、确认
 */
const handleClearClick = () => {
  paramNum.value = 0
  tableData.value = []
  currentPointArr.value = []
  proxy.$refs.treeRef.setCheckedKeys([])
  isEmpty.value = true
}
const paramNum = ref(0)
const handleConfirmClick = () => {
  let checkData = proxy.$refs.treeRef.getCheckedNodes()
  getResultFn(checkData)
}

/**
 * 移动端
 */
watch(mobile, () => {
  drawer.value = mobile.value ? false : true
})
onMounted(() => {
  document.addEventListener('click', function (event) {
    if (!mobile.value) return
    let overlay = document.querySelector('.tree-display')
    let selectBtn = document.querySelector('.select-device-btn')
    // 检查点击是否发生在#overlay元素之外
    if (
      !overlay?.contains(event.target) &&
      !selectBtn?.contains(event.target)
    ) {
      drawer.value = false
    }
  })
})
</script>

<template>
  <div
    class="pa-4 h-100 overflow-hidden d-flex"
    style="flex-direction: row-reverse; position: relative"
  >
    <v-col
      cols="12"
      lg="3"
      md="3"
      sm="3"
      xs="3"
      class="h-100 tree"
      :class="[mobile ? 'tree-display' : 'tree']"
      v-show="drawer"
    >
      <v-card
        elevation="4"
        class="h-full relative"
        :class="[!mobile && 'rounded-lg']"
      >
        <div class="d-flex justify-between align-center px-4 py-2">
          <div class="text-h6">{{ $t('设备列表') }}</div>
        </div>
        <v-sheet class="px-4 bg-primary-lighten-2">
          <v-text-field
            v-model="search"
            clear-icon="mdi-close-circle-outline"
            :label="$t('搜索')"
            clearable
            dark
            flat
            hide-details
            solo-inverted
            @click:control="handleShow($event, search)"
          ></v-text-field>
          <v-checkbox
            v-model="caseSensitive"
            :label="$t('区分大小写搜索')"
            dark
            hide-details
          ></v-checkbox>
        </v-sheet>
        <el-tree
          node-key="id"
          ref="treeRef"
          :props="{
            children: 'children',
            label: 'title',
            isLeaf: 'leaf'
          }"
          :default-expanded-keys="[0]"
          :default-checked-keys="defaultCheckedKeys"
          :show-checkbox="true"
          @check="clickDeal"
          :load="loadNode"
          lazy
          class="h-80% overflow-auto pl-4"
          expand-on-click-node
          :indent="25"
          :filter-node-method="filterNode"
        >
        </el-tree>
        <div class="tree-btn flex justify-center align-center">
          <v-btn
            height="48px"
            class="mr-4"
            color="primary"
            @click="handleClearClick"
            >{{ $t('清空') }}</v-btn
          >
          <v-btn height="48px" color="primary" @click="handleConfirmClick"
            >{{ $t('确认') }}({{ paramNum }})</v-btn
          >
        </div>
      </v-card>
    </v-col>
    <div class="tree-overlay" v-if="mobile && drawer"></div>
    <v-col cols="12" lg="9" md="12" sm="12" xs="12" class="h-100">
      <v-card elevation="4" class="rounded-lg h-100 overflow-y-auto pb-4">
        <div class="d-flex justify-between align-center flex-wrap px-4 py-2">
          <div class="text-h6">{{ $t('数据分析') }}</div>
          <div class="d-flex align-center flex-wrap">
            <v-btn
              height="48px"
              class="mr-4 select-device-btn"
              color="primary"
              @click="drawer = !drawer"
              v-if="mobile"
              >{{ $t('选择设备') }}</v-btn
            >
            <v-menu
              v-model="isShowDate"
              location="bottom"
              :close-on-content-click="false"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  v-model="formatDate"
                  prepend-inner-icon="mdi-calendar-range"
                  label="Date"
                  variant="outlined"
                  hide-details
                  single-line
                  v-bind="props"
                  style="width: 200px"
                ></v-text-field>
              </template>

              <v-date-picker
                v-model="date"
                show-adjacent-months
                :allowed-dates="allowedDates"
                color="primary"
                elevation="4"
                class="date-picker"
                @update:modelValue="handleDateChange"
              ></v-date-picker>
            </v-menu>
            <v-btn
              height="48px"
              class="ml-4"
              color="primary"
              @click="handleExport"
              >{{ $t('导出报表') }}</v-btn
            >
            <v-btn
              height="48px"
              class="ml-4"
              color="primary"
              @click="refreshChange"
              >{{ $t('刷新') }}</v-btn
            >
          </div>
        </div>
        <template v-if="!isEmpty">
          <LineEchart style="height: 60%" :lineData="currentPointArr" />
          <v-card elevation="0" class="px-2 mx-4 mt-8">
            <v-data-table
              border
              :headers="headers"
              :items="tableData"
              hide-default-footer
            ></v-data-table>
          </v-card>
        </template>
        <empty v-else />
      </v-card>
    </v-col>

    <v-dialog v-model="dialog" width="auto">
      <v-card width="540" class="pa-4 rounded-lg">
        <v-card-title class="text-center mb-6">{{
          $t('导出报表')
        }}</v-card-title>
        <v-select
          v-model="form.deviceId"
          item-value="id"
          clearable
          :label="$t('选择设备')"
          :items="treeData"
          variant="outlined"
          class="w-100 mr-4 mt-2"
          @update:modelValue="handleDeviceExportChange"
        ></v-select>
        <v-select
          v-model="form.clusterId"
          item-value="id"
          item-title="cluster_name"
          clearable
          :label="$t('选择模块')"
          :items="clusterExportList"
          variant="outlined"
          class="w-100 mr-4 mt-2"
          v-if="isBT"
        ></v-select>
        <v-menu
          v-model="isShowDiaDate"
          location="bottom"
          :close-on-content-click="false"
        >
          <template v-slot:activator="{ props }">
            <v-text-field
              v-model="form.date"
              prepend-inner-icon="mdi-calendar-range"
              label="Date"
              variant="outlined"
              hide-details
              single-line
              v-bind="props"
              class="my-6"
            ></v-text-field>
          </template>

          <v-date-picker
            v-model="form.formatDate"
            show-adjacent-months
            :allowed-dates="allowedDates"
            color="primary"
            elevation="4"
            class="date-picker"
            @update:modelValue="handleDateDiaChange"
          ></v-date-picker>
        </v-menu>
        <div class="d-flex justify-center">
          <v-btn
            class="mt-2 mr-4 px-8"
            height="50"
            @click="handleCancelDiaClick"
            >{{ $t('取消') }}</v-btn
          >
          <v-btn
            class="mt-2 px-8"
            height="50"
            :loading="loading"
            color="primary"
            @click="handleConfirmDiaClick"
            >{{ $t('确定') }}</v-btn
          >
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.v-table) {
  table {
    border-collapse: collapse;
  }
  td,
  th {
    border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  }
}
:deep(.v-input__details) {
  display: none;
}

:deep(.el-tree) {
  --el-tree-node-content-height: 48px;
  --el-tree-node-hover-bg-color: #f6f6f6;
  --el-tree-text-color: #333;
  --el-tree-expand-icon-color: #333;
  color: #333;
  font-size: 16px;
  height: calc(80% - 70px);
  .el-tree-node__expand-icon {
    font-size: 16px;
  }
  .el-tree-node__content {
    /* padding: 4px 16px !important; */
  }
  .el-checkbox__inner:hover {
    border-color: #5e5e5e;
  }
  .el-checkbox__inner {
    border: 2px solid #5e5e5e;
    height: 17px;
    width: 17px;
  }
  .el-checkbox__input.is-checked .el-checkbox__inner {
    background-color: #333;
    border-color: #333;
  }
  .el-checkbox__inner:after {
    height: 8px;
    left: 5px;
    width: 4px;
  }
  .no-check .el-tree-node__expand-icon + .el-checkbox {
    display: none !important;
  }
  .no-check .is-leaf + .el-checkbox {
    display: flex !important;
  }
  .el-checkbox__input.is-indeterminate .el-checkbox__inner {
    background-color: #333;
    border-color: #333;
  }
}
.tree-btn {
  height: 80px;
  border-top: 1px solid #d8dce5;
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
}

.tree {
  position: absolute;
  left: 10px;
  top: 16px;
  height: calc(100% - 2rem) !important;
}
.tree-display {
  height: 100% !important;
  left: 0;
  bottom: 0;
  top: 0;
  padding: 0;
  z-index: 2;
  width: 300px !important;
  transition-duration: 0.2s;
  transition-property: box-shadow, transform, visibility, width, height, left,
    right, top, bottom;
  transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);
}
.tree-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #000;
  opacity: 0.2;
  z-index: 1;
  transition: opacity 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}
</style>
