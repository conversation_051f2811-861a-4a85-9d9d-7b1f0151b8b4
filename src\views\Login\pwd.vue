<script setup>
import { ref, toRefs, getCurrentInstance, reactive, watch } from 'vue'
import { useUserStore } from '@/store/module/user'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useGlobalStore } from '@/store/global'

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const router = useRouter()
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const { userInfo } = toRefs(useUserStore())

const props = defineProps({
  typeText: {
    type: String,
    default: 'edit'
  }
})
watch(
  () => props.typeText,
  () => {
    form.value.user_name = type == 'edit' ? userInfo.value.user_name : ''
  }
)

const form = ref({
  user_name: userInfo.value.user_name,
  new_password: '',
  old_password: userInfo.value.password,
  confirmPassword: ''
})
const newPasswordRules = ref([(v) => !!v || t('密码必填')])
const confirmPasswordRules = ref([
  (v) => !!v || t('密码必填'),
  (v) => v == form.value.new_password || t('两次输入的密码不一致')
])

const loading = ref(false)
const submit = async () => {
  const { valid } = await proxy.$refs.editForm.validate()
  if (!valid) return
  loading.value = true
  let api = props.typeText == 'edit' ? 'editPwdFn' : 'resetPwdFn'
  let data =
    props.typeText == 'edit'
      ? {
          user_name: userInfo.value.user_name,
          new_password: form.value.new_password,
          old_password: userInfo.value.password
        }
      : {
          user_name: form.value.user_name,
          new_password: form.value.new_password
        }
  try {
    const res = await useUserStore()[api](data)
    snackbar.value = true
    snackbarText.value = props.typeText == 'edit' ? t('修改成功') : t('重置成功')
    loading.value = false
    if (props.typeText == 'edit') router.push('/login')
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
  }
}

/**
 * 眼睛
 */
const eyeObj = reactive({
  old: false,
  new: false,
  confirm: false
})
const handleEyeClick = (type) => {
  if (type == 'old') {
    eyeObj.old = !eyeObj.old
  } else if (type == 'new') {
    eyeObj.new = !eyeObj.new
  } else if (type == 'confirm') {
    eyeObj.confirm = !eyeObj.confirm
  }
}

/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value, prop) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  form.value[currentInput.value] = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}
</script>

<template>
  <v-card class="pa-4 w-100 rounded-lg no-scrollbar" elevation="4">
    <v-form fast-fail @submit.prevent="submit" ref="editForm">
      <v-text-field
        v-model="form.user_name"
        :label="$t('账号')"
        :readonly="typeText == 'edit'"
        :rules="typeText == 'edit' ? [] : [(v) => !!v || $t('账号必填')]"
        variant="outlined"
        class="mb-2"
        @click:control="handleShow($event, form.user_name, 'user_name')"
      ></v-text-field>

      <v-text-field
        v-model="userInfo.password"
        :label="$t('旧密码')"
        :type="eyeObj.old ? 'text' : 'password'"
        :append-inner-icon="eyeObj.old ? 'mdi-eye' : 'mdi-eye-closed'"
        readonly
        @click:appendInner="handleEyeClick('old')"
        variant="outlined"
        class="mb-2"
        v-if="typeText == 'edit'"
      ></v-text-field>
      <v-text-field
        v-model="form.new_password"
        :rules="newPasswordRules"
        :label="$t('新密码')"
        :clearable="true"
        :type="eyeObj.new ? 'text' : 'password'"
        :append-inner-icon="eyeObj.new ? 'mdi-eye' : 'mdi-eye-closed'"
        variant="outlined"
        class="mb-2"
        @click:appendInner="handleEyeClick('new')"
        @click:control="handleShow($event, form.new_password, 'new_password')"
      ></v-text-field>
      <v-text-field
        v-model="form.confirmPassword"
        :rules="confirmPasswordRules"
        :label="$t('确认新密码')"
        :clearable="true"
        :type="eyeObj.confirm ? 'text' : 'password'"
        :append-inner-icon="eyeObj.confirm ? 'mdi-eye' : 'mdi-eye-closed'"
        variant="outlined"
        class="mb-2"
        @click:appendInner="handleEyeClick('confirm')"
        v-if="typeText == 'edit'"
        @click:control="
          handleShow($event, form.confirmPassword, 'confirmPassword')
        "
      ></v-text-field>
      <div class="d-flex justify-center">
        <v-btn
          class="mt-2 px-8"
          type="submit"
          height="50"
          :loading="loading"
          color="primary"
          >{{ $t('确定') }}</v-btn
        >
      </div>
    </v-form>
  </v-card>
</template>

<style scoped></style>
