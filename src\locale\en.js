/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-02 15:41:22
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-21 15:23:52
 * @FilePath: \ems_manage\src\locale\en.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default {
  设备详情: 'Device Details',
  账号: 'Username',
  账号必填: 'Username required',
  密码必填: 'Password required',
  密码: 'Password',
  记住密码: 'Remember Pwd',
  登录: 'Log in',
  数据概括: 'Data Generalization',
  数据分析: 'Data Analysis',
  电量统计: 'Power Statistics',
  参数设置: 'Parameter Setting',
  策略管理: 'Policy Management',
  日志管理: 'Log Management',
  配置中心: 'Configuration Center',
  编辑图: 'Edit Diagram',
  退出: 'Logout',
  两次输入的密码不一致: 'The passwords you entered twice do not match',
  修改成功: 'Modification successful',
  重置成功: 'Reset Successful',
  重置密码: 'Reset Password',
  旧密码: 'Old Password',
  新密码: 'New Password',
  确认新密码: 'Confirm New Password',
  取消: 'Cancel',
  确定: 'Confirm',
  修改密码: 'Change Password',
  无数据: 'No data',
  采集器: 'Collector',
  设备参数获取失败: 'Failed to obtain device parameters',
  请先选择一个正确的设备: 'Please select a correct device first',
  数据名称: 'Data name',
  最大值: 'Maximum',
  数值: 'Numeric',
  发生日期: 'Date of occurrence',
  最小值: 'Minimum',
  设备: 'Device',
  参数: 'parameter',
  设备列表: 'Device List',
  搜索: 'Search',
  区分大小写搜索: 'Case sensitive search',
  导出报表: 'Exporting Reports',
  单位: 'unit',
  保存成功: 'Saved successfully',
  保存配置: 'Save Configuration',
  删除卡片: 'Delete Card',
  显示系统通讯拓扑图: 'Display system communication topology',
  结构拓扑图: 'Structural topology diagram',
  配置管理: 'Configuration Management',
  添加卡片: 'Add Card',
  删除: 'Delete',
  添加新卡片: 'Add New Card',
  标题必填: 'Title required',
  标题: 'Title',
  选择设备: 'Select Device',
  选择模块: 'Select Module',
  选择属性: 'Select Properties',
  选择图表类型: 'Select the chart type',
  折线图: 'Line chart',
  柱状图: 'Bar Chart',
  图表颜色: 'Chart Color',
  项目名称: 'Project Name',
  项目地址: 'Project Address',
  装机容量: 'Installed Capacity',
  装机功率: 'Installed Power',
  保存: 'Save',
  系统配置: 'System Configuration',
  首页配置: 'Home configuration',
  充放电信息: 'Charge and discharge information',
  今日放电量: "Today's discharge",
  累计放电量: 'Cumulative Discharge Capacity',
  今日充电量: "Today's charge",
  累计充电量: 'Cumulative Charge Capacity',
  告警信息: 'Alarm Information',
  系统信息: 'System Information',
  系统状态: 'System Status',
  运行: 'Running',
  并离网模式: 'On-grid and off-grid mode',
  通讯状态: 'Communication status',
  并网: 'Grid Connection',
  离网: 'Off-grid',
  在线: 'Online',
  离线: 'Offline',
  设备名称: 'Device Name',
  告警对象: 'Alarm Object',
  告警名称: 'Alarm Name',
  告警等级: 'Alarm level',
  发生时间: 'Occurrence Time',
  结束时间: 'End Time',
  操作: 'Actions',
  等级一: 'Level 1',
  等级二: 'Level 2',
  等级三: 'Level 3',
  已处理: 'Processed',
  未处理: 'Unprocessed',
  设备概览: 'Device Overview',
  故障告警: 'Fault Alarm',
  运行数据: 'Running Data',
  运行状态: 'Running Status',
  '1月': 'January',
  '2月': 'February',
  '3月': 'March',
  '4月': 'April',
  '5月': 'May',
  '6月': 'June',
  '7月': 'July',
  '8月': 'August',
  '9月': 'September',
  '10月': 'October',
  '11月': 'November',
  '12月': 'December',
  放电量: 'Discharge Capacity',
  充电量: 'Charge Capacity',
  日: 'Day',
  月: 'Month',
  年: 'Year',
  总电量: 'Total power',
  '00时': '00',
  '01时': '01',
  '02时': '02',
  '03时': '03',
  '04时': '04',
  '05时': '05',
  '06时': '06',
  '07时': '07',
  '08时': '08',
  '09时': '09',
  '10时': '10',
  '11时': '11',
  '12时': '12',
  '13时': '13',
  '14时': '14',
  '15时': '15',
  '16时': '16',
  '17时': '17',
  '18时': '18',
  '19时': '19',
  '20时': '20',
  '21时': '21',
  '22时': '22',
  '23时': '23',
  '01日': '01',
  '02日': '02',
  '03日': '03',
  '04日': '04',
  '05日': '05',
  '06日': '06',
  '07日': '07',
  '08日': '08',
  '09日': '09',
  '10日': '10',
  '11日': '11',
  '12日': '12',
  '13日': '13',
  '14日': '14',
  '15日': '15',
  '16日': '16',
  '17日': '17',
  '18日': '18',
  '19日': '19',
  '20日': '20',
  '21日': '21',
  '22日': '22',
  '23日': '23',
  '24日': '24',
  '25日': '25',
  '26日': '26',
  '27日': '27',
  '28日': '28',
  '29日': '29',
  '30日': '30',
  '31日': '31',
  参数类型: 'Parameter Type',
  参数值: 'Parameter Value',
  执行结果: 'Execution Results',
  操作人员: 'Operator',
  备注: 'Remark',
  操作日志: 'Operation Log',
  使能: 'Enable',
  不使能: 'Disable',
  开机: 'Power On',
  关机: 'Power Off',
  成功: 'Succeed',
  失败: 'Failed',
  下发成功: 'Issued Successfully',
  有功功率: 'Active Power',
  无功功率: 'Reactive Power',
  功率因数: 'Power Factor',
  防逆流: 'Reverse Power Protection',
  系统开关机: 'System Switch',
  下发: 'Send',
  查看日志: 'View logs',
  功率: 'Power',
  添加方案: 'Add plan',
  修改方案: 'Modification plan',
  至少要有一条哦: 'At least one',
  最多只能添加12条哦: 'You can only add up to 12 items.',
  执行成功: 'Execution Success',
  星期一: 'Monday',
  星期二: 'Tuesday',
  星期三: 'Wednesday',
  星期四: 'Thursday',
  星期五: 'Friday',
  星期六: 'Saturday',
  星期日: 'Sunday',
  策略模式: 'Strategy Pattern',
  削峰填谷: 'Peak Shaving',
  策略方式: 'Strategy',
  周: 'Week',
  策略配置: 'Policy Configuration',
  是否启用策略: 'Whether to enable the policy',
  启用: 'Enable',
  停止: 'Stop',
  策略方案: 'Strategic Plan',
  模式选择: 'Mode Selection',
  方案列表: 'Solution List',
  修改: 'Modify',
  放电: 'Discharge',
  充电: 'Charge',
  添加: 'Add',
  方案名称: 'Program Name',
  方案名称必填: 'Program Name required',
  时段: 'Work Shift',
  开始时间必填: 'Start time required',
  开始时间: 'Start time',
  结束时间必填: 'End time required',
  结束时间: 'End time',
  功率必填: 'Power required',
  添加成功: 'Added successfully',
  修改成功: 'Modify successfully',
  删除成功: 'Deleted successfully',
  相加: 'Addition',
  数据是否组合: 'Data combination or not',
  否: 'No',
  是: 'Yes',
  断开: 'Switch Off',
  闭合: 'Closure',
  正常: 'Normal',
  故障: 'Fault',
  无效: 'Invalid',
  满充: 'Fully Charged',
  满放: 'Fully Discharged',
  告警: 'Alarm',
  强充请求: 'Forced Charge Request',
  欠压保护: 'Undervoltage Protection',
  待机: 'Standby',
  制冷: 'Cooling',
  制热: 'Heating',
  风扇: 'Fan',
  除湿: 'Dehumidify',
  关闭: 'Closure',
  开启: 'Open',
  动作: 'Action',
  无: 'None',
  有: 'Have',
  打开: 'Open',
  可用: 'Available',
  不可用: 'Unavailable',
  高压已断开: 'High voltage disconnected',
  高压已连接: 'High voltage connected',
  需要: 'Need',
  不需要: 'Unnecessary',
  紧急停止: 'Emergency Stop',
  更多: 'More',
  用户管理: 'User Management',
  无告警: 'No Alarm',
  '恭喜，您的设备很安全。': 'Congratulations, your device is safe.',
  放大: 'Magnify',
  缩小: 'Minification',
  实时功率: 'Real-time Power',
  电流: 'Current',
  电压: 'Voltage',
  计量点电表: 'Meter At Metering Point',
  直流: 'DC',
  交流: 'AC',
  采样测点: 'Sampling Point',
  低压电网: 'Low-Voltage Grid',
  选择参数: 'Select Param',
  运行策略: 'Operation Strategy',
  并网模式: 'Grid-Connected Mode',
  备电SOC: 'Backup SOC',
  变压器容量: 'Transformer Capacity',
  电网充电功率: 'Grid Charging Power',
  离网模式: 'Off-Grid Mode',
  油机启动SOC: 'Diesel Engine Start SOC',
  油机充电使能: 'Diesel Engine Charging Enable',
  油机充电功率: 'Engine Charging Power',
  高压总开关: 'High Voltage Main Switch',
  油机开关: 'Diesel Generator Switch',
  '注：开关控制类，点击即触发！':
    'Note: Switch control class, click to trigger!',
  设备概括: 'Device Overview',
  设备总数: 'Total number of devices',
  告警总数: 'Total alarms',
  条: '',
  个: '',
  开: 'On',
  关: 'Off',
  隐藏: 'Hidden',
  隐藏键盘: 'Hidden Keyboard',
  显示键盘: 'Display Keyboard',
  返回: 'Back',
  已锁定大写: 'Locked',
  大写: 'Uppercase',
  切换大写: 'Switch Caps',
  小写: 'Lowercase',
  空格: 'Space',
  符号: 'Symbol',
  数字: '123',
  确认: 'Confirm',
  最高单体电压: 'Maximum Cell Voltage',
  最高单体温度: 'Maximum Cell Temperature',
  最低单体电压: 'Minimum Cell Voltage',
  最低单体温度: 'Minimum Cell Temperature',
  温度: 'Temperature',
  策略启用: 'Policy Enabling',
  电池设置: 'Battery Setting',
  油机设置: 'Diesel Generator Setting',
  并离网设置: 'On And Off Grid Setting',
  防逆流设置: 'Counter-current setting',
  SOC停止充电点: 'SOC stop charging point',
  SOC停止放电点: 'SOC stop discharge point',
  油机启用: 'Engine on',
  油机容量: 'Engine capacity',
  电网模式: 'Power grid mode',
  并网优先: 'Grid connection priority',
  离网优先: 'Off-grid priority',
  储能放电: 'Energy storage discharge',
  调节策略: 'Regulation strategy',
  降功率调节: 'Power reduction regulation',
  储能允许充电调节: 'Energy storage allows charge adjustment',
  目标值: 'Target value',
  储能和光伏联合供电: 'Energy storage and photovoltaic combined power supply',
  离网供电: 'Off-grid power supply',
  停止充电点: 'Stop charging point',
  停止放电点: 'Stop discharge point',
  '当SOC达到设定值，储能停止充电。':
    'When the SOC reaches the set value, the energy storage stops charging.',
  '并网模式下，当SOC达到设定值，储能停止放电。':
    'In grid-connected mode, when the SOC reaches the set value, the energy storage stops discharging.',
  '当SOC达到设定值，储能停止放电，离网模式下，启动油机。':
    'When SOC reaches the set value, the energy storage stops discharging and starts the oil engine in off-grid mode.',
  '启用后，油机会在储能停止放电时启动油机。':
    'When enabled, the engine will start the engine when the energy storage stops discharging.',
  '当油机工作时，储能会在设定容量内进行充电。':
    'When the oil machine is working, the energy storage will be charged within the set capacity.',
  '当有市电时，并网优先会优先使用市电，离网优先会优先离网供电。':
    'When there is mains power, grid-connected priority will be given priority to use mains power, off-grid priority will be given priority to off-grid power supply.',
  '当并网工作时，储能会在设定容量内进行充电。':
    'When connected to the grid, the energy storage will be charged within the set capacity.',
  '使能后，防止光伏和储能的能量馈入电网。':
    'After this function is enabled, photovoltaic and energy storage energy is prevented from being fed into the power grid.',
  '设定储能放电最大值。': 'Set the maximum energy storage discharge.',
  '降低放电功率，允许充电调节。':
    'Reduce discharge power and allow charge adjustment.',
  '逆流调节目标值。': 'Countercurrent adjustment target value.',
  充电限流值: 'Charging Current Limit',
  '设置电池充电时的电流最大值。':
    'Set the maximum current when charging the battery.',
  放电限流值: 'Discharge Current Limit',
  '设置电池放电时的电流最大值。':
    'Set the maximum current when discharging the battery.',
  欠压保护: 'Undervoltage Protection',
  '电池放电保护电压。': 'Battery discharge protection voltage.',
  欠压恢复: 'Undervoltage Recovery',
  '电池可放电恢复电压。':
    'The battery can be discharged to restore the voltage.',
  过压保护: 'Overvoltage Protection',
  '电池充电电保护电压。': 'Battery charging protection voltage.',
  过压恢复: 'Overvoltage Recovery',
  '电池可充电恢复电压。': 'The battery can be recharged to restore voltage.',
  range: 'The value ranges from {0} to {1}',
  修改卡片: 'Change Card',
  显示实时数据曲线: 'Display real-time data curve',
  键盘: 'Keyboard',
  生效: 'Effective',
  不生效: 'Invalid',
  '设置储能有功功率。': 'Set the active energy storage power.',
  '设置储能无功功率。': 'Set the reactive power of the storage system.',
  '设置储能功率因素。': 'Set the storage power factor.',
  '运行策略是否生效。': 'Whether the run policy takes effect.',
  键盘已开启: 'Keyboard enabled.',
  键盘已关闭: 'Keyboard is off.',
  数据不能为空: 'Data cannot be empty.',
  实时时间: 'Real-Time',
  '系统按照指定模式工作或停止运行。':
    'The system works or stops in the specified mode.',
  '控制电池高压开关状态。': 'Control the battery high voltage switch status.',
  '手动开关油机信号。': 'Switch the oil engine signal manually.',
  请选择设备: 'Please select device',
  请选择日期: 'Please select date',
  项目配置: 'Project Configuration',
  系统设置: 'System Setting',
  策略运行: 'Policy Running',
  策略停止: 'Policy Stop',
  系统开启: 'System On',
  系统关闭: 'System Shutdown',
  开始日期: 'Start Date',
  结束日期: 'End Date',
  最近一周: 'Last week',
  最近一个月: 'Last month',
  最近三个月: 'Last 3 months',
  最近一年: 'Last year',
  开始年份: 'Start Year',
  结束年份: 'End Year',
  今年: 'This year',
  去年: 'Last year',
  本月: 'This month',
  选择类型: 'Select Type',
  选择日期: 'Select Date',
  开始月份: 'Start Month',
  结束月份: 'End Month',
  日: 'Day',
  月: 'Month',
  年: 'Year',
  IP地址: 'IP Address',
  子网掩码: 'Subnet Mask',
  网关: 'Gateway',
  自动: 'Auto',
  手动: 'Manual Operation',
  IP地址必填: 'IP address required',
  子网掩码必填: 'Subnet mask required',
  网关必填: 'Gateway required',
  格式不正确: 'Incorrect format',
  '选择日：导出的是每一天的时刻统计报表':
    'Select Day: Export the statistics report at the time of each day.',
  '选择月：导出的是每个月的天统计表':
    'Select month: The day statistics for each month are exported.',
  '选择年：导出的是每天的月份统计表':
    'Select Year: The resulting monthly statistics table for each day.',
  只能为数字: 'Number only.',
  市电开关: 'Mains Switch',
  '手动开关市电信号。': 'Switch the mains signal manually.',
  双分控制: 'Double Division Control',
  '油机信号和市电信号都断开。':
    'The engine signal and mains signal are disconnected.',
  系统提示: 'System Prompt',
  '确认下发该参数？': 'Are you sure to deliver this parameter?',
  执行: 'Execute',
  平均: 'Average',
  清除电量: 'Clean Charge',
  您的权限不足: 'Your permissions are insufficient',
  清除成功: 'Cleared Successfully',
  '显示系统概括信息，项目名称、地址等':
    'Displays system summary information, project name, address, etc.',
  '显示系统运行状态，是否离线，通讯是否在线等':
    'Displays the operating status of the system, whether it is offline, whether the communication is online, etc.',
  显示最新的告警信息: 'The latest alarm information is displayed.',
  显示当前时间: 'Show current time.',
  中: 'CN',
  英: 'EN',
  激活: 'Activate',
  系统启动: 'System Switch',
  上下高压: 'High Pressure Up And Down',
  空调控制模式: 'Air Conditioning Control Mode',
  温控优先: 'Temperature Control Priority',
  能效优先: 'Energy Efficiency Priority',
  '强制启动空调的电池电芯温差，默认为8':
    'The battery cell temperature difference for forced air conditioning start is 8 by default',
  电池电芯温差: 'Battery Cell Temperature Difference',
  制冷设定温度: 'Refrigeration Set Temperature',
  制冷控制回差: 'Refrigeration Control Differential',
  制热设定温度: 'Heating Set Temperature',
  制热控制回差: 'Heating Control Hysteresis',
  湿度设定值: 'Humidity Set Point',
  除湿回差: 'Dehumidification Hysteresis',
  柜内温度过高点: 'The temperature inside the cabinet is too high',
  柜内温度过低点: 'The temperature inside the cabinet is too low',
  柜内湿度过高点: 'The humidity in the cabinet is too high',
  柜内允许最低温度: 'Minimum temperature allowed in the cabinet',
  柜内允许最高温度: 'Maximum allowable temperature inside the cabinet',
  后备模式: 'Back-Up Mode',
  手动模式: 'Manual Mode',
  '当电池电量低于该值时，启动油机':
    'When the battery power is lower than this value, the diesel engine is started.',
  停止油机SOC: 'Stop The Oil Engine SOC',
  '当电池电量大于该值时，关闭油机':
    'When the battery power is greater than this value, shut down the engine.',
  策略参数设置: 'Strategy Parameter Settings',
  电池保护设置: 'Battery Protection Settings',
  空调参数设置: 'Air Conditioning Parameter Settings',
  启动油机SOC: 'Start Diesel Generator SOC',
  停止油机SOC回差: 'Stop The Diesel Generator SOC Hysteresis',
  '整个系统总开关。': 'The main switch of the whole system.',
  '控制油机启动和停止，仅在光伏消纳生效。':
    'Controls the start and stop of the diesel engine, and is only effective when photovoltaic power is consumed.',
  '后备模式时，当电池SOC小于等于该值时，自动启动柴油发电机。':
    'In backup mode, when the battery SOC is less than or equal to this value, the diesel generator is automatically started.',
  '后备模式时，当电池SOC大于启动油机SOC加停止油机SOC回差时，自动体制柴油发电机。':
    'In backup mode, when the battery SOC is greater than the difference between the starting engine SOC and the stopping engine SOC, the diesel generator is automatically activated.',
  '温控优先：当电池柜温度或湿度达到设定的阈值后，空调立即启动。当电池柜内温度和湿度达到关闭的阈值时将继续运行五分钟以保证下一轮充放处在合适的环境。':
    'Temperature control priority: When the temperature or humidity of the battery cabinet reaches the set threshold, the air conditioner starts immediately. When the temperature and humidity in the battery cabinet reach the shutdown threshold, it will continue to run for five minutes to ensure that the next round of charging and discharging is in a suitable environment.',
  '能效优先：当电池柜温度或湿度达到设定的阈值持续五分钟后，空调才启动（湿度存在波动，五分钟是确认时间），当电池柜内温度和湿度达到关闭的阈值时，立即关闭空调。':
    'Energy efficiency priority: The air conditioner will start only when the temperature or humidity of the battery cabinet reaches the set threshold for five minutes (the humidity fluctuates, and five minutes is the confirmation time). When the temperature and humidity in the battery cabinet reach the shutdown threshold, the air conditioner will be turned off immediately.',
  '当电池电芯温差大于该值时，空调强制启动。':
    'When the battery cell temperature difference is greater than this value, the air conditioner is forced to start.',
  '当电池柜温度大于该值时，达到空调启动条件。':
    'When the battery cabinet temperature is greater than this value, the air conditioning start-up conditions are met.',
  '当电池柜温度低于制冷设定温度-制冷控制回差时，达到空调关闭条件。':
    'When the battery cabinet temperature is lower than the cooling set temperature - cooling control hysteresis, the air conditioning shutdown condition is met.',
  '当电池柜温度小于该值时，达到空调启动条件。':
    'When the battery cabinet temperature is lower than this value, the air conditioner start-up conditions are met.',
  '当电池柜温度大于制热设定温度+制热控制回差时，达到空调关闭条件。':
    'When the battery cabinet temperature is greater than the heating set temperature + heating control hysteresis, the air conditioner is turned off.',
  '当电池柜湿度大于该值时，达到空调启动条件。':
    'When the humidity in the battery cabinet is greater than this value, the air conditioning start-up conditions are met.',
  '当电池柜湿度小于湿度设定值-除湿回差时，达到空调关闭条件。':
    'When the humidity in the battery cabinet is lower than the humidity setting value - dehumidification hysteresis, the air conditioner is turned off.',
  '当电池柜内温度大于该值时，触发告警。':
    'When the temperature inside the battery cabinet exceeds this value, an alarm is triggered.',
  '当电池柜内温度小于该值时，触发告警。':
    'When the temperature inside the battery cabinet is lower than this value, an alarm is triggered.',
  '当电池柜内湿度大于该值时，触发告警。':
    'When the temperature inside the battery cabinet is lower than this value, an alarm is triggered.',
  '系统运行或待机时，都不允许低于该值，低于该值时强制启动空调制热以保证随时能够启动系统。':
    'When the system is running or in standby mode, the temperature is not allowed to fall below this value. When the temperature falls below this value, the air conditioning and heating are forced to start to ensure that the system can be started at any time.',
  '系统运行或待机时，都不允许高于该值，高于该值时强制启动空调制冷以保证随时能够启动系统。':
    'When the system is running or in standby mode, the temperature is not allowed to exceed this value. When the temperature exceeds this value, the air conditioning will be forced to start cooling to ensure that the system can be started at any time.',
  光伏限功率设置: 'Photovoltaic Power Limit Setting',
  电网充电: 'Grid Charging',
  油机充电: 'Diesel Generator Charging',
  充电功率: 'Charging Power',
  '是否允许从电网取电进行充电。': 'Whether charging from the grid is allowed.',
  '是否允许从柴油发电机取电充电。':
    'Is it allowed to charge from diesel generator?',
  '充电功率。': 'Charging power.',
  '光伏最大允许充电功率。':
    'The maximum permissible photovoltaic charging power.',
  '未激活策略，请联系管理人员激活。':
    'The policy is not activated. Please contact the administrator to activate it.',
  未激活: 'Not Activated',
  已激活: 'Activated',
  策略列表: 'Strategy List',
  有功功率设置: 'Active Power Setting',
  无功功率设置: 'Reactive Power Setting',
  功率因数设置: 'Power Factor Setting',
  点击显示更多: 'Click to show more',
  油机启动: 'Diesel Engine Switch',
  close: 'close',
  open: 'open',
  yes: 'yes',
  no: 'no',
  normal: 'normal',
  fault: 'Fault',
  alarm: 'Alarm',
  光伏消纳: 'Photovoltaic Consumption',
  选择策略: 'Select Strategy',
  计算公式: 'Computation Formula',
  'x+y+z,x为第一个属性值,y为第二个属性值...':
    'x+y+z,x is the first attribute value,y is the second attribute value...',
  设备展示配置: 'Device Display Configuration',
  模块类型: 'Module Type',
  配置: 'Configuration',
  添加配置: 'Add Configuration',
  修改配置: 'Modify Configuration',
  包总数: 'Package Total',
  包: 'Package',
  录波: 'Fault Recording',
  故障详情: 'Fault Details',
  手动调节: 'Manual Adjustment',
  系统文件配置: 'System File Configuration',
  文件名称: 'File Name',
  备份成功: 'Backup Success',
  恢复备份成功: 'Restore Backup Successfully',
  查看: 'Check',
  备份: 'Backup',
  恢复备份: 'Restore Backup',
  是否覆盖已有文件: 'Overwrite existing files?',
  设备编号: 'Device No.',
  设备类名: 'Device Class Name',
  设备类型: 'Device Type',
  '设备序列号(云平台)': 'Device Serial Number(Cloud Platform)',
  '设备模块编号(云平台)': 'Device Module Number(Cloud Platform)',
  '设备模块类型(云平台)': 'Device Module Type(Cloud Platform)',
  添加设备: 'Add Device',
  修改设备: 'Modify Device',
  协议类型: 'Protocol Type',
  适配器协议类型: 'Adapter Protocol Type',
  适配器数量: 'Number of Adapters',
  从站地址: 'Slave Address',
  协议文件: 'Protocol File',
  控制文件: 'Control File',
  读写周期: 'Read and write cycle',
  协议信息: 'Protocol Information',
  XML协议文件: 'XML Protocol File',
  故障录波JSON文件: 'Fault Recording JSON File',
  适配器类型: 'Adapter Type',
  设备IP: 'Device IP',
  设备端口: 'Device Port',
  串口文件路径: 'Serial Port File Path',
  串口波特率: 'Serial Port Baud Rate',
  校验位: 'Check Digit',
  数据位: 'Data Bits',
  停止位: 'Stop Bits',
  JSON控制文件: 'JSON Control File',
  请输入密码: 'Please enter password',
  应用: 'Apply',
  '是否应用？': 'Apply?',
  请输入所要备份的文件名: 'Please enter the file name you want to backup',
  必填: 'Required',
  应用成功: 'Application Success',
  应用失败: 'Application Failure',
  退出: 'Sign Out',
  更多: 'More',
  数据库管理: 'Database Management',
  表名: 'Table Name',
  已连接: 'Connected',
  未连接: 'Not connected',
  '是否确认删除该表？': 'Are you sure you want to delete this table?',
  重启EMS程序: 'Restart EMS Program',
  重启成功: 'Restart Successful',
  项目信息: 'Project Information',
  软件版本: 'Software Version',
  硬件版本: 'Hardware Version',
  系统语言: 'System Language',
  系统键盘: 'System Keyboard',
  设备管理: 'Device Management',
  全部: 'All',
  告警状态: 'Alarm Status',
  额定功率: 'Rated power',
  备电保持SOC: 'Battery Backup Holding SOC',
  油机停止SOC: 'Diesel Engine Stop SOC',
  油机停止SOC一定要大于油机启动SOC:
    'The SOC of the diesel engine stop must be greater than the SOC of the diesel engine start',
  结束日期必须晚于开始日期: 'End date must be after start date',
  时间范围超过三个月: 'Time frame longer than three months',
  日期: 'Date',
  刷新: 'Refresh',
  模拟: 'Analog',
  状态: 'Status',
  清空: 'Clear',
  游客登录: 'Visitor Login',
  请选择参数: 'Please select param',
  请输入存储周期: 'Please enter storage cycle',
  设置成功: 'Setting Success',
  设置存储周期: 'Set Storage Cycle',
  存储周期: 'Storage Cycle',
  '请输入数字（s）': 'Please enter number （s）',
  选择地区: 'Select Region',
  调度获取成功: 'Scheduling acquisition success',
  请先设置区域: '请先Please set the region first设置区域',
  策略生成: 'Strategy Generation',
  常规生成: 'Regular Generation',
  精确生成: 'Accurate Generation',
  更新策略: 'Update Strategy',
  是否使用currentSOC: 'Whether to use currentSOC',
  无调度行为: 'No scheduling behavior',
  下发关机: 'Send shutdown',
  设置0功率: 'Set 0 power',
  循环时长: 'Loop Duration',
  设置时区: 'Set Time Zone',
  后台日志: 'Background Log',
  建立连接: 'Establish Connection',
  连接关闭: 'Connection Closed',
  连接出错啦: 'Connection error',
  日志等级: 'Log Level',
  '是否离开此页面？': 'Do you want to leave this page?',
  分钟: 'Minute',
  总收入: 'Total Revenue',
  总支出: 'Total Expenditure',
  净利润: 'Net Profit',
  电网支出: 'Grid Expenditure',
  电网收入: 'Grid Revenue',
  节能收益: 'Energy Savings',
  充电电价: 'Charging Price',
  放电电价: 'Discharging Price',
  光伏日充电量: 'PV Daily Charged Amount',
  电网日充电量: 'Grid Daily Charged Amount',
  电网日放电量: 'Grid Daily Discharged Amount',
  日充电量: 'Daily Charged Amount',
  日放电量: 'Daily Discharged Amount',
  系统收益: 'System Revenue',
  最新: 'Latest',
  请先配置系统收益信息: 'Please configure system revenue information first',
  收入区域: 'Income Region',
  收入场景: 'Revenue Scenario',
  选择场景: 'Select Scenario',
  收益详情: 'Revenue Details',
  收益趋势: 'Revenue Trend',
  历史策略: 'Historical Policy',
  查询失败: 'Query Failed',
  历史策略记录: 'Historical Policy Record',
  查看和管理历史调度策略: 'View and Manage Historical Scheduling Policies',
  条记录: 'Records',
  查询日期: 'Query Date',
  查询: 'Query',
  最新记录在顶部: 'Newest Records on Top',
  加载中: 'Loading',
  该日期暂无历史记录: 'No historical records for this date',
  请选择其他日期或创建新的策略:
    'Please select a different date or create a new policy',
  历史: 'History',
  调度点: 'Schedule Point',
  范围: 'Range',
  最小: 'Minimum',
  最大: 'Maximum',
  调度预览: 'Schedule Preview',
  详情: 'Details',
  策略详情: 'Policy Details',
  基础配置: 'Basic Configuration',
  算法类型: 'Algorithm Type',
  SOC配置: 'SOC Configuration',
  最大SOC: 'Maximum SOC',
  最小SOC: 'Minimum SOC',
  使用外部SOC: 'Use External SOC',
  外部SOC: 'External SOC',
  功率配置: 'Power Configuration',
  最大充电功率: 'Maximum Charging Power',
  最大放电功率: 'Maximum Discharging Power',
  电池容量: 'Battery Capacity',
  效率配置: 'Efficiency Configuration',
  充电效率: 'Charging Efficiency',
  放电效率: 'Discharging Efficiency',
  价格阈值比例: 'Price Threshold Ratio',
  应用此策略: 'Apply This Policy',
  属性: 'Property',
  新值: 'New Value',
  旧值: 'Old Value',
  策略: 'Strategy',
  主机: 'Host',
  连接中: 'Connecting',
  日志控制面板: 'Log Control Panel',
  会话默认日志等级: 'Session Default Log Level',
  选择默认等级: 'Select Default Level',
  模块控制: 'Module Control',
  全部禁用: 'Disable All',
  全部启用: 'Enable All',
  模块等级: 'Module Level',
  '暂无可用模块，请检查连接状态':
    'No modules available, please checelectk the connection status',
  实时日志: 'Real-time Log',
  参数下发: 'Parameter Delivery',
  选择: 'Select',
  导入语言文件: 'Import Language File',
  语言文件: 'Language File',
  语言文件名: 'Language File Name',
  '例如：zh_CN.ems': 'e.g.: zh_CN.ems',
  语言文件内容: 'Language File Content',
  '请粘贴完整的语言文件内容...':
    'Please paste the complete language file content...',
  覆盖模式: 'Overwrite Mode',
  不覆盖模式: 'No Overwrite Mode',
  如果文件已存在将被覆盖: 'File will be overwritten if it already exists',
  如果文件已存在将导入失败: 'Import will fail if file already exists',
  语言文件名不能为空: 'Language file name cannot be empty',
  语言文件内容不能为空: 'Language file content cannot be empty',
  请输入语言文件名: 'Please enter language file name',
  请输入语言文件内容: 'Please enter language file content',
  语言文件导入成功: 'Language file imported successfully',
  导入: 'Import',
  拖拽文件到此处或点击选择文件: 'Drag files here or click to select',
  '支持 .ems 格式': 'Supports .ems formats',
  文件读取失败: 'File reading failed'
}
