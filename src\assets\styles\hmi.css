/* HMI 10寸屏幕优化样式 */

/* 根元素设置 */
:root {
  /* HMI屏幕尺寸变量 */
  --hmi-screen-width: 1024px;
  --hmi-screen-height: 600px;

  /* 触摸友好的尺寸 */
  --hmi-touch-target-min: 44px;
  --hmi-button-height: 48px;
  --hmi-input-height: 56px;

  /* 字体大小 */
  --hmi-font-size-small: 14px;
  --hmi-font-size-base: 16px;
  --hmi-font-size-large: 18px;
  --hmi-font-size-xl: 20px;

  /* 间距 */
  --hmi-spacing-xs: 4px;
  --hmi-spacing-sm: 8px;
  --hmi-spacing-md: 16px;
  --hmi-spacing-lg: 24px;
  --hmi-spacing-xl: 32px;

  /* 圆角 */
  --hmi-border-radius: 8px;
  --hmi-border-radius-lg: 12px;
}

/* HMI屏幕适配 */
@media screen and (max-width: 1280px) and (min-width: 1024px) {

  /* 主容器优化 */
  .v-application {
    font-size: var(--hmi-font-size-base) !important;
  }

  /* 按钮优化 */
  .v-btn {
    min-height: var(--hmi-button-height) !important;
    min-width: 120px !important;
    font-size: var(--hmi-font-size-base) !important;
    font-weight: 500 !important;
  }

  .v-btn--size-small {
    min-height: 40px !important;
    min-width: 100px !important;
    font-size: var(--hmi-font-size-small) !important;
  }

  .v-btn--size-large {
    min-height: 56px !important;
    min-width: 140px !important;
    font-size: var(--hmi-font-size-large) !important;
  }

  /* 输入框优化 */
  .v-text-field .v-field__input {
    min-height: var(--hmi-input-height) !important;
    font-size: var(--hmi-font-size-base) !important;
    padding: 0 16px !important;
  }

  .v-select .v-field__input {
    min-height: var(--hmi-input-height) !important;
    font-size: var(--hmi-font-size-base) !important;
  }

  /* 卡片优化 */
  .v-card {
    border-radius: var(--hmi-border-radius-lg) !important;
  }

  .v-card-title {
    font-size: var(--hmi-font-size-large) !important;
    font-weight: 600 !important;
    padding: var(--hmi-spacing-lg) !important;
  }

  .v-card-text {
    font-size: var(--hmi-font-size-base) !important;
    padding: var(--hmi-spacing-md) var(--hmi-spacing-lg) !important;
  }

  /* 导航优化 */
  .v-navigation-drawer {
    width: 280px !important;
  }

  .v-list-item {
    min-height: 56px !important;
    padding: 0 var(--hmi-spacing-lg) !important;
  }

  .v-list-item-title {
    font-size: var(--hmi-font-size-base) !important;
    font-weight: 500 !important;
  }

  /* 表格优化 */
  .v-data-table .v-data-table__td {
    height: 56px !important;
    font-size: var(--hmi-font-size-base) !important;
    padding: 0 var(--hmi-spacing-md) !important;
  }

  .v-data-table .v-data-table__th {
    height: 56px !important;
    font-size: var(--hmi-font-size-base) !important;
    font-weight: 600 !important;
    padding: 0 var(--hmi-spacing-md) !important;
  }

  /* 对话框优化 */
  .v-dialog {
    border-radius: var(--hmi-border-radius-lg) !important;
  }

  .v-dialog .v-card {
    max-width: 90vw !important;
    max-height: 90vh !important;
  }

  /* 工具栏优化 */
  .v-toolbar {
    height: 64px !important;
  }

  .v-toolbar-title {
    font-size: var(--hmi-font-size-xl) !important;
    font-weight: 600 !important;
  }

  /* 标签页优化 */
  .v-tab {
    min-height: var(--hmi-button-height) !important;
    font-size: var(--hmi-font-size-base) !important;
    font-weight: 500 !important;
    padding: 0 var(--hmi-spacing-lg) !important;
  }

  /* 芯片优化 */
  .v-chip {
    height: 36px !important;
    font-size: var(--hmi-font-size-small) !important;
    padding: 0 var(--hmi-spacing-md) !important;
  }

  /* 图标优化 */
  .v-icon {
    font-size: 24px !important;
  }

  .v-btn .v-icon {
    font-size: 20px !important;
  }

  /* 滚动条优化 */
  ::-webkit-scrollbar {
    width: 12px !important;
    height: 12px !important;
  }

  ::-webkit-scrollbar-thumb {
    border-radius: 6px !important;
    background-color: rgba(0, 0, 0, 0.3) !important;
  }

  ::-webkit-scrollbar-track {
    background-color: rgba(0, 0, 0, 0.1) !important;
  }
}

/* 触摸优化 */
.hmi-touch-friendly {
  cursor: pointer !important;
  user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;
}

/* HMI专用工具类 */
.hmi-font-small {
  font-size: var(--hmi-font-size-small) !important;
}

.hmi-font-base {
  font-size: var(--hmi-font-size-base) !important;
}

.hmi-font-large {
  font-size: var(--hmi-font-size-large) !important;
}

.hmi-font-xl {
  font-size: var(--hmi-font-size-xl) !important;
}

.hmi-spacing-xs {
  padding: var(--hmi-spacing-xs) !important;
}

.hmi-spacing-sm {
  padding: var(--hmi-spacing-sm) !important;
}

.hmi-spacing-md {
  padding: var(--hmi-spacing-md) !important;
}

.hmi-spacing-lg {
  padding: var(--hmi-spacing-lg) !important;
}

.hmi-spacing-xl {
  padding: var(--hmi-spacing-xl) !important;
}

.hmi-margin-xs {
  margin: var(--hmi-spacing-xs) !important;
}

.hmi-margin-sm {
  margin: var(--hmi-spacing-sm) !important;
}

.hmi-margin-md {
  margin: var(--hmi-spacing-md) !important;
}

.hmi-margin-lg {
  margin: var(--hmi-spacing-lg) !important;
}

.hmi-margin-xl {
  margin: var(--hmi-spacing-xl) !important;
}

/* 响应式网格优化 */
.hmi-grid-container {
  display: grid !important;
  gap: var(--hmi-spacing-md) !important;
  padding: var(--hmi-spacing-lg) !important;
}

.hmi-grid-2-cols {
  grid-template-columns: 1fr 1fr !important;
}

.hmi-grid-3-cols {
  grid-template-columns: 1fr 1fr 1fr !important;
}

.hmi-grid-4-cols {
  grid-template-columns: repeat(4, 1fr) !important;
}

/* 状态指示器优化 */
.hmi-status-indicator {
  width: 16px !important;
  height: 16px !important;
  border-radius: 50% !important;
  display: inline-block !important;
  margin-right: var(--hmi-spacing-sm) !important;
}

.hmi-status-online {
  background-color: #28c79c !important;
}

.hmi-status-offline {
  background-color: #ff6b6b !important;
}

.hmi-status-warning {
  background-color: #ffaa43 !important;
}

.hmi-status-info {
  background-color: #16B1FF !important;
}

/* 数据显示优化 */
.hmi-data-value {
  font-size: var(--hmi-font-size-xl) !important;
  font-weight: 700 !important;
  color: #0093b6 !important;
}

.hmi-data-unit {
  font-size: var(--hmi-font-size-small) !important;
  color: rgba(0, 0, 0, 0.6) !important;
  margin-left: var(--hmi-spacing-xs) !important;
}

.hmi-data-label {
  font-size: var(--hmi-font-size-base) !important;
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 0.8) !important;
}

/* 表单元素优化 */
.v-text-field,
.v-select,
.v-textarea,
.v-autocomplete {
  .v-field__input {
    font-size: var(--hmi-font-size-base) !important;
    line-height: 1.5 !important;
  }

  .v-field__append-inner,
  .v-field__prepend-inner {
    .v-icon {
      font-size: 20px !important;
    }
  }

  .v-label {
    font-size: var(--hmi-font-size-base) !important;
    font-weight: 500 !important;
  }
}

/* 数字输入框优化 */
.v-number-input {
  .v-field__input {
    font-size: var(--hmi-font-size-large) !important;
    font-weight: 600 !important;
    text-align: center !important;
  }

  .v-btn {
    min-width: 40px !important;
    width: 40px !important;
    height: 40px !important;
  }
}

/* 开关组件优化 */
.v-switch {
  .v-switch__track {
    width: 48px !important;
    height: 24px !important;
  }

  .v-switch__thumb {
    width: 20px !important;
    height: 20px !important;
  }
}

/* 复选框和单选框优化 */
.v-checkbox,
.v-radio {
  .v-selection-control__input {
    width: 24px !important;
    height: 24px !important;
  }

  .v-icon {
    font-size: 24px !important;
  }

  .v-label {
    font-size: var(--hmi-font-size-base) !important;
    margin-left: var(--hmi-spacing-sm) !important;
  }
}

/* 滑块优化 */
.v-slider {

  .v-slider-track__fill,
  .v-slider-track__background {
    height: 6px !important;
  }

  .v-slider-thumb__surface {
    width: 24px !important;
    height: 24px !important;
  }
}

/* 进度条优化 */
.v-progress-linear {
  height: 8px !important;
  border-radius: 4px !important;
}

.v-progress-circular {
  .v-progress-circular__overlay {
    stroke-width: 4 !important;
  }
}