/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-10-11 19:18:30
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-18 16:15:07
 * @FilePath: \ems_manage\src\api\config.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from './index'

export const systemInfoSet = (data) => {
  return request({
    url: '/systemInfoSet',
    method: 'post',
    data
  })
}

export const getSystemInfo = () => {
  return request({
    url: '/systemInformation',
    method: 'get'
  })
}

/**
 * 拓扑图
 */
export const topologyInformation = () => {
  return request({
    url: '/topologyInformation',
    method: 'get'
  })
}

export const topologyInfoSet = (data) => {
  return request({
    url: '/topologyInfoSet',
    method: 'post',
    data
  })
}

export const getRealtimePointData = (data) => {
  return request({
    url: '/getRealtimePointData',
    method: 'post',
    data
  })
}

/**
 * 系统设置
 */
export const getIp = () => {
  return request({
    url: '/getIp',
    method: 'post'
  })
}

export const ipSet = (data) => {
  return request({
    url: '/ipSet',
    method: 'post',
    data
  })
}

/**
 * 获取系统状态
 */
export const getSystemStatus = () => {
  return request({
    url: '/getSystemStatus',
    method: 'get'
  })
}

/**
 * 获取首页卡片数据
 */
export const getHomeCardData = (data) => {
  return request({
    url: '/getHomeCardData',
    method: 'post',
    data
  })
}

/**
 * 同步后端时间
 */
export const getLocalTime = () => {
  return request({
    url: '/getLocalTime',
    method: 'get'
  })
}


/**
 * json配置
 */
export const setWebConfig = (data) => {
  return request({
    url: '/setWebConfig',
    method: 'post',
    data
  })
}

export const getWebConfig = (data) => {
  return request({
    url: '/getWebConfig',
    method: 'post',
    data
  })
}

/**
 * 获取版本
 */
export const getVersionInfo = () => {
  return request({
    url: '/getVersionInfo',
    method: 'get'
  })
}

/**
 * 设置时区
 */
export const getTimeZoneList = () => {
  return request({
    url: '/time/timezones',
    method: 'get'
  })
}
export const setTimeZone = (data) => {
  return request({
    url: '/time/timezone',
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}

