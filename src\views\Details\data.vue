<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-07-28 14:33:23
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-28 14:36:35
 * @FilePath: \ems_manage\src\views\Details\data.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { isEmpty } from 'lodash-es'

import DataItem from '@/components/data-item'

const currentData = defineModel('currentData', { default: () => {} })
const deviceName = defineModel('deviceName')
</script>

<template>
  <div class="px-4 py-3 h-full">
    <template v-if="currentData?.status">
      <div class="text-body-1 mb-4 d-flex align-center">
        <v-icon icon="mdi-decagram" color="warning" class="mr-1"></v-icon
        >{{ $t('运行状态') }}
      </div>
      <v-row class="mb-4">
        <v-col
          cols="12"
          lg="3"
          md="6"
          sm="12"
          v-for="item in currentData.status"
          :key="item.point_id"
          class="pa-2"
        >
          <data-item isStatus :item="item" :deviceName="deviceName" />
        </v-col>
      </v-row>
    </template>
    <template v-if="currentData?.analog">
      <div class="text-body-1 mb-4 d-flex align-center">
        <v-icon icon="mdi-database" color="warning" class="mr-1"></v-icon
        >{{ $t('运行数据') }}
      </div>
      <v-row>
        <v-col
          cols="12"
          lg="3"
          md="6"
          sm="12"
          v-for="item in currentData.analog"
          :key="item.point_id"
          class="pa-2"
        >
          <data-item :item="item" :deviceName="deviceName" />
        </v-col>
      </v-row>
    </template>
    <empty v-if="isEmpty(currentData)" />
  </div>
</template>

<style lang="scss" scoped></style>
