<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-25 18:41:41
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-28 12:04:25
 * @FilePath: \ems_manage\src\views\Config\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, toRefs, computed } from 'vue'
import { useUserStore } from '@/store/module/user'
import { useDeviceConfigStore } from '@/store/module/deviceConfig'
import router, { configRoutes } from '@/router'
import { useRoute } from 'vue-router'

const route = useRoute()
const tab = computed({
  get() {
    return route.name
  },
  set(val) {
    router.push({ name: val })
  }
})
const { userInfo } = toRefs(useUserStore())

const subCom = configRoutes.map((item) => {
  let show = true
  if (item.name == 'DeviceConfig') {
    show = false
  } else {
    show =
      item.meta.roles.find((role) => role == userInfo.value.permission_level) !=
      -1
  }
  return {
    ...item,
    show
  }
})

useDeviceConfigStore().isEdit = false
</script>

<template>
  <div class="pa-6 w-100 overflow-hidden d-flex flex-column">
    <div class="px-2">
      <v-card class="pa-4 w-100 rounded-lg no-scrollbar mb-4" elevation="4">
        <div class="d-flex justify-between align-center">
          <div class="text-h6">{{ $t('配置中心') }}</div>
          <div class="d-flex align-center"></div>
        </div>
      </v-card>
    </div>
    <v-tabs v-model="tab" color="secondary" class="px-3">
      <template v-for="item in subCom" :key="item.name">
        <v-tab :value="item.name" v-if="item.show">{{
          $t(item.meta.title)
        }}</v-tab>
      </template>
    </v-tabs>

    <v-tabs-window v-model="tab" class="mt-4">
      <router-view />
    </v-tabs-window>
  </div>
</template>

<style lang="scss" scoped>
:deep(.v-window) {
  overflow: visible;
}
</style>
