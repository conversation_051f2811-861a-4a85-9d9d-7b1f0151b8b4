<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-06 14:45:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-11 14:56:31
 * @FilePath: \ems_manage\src\views\Dashboard\lineEchart.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { computed, ref } from 'vue'
import dayjs from '@/utils/date'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const props = defineProps({
  data: Array
})

// 用来存储一天中每分钟的时间戳
const timestamps = ref([])
const generateTimes = () => {
  // 设置起始时间为当天的午夜
  let startTime = dayjs().startOf('day')

  // 从午夜开始，每隔一分钟添加一个时间戳，直到第二天午夜
  for (let minute = 0; minute < 24 * 60; minute++) {
    // 使用 minute 对当前时间进行增加
    let currentMinute = startTime.clone().add(minute, 'minute')
    timestamps.value.push(currentMinute)
  }
}
generateTimes()

const getData = () => {
  let startTime = dayjs().startOf('day')

  // 新的数组，用于存放结果
  let resultArray = timestamps.value.map((timestamp) => null)

  props.data.forEach((interval) => {
    const start = dayjs(startTime.format('YYYY-MM-DD ') + interval.start_time)
    const end = dayjs(startTime.format('YYYY-MM-DD ') + interval.end_time)

    for (let i = 0; i < timestamps.value.length; i++) {
      if (
        timestamps.value[i].isSameOrAfter(start) &&
        timestamps.value[i].isSameOrBefore(end)
      ) {
        resultArray[i] = interval.power_set
      }
    }
  })

  return resultArray
}

const options = computed(() => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    grid: {
      left: '10%',
      right: '5%',
      bottom: '20%',
      top: '5%'
    },
    xAxis: {
      data: timestamps.value.map((item) => item.format('HH:mm')),
      axisLabel: {
        show: true
      }
    },
    yAxis: {
      type: 'value',
      splitLine: { show: false },
      minInterval: 50
    },
    visualMap: {
      show: false,
      pieces: [
        {
          gt: 0,
          lte: 100,
          color: '#93CE07'
        },
        {
          lte: 0,
          gte: -100,
          color: '#FBDB0F'
        }
      ],
      outOfRange: {
        color: '#999'
      }
    },
    series: {
      name: t('功率'),
      type: 'line',
      data: getData()
    }
  }
})
</script>

<template>
  <div>
    <BaseEchart
      width="100%"
      height="100%"
      :options="options"
      ref="myChart"
    ></BaseEchart>
  </div>
</template>

<style lang="scss" scoped></style>
