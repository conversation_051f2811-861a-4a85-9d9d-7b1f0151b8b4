<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-25 18:41:41
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-22 08:58:28
 * @FilePath: \ems_manage\src\views\Config\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, toRefs, getCurrentInstance } from 'vue'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'
import { useI18n } from 'vue-i18n'
import { fileToBase64 } from '@/utils'

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { systemInfo } = toRefs(useConfigStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())

const getData = () => {
  useConfigStore()
    .getSystemInfoFn()
    .then((res) => {
      form.value = {
        ...systemInfo.value,
        logo: systemInfo.value.logo
          ? 'data:image/png;base64,' +
            decodeURIComponent(decodeURIComponent(systemInfo.value.logo))
          : undefined
      }
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
getData()

const form = ref({})
const loading = ref(false)
const submit = async () => {
  let logo = null
  if (form.value.logo instanceof File) {
    logo = encodeURIComponent(
      (await fileToBase64(form.value.logo)).split('base64,')[1]
    )
  } else {
    if (form.value.logo && form.value.logo?.indexOf('base64,') != -1) {
      logo = encodeURIComponent(form.value.logo?.split('base64,')[1])
    }
  }
  if (form.value.logo == undefined) {
    form.value.logo = ''
  }
  const { valid } = await proxy.$refs.formRef.validate()
  if (!valid) return
  loading.value = true
  try {
    const res = await useConfigStore().systemInfoSetFn({
      ...form.value,
      logo: logo == null ? form.value.logo : logo
    })
    snackbar.value = true
    snackbarText.value = t('保存成功')
    loading.value = false
    getData()
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
  }
}

/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value, prop) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  form.value[currentInput.value] = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}

defineExpose({
  getData
})
</script>

<template>
  <div class="w-100 px-2">
    <v-card class="pa-4 w-100 rounded-lg no-scrollbar" elevation="4">
      <v-form @submit.prevent="submit" ref="formRef">
        <v-row>
          <v-col cols="12" lg="6" md="12" sm="12" xs="12">
            <v-text-field
              v-model="form.project_name"
              :label="$t('项目名称')"
              variant="outlined"
              @click:control="
                handleShow($event, form.project_name, 'project_name')
              "
            ></v-text-field>
          </v-col>
          <v-col cols="12" lg="6" md="12" sm="12" xs="12">
            <v-text-field
              v-model="form.project_address"
              :label="$t('项目地址')"
              variant="outlined"
              append-inner-icon="mdi-map-marker"
              @click:control="
                handleShow($event, form.project_address, 'project_address')
              "
            ></v-text-field>
          </v-col>
          <v-col cols="12" lg="6" md="12" sm="12" xs="12">
            <v-text-field
              v-model="form.installed_capacity"
              :label="$t('装机容量')"
              variant="outlined"
              @click:control="
                handleShow(
                  $event,
                  form.installed_capacity,
                  'installed_capacity'
                )
              "
            >
              <template #append-inner>kWh</template>
            </v-text-field>
          </v-col>
          <v-col cols="12" lg="6" md="12" sm="12" xs="12">
            <v-text-field
              v-model="form.installed_power"
              :label="$t('装机功率')"
              variant="outlined"
              @click:control="
                handleShow($event, form.installed_power, 'installed_power')
              "
            >
              <template #append-inner>kW</template>
            </v-text-field>
          </v-col>
          <v-col cols="12">
            <v-file-input
              v-model="form.logo"
              label="Logo"
              accept="image/*"
              counter
              show-size
              variant="outlined"
              prepend-icon=""
            ></v-file-input>
            <img :src="form.logo" style="width: 200px" />
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12">
            <v-btn
              class="px-8"
              type="submit"
              height="50"
              :loading="loading"
              color="primary"
              >{{ $t('保存') }}</v-btn
            >
          </v-col>
        </v-row>
      </v-form>
    </v-card>
  </div>
</template>

<style lang="scss" scoped></style>
