/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-05-17 12:03:18
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-19 17:39:06
 * @FilePath: \elecloud_platform_visual\src\components\echart\hooks\useEchart.ts
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
// 引入 echarts 核心模块，核心模块提供了 echarts 使用必须要的接口。
import * as echarts from 'echarts/core'
// 引入标题，提示框，直角坐标系，数据集，内置数据转换器组件，组件后缀都为 Component
import {
  GridComponent,
  TooltipComponent,
  LegendPlainComponent,
  AxisPointerComponent,
  DataZoomComponent,
  VisualMapComponent,
  LegendScrollComponent
} from 'echarts/components'
// 引入柱状图图表，图表后缀都为 Chart
import { LineChart, BarChart } from 'echarts/charts'
// 标签自动布局、全局过渡动画等特性
import { UniversalTransition, LabelLayout } from 'echarts/features'
// 引入 Canvas 渲染器，注意引入 CanvasRenderer 或者 SVGRenderer 是必须的一步
import { CanvasRenderer } from 'echarts/renderers'
// 主题
import walden from '../data/walden.project.json'

echarts.registerTheme('walden', walden.theme)
echarts.use([
  GridComponent,
  LineChart,
  CanvasRenderer,
  UniversalTransition,
  LabelLayout,
  BarChart,
  TooltipComponent,
  LegendPlainComponent,
  AxisPointerComponent,
  DataZoomComponent,
  VisualMapComponent,
  LegendScrollComponent
])

export default function (el) {
  const echartInstance = echarts.init(el, 'walden')

  const setOptions = (options) => {
    echartInstance.setOption(options)
  }

  const updateSize = () => {
    echartInstance.resize()
  }

  window.addEventListener('resize', () => {
    echartInstance.resize()
  })

  return {
    echartInstance,
    setOptions,
    updateSize
  }
}
