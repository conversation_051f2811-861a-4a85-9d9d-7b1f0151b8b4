<template>
  <div class="base-echart">
    <div ref="echartDivRef" :style="{ width: width, height: height }" />
  </div>
</template>

<script setup>
import { ref, onMounted, watchEffect } from 'vue'
import useEchart from '../hooks/useEchart.js'

// 定义props
const props = defineProps({
  options: Object,
  width: String,
  height: String
})

const echartDivRef = ref()
const instance = ref()

onMounted(() => {
  const { setOptions, echartInstance, updateSize } = useEchart(
    echartDivRef.value
  )
  instance.value = echartInstance

  watchEffect(() => {
    updateSize()
    echartInstance.clear()
    setOptions(props.options)
  })
})

defineExpose({
  instance,
  echartDivRef
})
</script>

<style scoped>
.base-echart {
  width: 100%;
  height: 100%;
}
</style>
