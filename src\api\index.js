/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-11 10:03:57
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-29 14:17:48
 * @FilePath: \ems_manage\src\api\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import axios from 'axios'
import router from '@/router'
import { useGlobalStore } from '@/store/global'
import { isEmpty } from 'lodash-es'
import { useConfigStore } from '@/store/module/config'
import { useUserStore } from '@/store/module/user'

export let BASE_URL = '/api'

const requestInstance = axios.create({
  baseURL: BASE_URL,
  headers: {
    'Content-Type': 'application/x-www-form-urlencoded;'
  }
})

requestInstance.interceptors.request.use((config) => {
  let lang = localStorage.getItem('lang') || 'en'
  if (lang == 'zhHans') config.headers['language'] = 'zh'
  else config.headers['language'] = lang
  return config
})

requestInstance.interceptors.response.use(
  (res) => {
    // 未设置状态码则默认成功状态
    const code = res.data.code || 200
    // 二进制数据则直接返回
    if (
      res.request.responseType === 'blob' ||
      res.request.responseType === 'arraybuffer'
    ) {
      return res.data
    }
    if (code === 401) {
      useUserStore().isLogin = false
      useGlobalStore().showPanel = false
      useConfigStore().logData = []
      useConfigStore().logWs?.close()
      useGlobalStore().snackbar = true
      useGlobalStore().snackbarText = res.data.msg
      router.push('/login')
      return Promise.reject(res.data.msg)
    } else {
      return res.data
    }
  },
  (error) => {
    return Promise.reject(error)
  }
)

export const request = (config) => {
  return new Promise((resolve, reject) => {
    requestInstance
      .request(config)
      .then((res) => {
        resolve(res)
      })
      .catch((err) => {
        console.log(err)
        if (isEmpty(err?.response?.data)) {
          useGlobalStore().snackbar = true
          useGlobalStore().snackbarText = err?.response.statusText
          reject(err?.response.statusText)
        } else if (
          typeof err?.response?.data == 'string' &&
          err?.response?.data?.indexOf('<html>') !== -1
        ) {
          useGlobalStore().snackbar = true
          useGlobalStore().snackbarText = err?.response.statusText
          reject(err?.response.statusText)
        } else if (typeof err?.response?.data == 'string') {
          useGlobalStore().snackbar = true
          useGlobalStore().snackbarText = err?.response.statusText
          reject(err?.response.statusText)
        } else {
          if (err?.response.data.code == 401) {
            router.push('/login')
          }
          reject(err?.response.data.msg)
        }
      })
  })
}
