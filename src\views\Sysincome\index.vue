<script setup>
import {
  ref,
  getCurrentInstance,
  toRefs,
  nextTick,
  watch,
  computed,
  onMounted
} from 'vue'
import { useI18n } from 'vue-i18n'
import {
  getSysincomeConfig,
  setSysincomeScenario,
  setSysincomeRegion,
  getSysincomeData,
  getSysincomeDay,
  getSysincomeMonth,
  getSysincomeYear,
  getSysincomeUnitTime
} from '@/api/sysincome'
import { useGlobalStore } from '@/store/global'
import { useParamStore } from '@/store/module/param'
import { setLeftWidth } from '@/utils'
import { useAllowedDate } from '@/hook/useAllowedDate'
import dayjs, { generateTimePoints } from '@/utils/date'

import BarEchart from './barEchart.vue'

const { allowedDates } = useAllowedDate()
const { proxy } = getCurrentInstance()
const { t } = useI18n()
const { snackbar, snackbarText, legendSelected } = toRefs(useGlobalStore())
const { countryOptions } = toRefs(useParamStore())

/**
 * 获取系统收入配置
 */
const isHaveConfig = ref(false)
const isShowEmpty = ref(false)
const getSysincomeConfigFn = () => {
  getSysincomeConfig()
    .then((res) => {
      if (res.code != 200) {
        snackbar.value = true
        snackbarText.value = res.msg
      }
      let data = res.data
      form.value = {
        ...data
      }
      if (!data.region || (!data.scenario && data.scenario != 0)) {
        isHaveConfig.value = false
      } else {
        isNew.value = true
        // getSysincomeDataFn()
        getSysincomeDayFn()
        getSysincomeUnitTimeFn()
        isHaveConfig.value = true
      }
      nextTick(() => {
        setLeftWidth('left')
      })
    })
    .catch((err) => {
      snackbar.value = true
      snackbarText.value = err
    })
}
getSysincomeConfigFn()
const getCountryList = () => {
  useParamStore()
    .countryListFn()
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
getCountryList()
const form = ref({
  region: undefined,
  scenario: undefined
})
const scenarioOptions = ref([
  {
    value: 0,
    title: 'DCPV to DCPCS'
  },
  {
    value: 1,
    title: 'ACPV front ACPCS'
  },
  {
    value: 2,
    title: 'AC back ACPCS'
  }
])
const regionLoading = ref(false)
const scenarioLoading = ref(false)
const setConfigFn = (type) => {
  let api = null
  let data = null
  if (type === 'region') {
    regionLoading.value = true
    api = setSysincomeRegion
    data = { region: form.value.region }
  } else {
    scenarioLoading.value = true
    api = setSysincomeScenario
    data = { scenario: form.value.scenario }
  }
  api(data)
    .then((res) => {
      snackbar.value = true
      snackbarText.value = res.msg
      getSysincomeConfigFn()
      regionLoading.value = false
      scenarioLoading.value = false
    })
    .catch((err) => {
      snackbar.value = true
      snackbarText.value = err
      regionLoading.value = false
      scenarioLoading.value = false
    })
}
/**
 * 获取系统收益数据
 */
const getForm = ref({
  date: new Date(),
  month: Number(dayjs(new Date()).format('MM')) - 1,
  year: Number(dayjs(new Date()).format('YYYY')),
  formatDate: dayjs(new Date()).format('YYYY-MM-DD')
})
const isShowDate = ref(false)
const handleDateChange = (e) => {
  if (viewMode.value !== 'month') return
  getForm.value.formatDate = dayjs(e).format('YYYY-MM-DD')
  isShowDate.value = false
  getSysincomeDayFn()
  getSysincomeUnitTimeFn()
}
const handleMonthChange = (e) => {
  let value = e + 1
  if (viewMode.value !== 'months') return
  getForm.value.formatDate = `${getForm.value.year}-${
    value < 10 ? '0' + value : value
  }`
  isShowDate.value = false
  getSysincomeMonthFn()
}
const handleYearChange = (e) => {
  if (viewMode.value !== 'year') return
  getForm.value.formatDate = e + ''
  isShowDate.value = false
  getSysincomeYearFn()
}

const toggle = ref(0)
const viewMode = ref('month')
const isNew = ref(true)
const oldToggle = ref(undefined)
watch(toggle, (newValue, oldValue) => {
  isNew.value = false
  if (newValue == undefined) {
    toggle.value = oldValue
    return
  }
  getForm.value.date = new Date()
  if (toggle.value == 0) {
    viewMode.value = 'month'
    getForm.value.formatDate = dayjs(getForm.value.date).format('YYYY-MM-DD')
    getForm.value.month = Number(dayjs(getForm.value.date).format('MM')) - 1
    getForm.value.year = Number(dayjs(getForm.value.date).format('YYYY'))
    getSysincomeDayFn()
    getSysincomeUnitTimeFn()
  } else if (toggle.value == 1) {
    viewMode.value = 'months'
    getForm.value.formatDate = dayjs(getForm.value.date).format('YYYY-MM')
    getForm.value.month = Number(dayjs(getForm.value.date).format('MM')) - 1
    getForm.value.year = Number(dayjs(getForm.value.date).format('YYYY'))
    getSysincomeMonthFn()
  } else if (toggle.value == 2) {
    viewMode.value = 'year'
    getForm.value.formatDate = dayjs(getForm.value.date).format('YYYY')
    getForm.value.year = Number(dayjs(getForm.value.date).format('YYYY'))
    getSysincomeYearFn()
  }
})

const getSysincomeDataFn = () => {
  getSysincomeData()
    .then((res) => {
      if (res.code != 200) {
        snackbar.value = true
        snackbarText.value = res.msg
      }
      let data = res.data
      // getForm.value.date = new Date(data.date)
      getForm.value.formatDate = data.date
      sysincomeData.value = data
    })
    .catch((err) => {
      if (err == 'No system income data found') {
        isShowEmpty.value = true
      }
      snackbar.value = true
      snackbarText.value = err
    })
}
// getSysincomeDataFn()
const getSysincomeDayFn = () => {
  getSysincomeDay({
    date: getForm.value.formatDate
  })
    .then((res) => {
      if (res.code != 200) {
        snackbar.value = true
        snackbarText.value = res.msg
      }
      sysincomeData.value = res.data
    })
    .catch((err) => {
      snackbar.value = true
      snackbarText.value = err
    })
}
const getSysincomeUnitTimeFn = () => {
  getSysincomeUnitTime({
    date: getForm.value.formatDate
  })
    .then((res) => {
      if (res.code != 200) {
        snackbar.value = true
        snackbarText.value = res.msg
      }
      let data = res.data
      let times = generateTimePoints(data.timeInterval, 'HH:mm', {
        value: data.resolution == 60 ? 1 : 15,
        unit: data.resolution == 60 ? 'hour' : 'minute'
      })
      // 电价
      sysincomeData.value.times = times
      sysincomeData.value.datas = data.points
      nextTick(() => {
        legendSelect()
      })
    })
    .catch((err) => {
      snackbar.value = true
      snackbarText.value = err
    })
}
legendSelected.value = {
  [t('总支出')]: true,
  [t('总收入')]: true,
  [t('净利润')]: true,
  [t('电网支出')]: false,
  [t('电网收入')]: false,
  [t('节能收益')]: false,
  [t('充电电价')]: false,
  [t('放电电价')]: false,
  [t('光伏日充电量')]: false,
  [t('电网日充电量')]: false,
  [t('电网日放电量')]: false,
  [t('日充电量')]: false,
  [t('日放电量')]: false
}
const legendSelect = () => {
  proxy.$refs.incomeChart?.instance.on('legendselectchanged', (param) => {
    ed.value = param.selected
  })
}
const getSysincomeMonthFn = () => {
  getSysincomeMonth({
    yearMonth: getForm.value.formatDate
  })
    .then((res) => {
      if (res.code != 200) {
        snackbar.value = true
        snackbarText.value = res.msg
      }
      sysincomeData.value = res.data
    })
    .catch((err) => {
      snackbar.value = true
      snackbarText.value = err
    })
}
const getSysincomeYearFn = () => {
  getSysincomeYear({
    year: getForm.value.formatDate
  })
    .then((res) => {
      if (res.code != 200) {
        snackbar.value = true
        snackbarText.value = res.msg
      }
      let data = res.data
      sysincomeData.value = {
        ...data,
        monthly_records: data.monthly_records.map((item, index) => {
          return {
            ...item,
            date: getForm.value.formatDate + '-' + (index + 1)
          }
        })
      }
    })
    .catch((err) => {
      snackbar.value = true
      snackbarText.value = err
    })
}

// 在 script setup 中添加数据处理逻辑
const sysincomeData = ref({
  date: '',
  currency: '',
  scenario: '',
  daily: {},
  monthly: {},
  yearly: {},
  total: {},
  daily_records: [],
  monthly_records: [],
  datas: [],
  times: []
})
const currentData = computed(() => {
  return (type) => {
    return sysincomeData.value[type] || {}
  }
})

const overviewCards = ref([
  {
    key: 'total_income',
    title: t('总收入'),
    icon: 'mdi-trending-up',
    color: 'success',
    textColor: 'text-success'
  },
  {
    key: 'total_expense',
    title: t('总支出'),
    icon: 'mdi-trending-down',
    color: 'error',
    textColor: 'text-error'
  },
  {
    key: 'profit',
    title: t('净利润'),
    icon: 'mdi-currency-usd',
    color: 'primary',
    textColor: 'text-primary'
  }
  // {
  //   key: 'grid_income',
  //   title: '电网收入',
  //   icon: 'mdi-trending-up',
  //   color: 'success',
  //   textColor: 'text-success'
  // },
  // {
  //   key: 'grid_expense',
  //   title: '电网支出',
  //   icon: 'mdi-trending-down',
  //   color: 'error',
  //   textColor: 'text-error'
  // }
])

const tableData = computed(() => [
  {
    name: t('电网支出'),
    key: 'expense',
    prop: 'grid_expense'
  },
  {
    name: t('电网收入'),
    key: 'income',
    prop: 'grid_income'
  },
  {
    name: t('节能收益'),
    key: 'saving',
    prop: 'energy_saving'
  },
  {
    name: t('总支出'),
    key: 'expense',
    prop: 'total_expense'
  },
  {
    name: t('总收入'),
    key: 'income',
    prop: 'total_income'
  },
  {
    name: t('净利润'),
    key: 'profit',
    prop: 'profit'
  }
])

const chartOptions = computed(() => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      top: 5,
      selected: legendSelected.value,
      type: 'scroll'
    },
    grid: { left: '3%', right: '4%', bottom: '15%', containLabel: true },
    xAxis: {
      type: 'category',
      data: sysincomeData.value.times,
      splitLine: {
        //分割线
        show: false //控制分割线是否显示
      }
    },
    yAxis: {
      type: 'value',
      name: `${t('单位')}：${sysincomeData.value.currency || '--'}`
    },
    dataZoom: [
      {
        type: 'inside',
        show: true,
        start: 0,
        end: 20
      },
      {
        type: 'slider',
        show: true,
        start: 0,
        end: 20
      }
    ],
    series: [
      {
        name: t('总支出'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map((item) => item.total_expense),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('总收入'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map((item) => item.total_income),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('净利润'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map((item) => item.profit),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('电网支出'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map((item) => item.grid_expense),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('电网收入'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map((item) => item.grid_income),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('节能收益'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map((item) => item.energy_saving),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('充电电价'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map((item) => item.charge_price),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('放电电价'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map((item) => item.discharge_price),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('光伏日充电量'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map(
          (item) => item.delta_pv_day_chg_energy
        ),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('电网日充电量'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map(
          (item) => item.delta_grid_day_chg_energy
        ),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('电网日放电量'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map(
          (item) => item.delta_grid_day_dischg_energy
        ),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('日充电量'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map(
          (item) => item.delta_day_chg_energy
        ),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('日放电量'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: sysincomeData.value.datas?.map(
          (item) => item.delta_day_dischg_energy
        ),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      }
    ]
  }
})

const formatCurrency = computed(() => {
  return (value) => {
    if (!value) return '0.00'
    return new Intl.NumberFormat('zh-CN', {
      style: 'currency',
      currency: sysincomeData.value.currency || 'CNY'
    }).format(value)
    // return value.toFixed(2) + ' ' + sysincomeData.value.currency
  }
})

const getValueColor = (key) => {
  if (key === 'expense') return 'text-error'
  if (key === 'income' || key === 'saving') return 'text-success'
  if (key === 'profit') return 'text-primary'
  return ''
}
</script>

<template>
  <div class="pa-6 h-100 overflow-hidden d-flex w-100">
    <v-card
      class="pa-4 h-100 w-100 rounded-lg px-4 overflow-auto no-scrollbar"
      elevation="4"
    >
      <div class="d-flex justify-between align-center py-2 mb-4">
        <div class="text-h6">{{ $t('系统收益') }}</div>
        <div class="flex align-center" v-if="isHaveConfig">
          <v-chip class="ma-2" color="red" label v-if="isNew">
            <v-icon icon="mdi-new-box" start></v-icon>
            {{ $t('最新') }}
          </v-chip>
          <v-chip class="ma-2" color="orange" label>
            <v-icon icon="mdi-currency-cny" start></v-icon>
            {{ form.currency }}
          </v-chip>
          <v-chip class="ma-2" color="primary" label>
            <v-icon icon="mdi-earth" start></v-icon>
            {{ form.region }}
          </v-chip>
          <v-chip class="ma-2" color="teal" label>
            <v-icon icon="mdi-earth" start></v-icon>
            {{ form.scenario_description }}
          </v-chip>
          <v-btn-toggle
            v-model="toggle"
            variant="outlined"
            divided
            class="mr-4"
          >
            <!-- 0 -->
            <v-btn>{{ $t('日') }}</v-btn>
            <!-- 1 -->
            <v-btn>{{ $t('月') }}</v-btn>
            <!-- 2 -->
            <v-btn>{{ $t('年') }}</v-btn>
          </v-btn-toggle>
          <v-menu
            v-model="isShowDate"
            location="bottom"
            :close-on-content-click="false"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                v-model="getForm.formatDate"
                prepend-inner-icon="mdi-calendar-range"
                title="Date"
                variant="outlined"
                hide-details
                single-line
                v-bind="props"
                style="width: 200px"
              ></v-text-field>
            </template>

            <v-date-picker
              v-model="getForm.date"
              v-model:month="getForm.month"
              v-model:year="getForm.year"
              show-adjacent-months
              :allowed-dates="allowedDates"
              color="primary"
              elevation="4"
              class="date-picker"
              :view-mode="viewMode"
              @update:modelValue="handleDateChange"
              @update:month="handleMonthChange"
              @update:year="handleYearChange"
            ></v-date-picker>
          </v-menu>
          <!-- <v-btn
            height="48px"
            class="ml-4"
            color="primary"
            @click="isEdit = true"
            >{{ $t('修改配置') }}</v-btn
          > -->
        </div>
      </div>
      <div
        class="flex flex-column justify-center align-center h-50"
        v-if="!isHaveConfig"
      >
        <div class="text-h6 mb-4">{{ $t('请先配置系统收益信息') }}</div>
        <div class="flex align-center row flex-wrap my-2">
          <div class="text-body-1 left mr-2">{{ $t('收入区域') }}</div>
          <v-autocomplete
            v-model="form.region"
            item-value="code"
            item-title="name"
            clearable
            :placeholder="$t('选择地区')"
            :items="countryOptions"
            variant="outlined"
            hide-details
            style="width: 300px"
          ></v-autocomplete>
          <v-btn
            :loading="regionLoading"
            @click="setConfigFn('region')"
            class="ml-2"
            >{{ $t('确认') }}</v-btn
          >
        </div>
        <div class="flex align-center row flex-wrap my-2">
          <div class="text-body-1 left mr-2">{{ $t('收入场景') }}</div>
          <v-autocomplete
            v-model="form.scenario"
            item-value="value"
            clearable
            :placeholder="$t('选择场景')"
            :items="scenarioOptions"
            variant="outlined"
            hide-details
            style="width: 300px"
          ></v-autocomplete>
          <v-btn
            :loading="scenarioLoading"
            @click="setConfigFn('scenario')"
            class="ml-2"
            >{{ $t('确认') }}</v-btn
          >
        </div>
        <!-- <v-btn
          height="48px"
          class="mt-6"
          color="primary"
          @click="isEdit = false"
          v-if="isHaveConfig"
          >{{ $t('查看收益数据') }}</v-btn
        > -->
      </div>
      <div v-else>
        <empty v-if="isShowEmpty" />
        <!-- 在现有模板中添加数据展示部分 -->
        <div class="mt-4" v-else>
          <!-- 概览卡片 -->
          <v-row class="mb-6">
            <v-col
              cols="12"
              md="4"
              v-for="item in overviewCards"
              :key="item.key"
            >
              <v-card elevation="2" class="pa-4 text-center">
                <v-icon
                  :icon="item.icon"
                  :color="item.color"
                  size="40"
                  class="mb-2"
                ></v-icon>
                <div class="text-h4 font-weight-bold" :class="item.textColor">
                  {{ formatCurrency(currentData('total')[item.key]) }}
                </div>
                <div class="text-body-2 text-grey">{{ $t(item.title) }}</div>
              </v-card>
            </v-col>
          </v-row>

          <!-- 详细数据表格 -->
          <v-card elevation="2" class="mb-6">
            <v-card-title>{{ $t('收益详情') }}</v-card-title>
            <v-row>
              <v-col cols="12" md="4" v-if="toggle == 0 || isNew">
                <v-list lines="two">
                  <v-list-subheader inset>{{ $t('日') }}</v-list-subheader>
                  <v-list-item
                    v-for="item in tableData"
                    :key="item.name"
                    :title="item.name"
                  >
                    <template v-slot:append>
                      <span :class="getValueColor(item.key)">
                        {{ formatCurrency(currentData('daily')[item.prop]) }}
                      </span>
                    </template>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col
                cols="12"
                md="4"
                v-if="toggle == 1 || toggle == 0 || isNew"
              >
                <v-list lines="two">
                  <v-list-subheader inset>{{ $t('月') }}</v-list-subheader>
                  <v-list-item
                    v-for="item in tableData"
                    :key="item.name"
                    :title="item.name"
                  >
                    <template v-slot:append>
                      <span :class="getValueColor(item.key)">
                        {{ formatCurrency(currentData('monthly')[item.prop]) }}
                      </span>
                    </template>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col
                cols="12"
                md="4"
                v-if="toggle == 2 || toggle == 0 || isNew"
              >
                <v-list lines="two">
                  <v-list-subheader inset>{{ $t('年') }}</v-list-subheader>
                  <v-list-item
                    v-for="item in tableData"
                    :key="item.name"
                    :title="item.name"
                  >
                    <template v-slot:append>
                      <span :class="getValueColor(item.key)">
                        {{ formatCurrency(currentData('yearly')[item.prop]) }}
                      </span>
                    </template>
                  </v-list-item>
                </v-list>
              </v-col>
              <v-col
                cols="12"
                md="8"
                v-if="toggle == 1 || toggle == 2"
                style="min-height: 450px"
              >
                <BarEchart
                  :data="
                    toggle == 1
                      ? sysincomeData.daily_records
                      : sysincomeData.monthly_records
                  "
                  :currency="sysincomeData.currency"
                  class="w-100 h-100 mb-2"
                />
              </v-col>
            </v-row>
          </v-card>

          <!-- 图表展示 -->
          <v-card elevation="2" v-if="toggle == 0">
            <v-card-title>{{ $t('收益趋势') }}</v-card-title>
            <div style="height: 400px" class="pa-4">
              <BaseEchart
                width="100%"
                height="100%"
                :options="chartOptions"
                ref="incomeChart"
              />
            </div>
          </v-card>
        </div>
      </div>
    </v-card>
  </div>
</template>

<style lang="scss" scoped>
.left {
  flex: 0 0 auto; /* 不可拉伸，宽度不变 */
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容显示出来 */
  text-align: right;
}
</style>
