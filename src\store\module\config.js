import { defineStore, storeToRefs } from 'pinia'
import { ref } from 'vue'
import {
  systemInfoSet,
  getSystemInfo,
  topologyInformation,
  topologyInfoSet,
  getIp,
  ipSet,
  getSystemStatus,
  getLocalTime,
  getRealtimePointData,
  getWebConfig,
  setWebConfig,
  getVersionInfo,
  getTimeZoneList,
  setTimeZone
} from '@/api/config'
import {
  getDeviceData
} from '@/api/device'
import { i18n } from '@/locale'
import { isEmpty } from 'lodash-es'
import { sortFn } from '@/utils'
import { useGlobalStore } from '../global'
import dayjs from '@/utils/date'

import Info from '../../assets/img/info.webp'
import Run from '../../assets/img/run.webp'
import Alarm from '../../assets/img/alarm.webp'

export const useConfigStore = defineStore(
  'config',
  () => {
    const { snackbar, snackbarText } = storeToRefs(useGlobalStore())
    const homeConfig = ref({
      top: [
        {
          name: '系统信息',
          id: 1,
          desc: '显示系统概括信息，项目名称、地址等',
          img: Info,
          type: 'system_info'
        },
        {
          name: '系统状态',
          id: 2,
          desc: '显示系统运行状态，是否离线，通讯是否在线等',
          img: Run,
          type: 'system_status'
        },
        {
          name: '告警信息',
          id: 4,
          desc: '显示最新的告警信息',
          img: Alarm,
          type: 'fault'
        },
        // {
        //   name: '实时时间',
        //   id: 3,
        //   desc: '显示当前时间',
        //   img: Time,
        //   type: 'charge-discharge'
        // },
      ]
    })

    /**
     * 系统设置
     */
    const systemInfoSetFn = async (data) => {
      const res = await systemInfoSet(data)
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
    }
    const systemInfo = ref({})
    const getSystemInfoFn = async () => {
      const res = await getSystemInfo()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      systemInfo.value = res.data
    }
    const isModule = ref(false)
    const moduleData = ref([])
    const pointData = ref([])
    const getTreePointDataFn = async (queryInfo) => {
      moduleData.value = []
      pointData.value = []
      const res = await getDeviceData({ deviceId: queryInfo.deviceId })
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      let item = res.data
      // res.data.forEach((item) => {
      if (item.modules.length) {
        // 有模块
        isModule.value = true
        item.modules.forEach((module, moduleIndex) => {
          module.points.forEach((point) => {
            let key = Object.keys(point)[0]
            if (key == (queryInfo.dataType == '1' ? 'analog' : 'status')) {
              point[key].sort(sortFn)
              moduleData.value = [...moduleData.value, {
                id: `${module.device_id}_${module.module_name}`,
                title: module.module_name
              }]
              pointData.value = [...pointData.value, {
                id: `${module.device_id}_${module.module_name}`,
                title: module.module_name,
                module: true,
                children: point[key].map(item => {
                  return {
                    id: item.point_id,
                    title: item.point_name,
                    units: item.units
                  }
                })
              }]
            }
          })
        })
      } else {
        if (!item.points || !item.points.length) return
        isModule.value = false
        item.points.forEach((point) => {
          let key = Object.keys(point)[0]
          if (key == (queryInfo.dataType == '1' ? 'analog' : 'status')) {
            point[key].sort(sortFn)
            point[key].forEach(item => {
              pointData.value = [...pointData.value, {
                id: item.point_id,
                title: item.point_name,
                units: item.units
              }]
            })
          }
        })
      }
      // })
    }

    /**
     * 拓补图
     */
    const editorConfig = ref()
    const topologyInformationFn = async () => {
      const res = await topologyInformation()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      if (isEmpty(res.data.topology_info)) {
        editorConfig.value = {}
      } else {
        editorConfig.value = JSON.parse(res.data.topology_info)
      }
    }
    const topologyInfoSetFn = async (data) => {
      const res = await topologyInfoSet(data)
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
    }
    const iconsData = ref([])
    const getRealtimePointDataFn = async (data) => {
      const res = await getRealtimePointData(data)
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      return res.data
    }
    /**
     * 首页配置
     */
    const homeCardData = ref([])

    /**
     * ip
     */
    const ipData = ref({})
    const getIpFn = async () => {
      const res = await getIp()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      if (res?.data.mode) {
        ipData.value = {
          mode: res.mode,
          ip: res.address,
          netmask: res.netmask,
          gateway: res.gateway,
          name: 'eth0'
        }
      } else {
        ipData.value = {
          mode: 'auto',
          name: 'eth0',
          ip: '',
          netmask: '',
          gateway: ''
        }
      }
    }
    const ipSetFn = async (data) => {
      const res = await ipSet(data)
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
    }

    /**
     * 获取系统状态
     */
    const systemStatus = ref()
    const getSystemStatusFn = async () => {
      const res = await getSystemStatus()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      systemStatus.value = res.data.status
      return res.data.status
    }

    /**
     * 获取后端时间
     */
    const localTime = ref()
    const localTimeZone = ref()
    const localTimer = ref(null)
    const getLocalTimeFn = async () => {
      const res = await getLocalTime()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      localTime.value = res.data.time
      localTimeZone.value = res.data.timezone
      return res.data.time
    }
    const startLocalTimeUpdate = () => {
      getLocalTimeFn()
        .then(() => {
          // 先清理旧的定时器
          if (localTimer.value) {
            clearInterval(localTimer.value)
          }

          localTimer.value = setInterval(() => {
            localTime.value = dayjs(localTime.value)
              .add(1, 's')
              .format('YYYY-MM-DD HH:mm:ss')
          }, 1000)
        })
    }
    const stopLocalTimeUpdate = () => {
      if (localTimer.value) {
        clearInterval(localTimer.value)
        timer.value = null
      }
    }
    const timeZoneList = ref([])
    const getTimeZoneListFn = async () => {
      const res = await getTimeZoneList()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      timeZoneList.value = res.data.map(item => {
        return {
          title: item,
          value: item
        }
      })
    }
    const setTimeZoneFn = async (data) => {
      const res = await setTimeZone(data)
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
    }

    /**
     * webConfig
     */
    const deviceConfigData = ref([])
    const getWebConfigFn = async (data) => {
      const res = await getWebConfig(JSON.stringify(data))
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      const { key } = data
      if (isEmpty(res.data)) {
        if (key == 'homeCard') {
          homeCardData.value = []
        } else if (key == 'icon') {
          iconsData.value = []
        } else if (key == 'device') {
          deviceConfigData.value = []
        }
        return
      }
      if (key == 'homeCard') {
        homeCardData.value = JSON.parse(res.data)
      } else if (key == 'icon') {
        iconsData.value = JSON.parse(res.data)
      } else if (key == 'device') {
        deviceConfigData.value = JSON.parse(res.data)
      }
    }
    const setWebConfigFn = async (data) => {
      const res = await setWebConfig(data)
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
    }

    /**
     * 获取版本
     */
    const version = ref()
    const getVersionInfoFn = async () => {
      const res = await getVersionInfo()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      version.value = res.data.ems_version
    }

    /**
     * 设备管理
     */
    const deviceConfigList = ref([])
    const isEdit = ref(false)

    /**
     * 设置循环时长
     */
    const cycleTime = ref(2000)

    const logData = ref([])
    const logWs = ref(null)

    return {
      homeConfig,
      systemInfoSetFn,
      systemInfo,
      getSystemInfoFn,
      isModule,
      moduleData,
      pointData,
      getTreePointDataFn,
      editorConfig,
      topologyInformationFn,
      topologyInfoSetFn,
      iconsData,
      homeCardData,
      ipData,
      getIpFn,
      ipSetFn,
      systemStatus,
      getSystemStatusFn,
      localTime,
      localTimeZone,
      getLocalTimeFn,
      getRealtimePointDataFn,
      getWebConfigFn,
      setWebConfigFn,
      deviceConfigData,
      getVersionInfoFn,
      version,
      deviceConfigList,
      isEdit,
      cycleTime,
      getTimeZoneListFn,
      setTimeZoneFn,
      timeZoneList,
      logData,
      logWs,
      startLocalTimeUpdate,
      stopLocalTimeUpdate
    }
  },
  {
    // persist: true,
    persist: [
      {
        pick: ['editorConfig', 'cycleTime'],
        storage: localStorage
      }
    ]
  }
)
