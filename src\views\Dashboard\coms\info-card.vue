<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-27 15:33:04
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-09-27 15:34:40
 * @FilePath: \ems_manage\src\views\Dashboard\coms\info-card.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { toRefs, ref } from 'vue'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'

const loading = ref(false)
const { systemInfo } = toRefs(useConfigStore())
const { snackbar, snackbarText } = toRefs(useGlobalStore())

// loading.value = true
// useConfigStore()
//   .getSystemInfoFn()
//   .then((res) => {
//     if (res?.msg) {
//       snackbar.value = true
//       snackbarText.value = res.msg
//       return
//     }
//     loading.value = false
//   }).catch(() => {
//     loading.value = false
//   })
</script>

<template>
  <v-card
    height="170px"
    elevation="4"
    class="rounded-lg"
    :disabled="loading"
    :loading="loading"
  >
    <template v-slot:loader="{ isActive }">
      <v-progress-linear
        :active="isActive"
        color="primary"
        height="4"
        indeterminate
      ></v-progress-linear>
    </template>
    <div class="d-flex flex-no-wrap justify-space-between h-full">
      <div>
        <v-card-title> {{ $t('项目信息') }} </v-card-title>

        <div class="text-subtitle-2 ml-4 mt--5px">
          {{ $t('项目名称') }}：<span>{{ systemInfo.project_name }}</span>
        </div>
        <div class="text-subtitle-2 ml-4 mt-5px">
          {{ $t('装机容量') }}：<span class="font-600">{{
            systemInfo.installed_capacity
          }}</span>
          kWh
        </div>
        <div class="text-subtitle-2 ml-4 mt-5px">
          {{ $t('装机功率') }}：<span class="font-600">{{
            systemInfo.installed_power
          }}</span>
          kW
        </div>
        <div class="text-subtitle-2 ml-4 mt-5px">
          {{ $t('项目地址') }}：<span>{{ systemInfo.project_address }}</span>
        </div>
      </div>

      <div class="d-flex align-center mr-4">
        <img
          src="../../../assets/img/info.webp"
          style="width: 100px; height: 100px"
        />
      </div>
    </div>
  </v-card>
</template>

<style lang="scss" scoped></style>
