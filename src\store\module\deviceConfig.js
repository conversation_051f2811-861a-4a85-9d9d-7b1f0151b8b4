/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-04-16 17:24:26
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-23 12:57:13
 * @FilePath: \ems_manage\src\store\module\deviceConfig.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore, storeToRefs } from 'pinia'
import { ref } from 'vue'
import {
  deviceFiles,
  getDeviceFile,
  setDeviceFile,
  protocolFiles,
  deviceClassNames,
  adaptorTypes,
  serialPorts,
  hardwareModel
} from '@/api/deviceConfig'
import { useGlobalStore } from '../global'
import { isEmpty } from 'lodash-es'

export const useDeviceConfigStore = defineStore(
  'deviceConfig',
  () => {
    const { snackbar, snackbarText } = storeToRefs(useGlobalStore())
    const filesData = ref([])
    const getFilesData = async () => {
      const res = await deviceFiles()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      filesData.value = res.data.files.map((item, index) => {
        return {
          id: index,
          fileName: item
        }
      })
    }

    const queryInfo = ref({
      fileName: ''
    })
    const deviceConfigList = ref([])
    const isEdit = ref(false)
    const getFileInfoData = async () => {
      const res = await getDeviceFile(queryInfo.value)
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return new Error(res.msg)
      }
      deviceConfigList.value = parseXML(res.data)
      return res.data
    }
    /**
     * 修改配置文件
     */
    const setDeviceFileFn = async () => {
      let xmlString = convertToXML(deviceConfigList.value)
      const res = await setDeviceFile(
        { fileName: queryInfo.value.fileName, content: xmlString })
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        throw res.msg
      }
      getFileInfoData()
    }
    /**
     * 设备类名
     */
    const deviceClassData = ref([])
    const getDeviceClassNames = async () => {
      const res = await deviceClassNames()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return
      }
      deviceClassData.value = res.data.classNames.map((item) => {
        let keys = Object.keys(item)
        return {
          value: keys[0],
          title: keys[0],
          det: item[keys[0]]
        }
      })
      return res.data
    }
    /**
     * 适配器类型
     */
    const adaptorTypeOptions = ref([])
    const getAdaptorTypes = async () => {
      const res = await adaptorTypes()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return
      }
      adaptorTypeOptions.value = res.data.adaptors.map((item) => {
        return {
          value: item,
          title: item
        }
      })
      return res.data
    }
    /**
     * 协议文件
     */
    const protocolFileData = ref({})
    const getProtocolFiles = async () => {
      const res = await protocolFiles()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return
      }
      let data = res.data.files.map((item) => {
        return {
          value: item,
          title: item
        }
      })
      protocolFileData.value = {
        xml: data.filter((item) => item.value.endsWith('.xml')),
        json: data.filter((item) => item.value.endsWith('.json'))
      }
      return res.data
    }
    /**
     * 串口信息
     */
    const serialPortData = ref([])
    const getSerialPorts = async () => {
      const res = await serialPorts()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return
      }
      serialPortData.value = res.data.serialPorts.map((item) => {
        return {
          value: item.split(':')[1],
          title: item
        }
      })
      return serialPortData.value
    }
    /**
     * 硬件信息
     */
    const hardwareModelData = ref()
    const getHardwareModel = async () => {
      const res = await hardwareModel()
      if (res.code !== 200) {
        snackbar.value = true
        snackbarText.value = res.msg
        return
      }
      hardwareModelData.value = res.data.model
      return res.data
    }

    return {
      filesData,
      getFilesData,
      queryInfo,
      getFileInfoData,
      deviceConfigList,
      isEdit,
      deviceClassData,
      getDeviceClassNames,
      adaptorTypeOptions,
      getAdaptorTypes,
      protocolFileData,
      getProtocolFiles,
      setDeviceFileFn,
      serialPortData,
      getSerialPorts,
      hardwareModelData,
      getHardwareModel
    }
  },
)

// 辅助函数：将 DOM 元素的属性解析为对象
const parseAttributes = (element) => {
  const attributes = {}
  for (let attr of element.attributes) {
    attributes[attr.name] = attr.value
  }
  return attributes
}
// 解析 XML 数据
const parseXML = (xmlData) => {
  // 使用 DOMParser 解析 XML 数据
  const parser = new DOMParser()
  const xmlDoc = parser.parseFromString(xmlData, 'application/xml')

  // 检查是否有解析错误
  const parseError = xmlDoc.getElementsByTagName('parsererror')
  if (parseError.length > 0) {
    console.error('XML 解析失败:', parseError[0].textContent)
    return
  }

  // 获取所有 collector 标签
  const collectorsList = xmlDoc.getElementsByTagName('collector')
  return Array.from(collectorsList).map((collector) => {
    // 提取 collector 的属性
    const collectorObj = { ...parseAttributes(collector), protocols: [] }

    // 提取 adaptor 子标签
    const adaptors = collector.getElementsByTagName('adaptor')
    Array.from(adaptors).forEach((adaptor) => {
      collectorObj.protocols.push({
        type: 'adaptor',
        ...parseAttributes(adaptor)
      })
    })

    // 提取 control 子标签
    const controls = collector.getElementsByTagName('control')
    Array.from(controls).forEach((control) => {
      collectorObj.protocols.push({
        type: 'control',
        ...parseAttributes(control)
      })
    })

    // 提取 fault_record 子标签
    const faultRecords = collector.getElementsByTagName('fault_record')
    Array.from(faultRecords).forEach((faultRecord) => {
      collectorObj.protocols.push({
        type: 'fault_record',
        ...parseAttributes(faultRecord)
      })
    })

    return collectorObj
  })
}

// 将对象数组转换为 XML 格式
const convertToXML = (collectorsList) => {
  const xmlDoc = document.implementation.createDocument(null, "root");
  const collectorsElement = xmlDoc.createElement("collectors");

  collectorsList.forEach((collector) => {
    const collectorElement = xmlDoc.createElement("collector");

    // 添加 collector 属性
    Object.entries(collector).forEach(([key, value]) => {
      if (key !== "protocols") {
        collectorElement.setAttribute(key, value);
      }
    });

    // 添加 protocols 子标签
    collector.protocols.forEach((protocol) => {
      const protocolElement = xmlDoc.createElement(protocol.type);

      // 添加 protocol 属性
      Object.entries(protocol).forEach(([key, value]) => {
        if (key !== "type") {
          if (!isEmpty(value)) protocolElement.setAttribute(key, value);
        }
      });

      collectorElement.appendChild(protocolElement);
    });

    collectorsElement.appendChild(collectorElement);
  });

  xmlDoc.documentElement.appendChild(collectorsElement);

  // 将 XML 文档转换为字符串
  const serializer = new XMLSerializer();
  return serializer.serializeToString(xmlDoc);
}
