<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-06 14:45:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-04 11:19:09
 * @FilePath: \ems_manage\src\views\Dashboard\lineEchart.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const props = defineProps({
  data: {
    type: Array,
    default: () => []
  },
  currency: {
    type: String
  }
})

const options = computed(() => {
  return {
    tooltip: {
      trigger: 'axis'
    },
    legend: {
      type: 'plain',
      textStyle: {
        fontSize: 12,
        fontWeight: 400
        // color: 'rgba(0, 0, 0, .9)',
        // lineHeight: 18
      },
      itemGap: 30,
      top: 5
    },
    grid: {
      left: '3%',
      right: '2%',
      bottom: '0%',
      containLabel: true
    },
    xAxis: {
      axisLine: {
        lineStyle: {
          // color: '#fff'
        }
      },
      axisTick: {
        //y轴刻度线
        show: true
      },
      splitLine: {
        //分割线
        show: false, //控制分割线是否显示
        lineStyle: {
          //分割线的样式
          color: 'rgba(81, 82, 85, 0.3)',
          width: 1,
          type: 'solid'
        }
      },
      data: props.data?.map((item) => item.date)
    },
    yAxis: {
      type: 'value',
      name: `${t('单位')}：${props.currency || '--'}`,
      axisLine: {
        lineStyle: {
          // color: '#fff'
        }
      },
      splitLine: {
        //分割线
        show: false, //控制分割线是否显示
        lineStyle: {
          //分割线的样式
          color: 'rgba(81, 82, 85, 0.3)',
          width: 1,
          type: 'solid'
        }
      }
    },
    dataZoom: {
      type: 'inside'
    },
    series: [
      {
        name: t('总支出'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: props.data?.map((item) => item.total_expense),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('总收入'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: props.data?.map((item) => item.total_income),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      },
      {
        name: t('净利润'),
        barGap: 0,
        smooth: true,
        type: 'bar',
        barWidth: 10,
        data: props.data?.map((item) => item.profit),
        animationDuration: 2800,
        animationEasing: 'cubicInOut'
      }
    ]
  }
})
</script>

<template>
  <div>
    <BaseEchart
      width="100%"
      height="100%"
      :options="options"
      ref="myChart"
    ></BaseEchart>
  </div>
</template>

<style lang="scss" scoped></style>
