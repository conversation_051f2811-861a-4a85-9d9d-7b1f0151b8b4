<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-09 18:59:57
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-18 10:27:10
 * @FilePath: \ems_manage\src\layout\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import Navigation from './coms/Navigation.vue'
import ButtonNavigation from './coms/ButtonNavigation.vue'
import Setting from './coms/Setting.vue'
import TopNav from './coms/TopNav.vue'
</script>

<template>
  <v-layout class="rounded rounded-md h-100 w-100">
    <Navigation></Navigation>
    <ButtonNavigation></ButtonNavigation>
    <v-main class="overflow-auto">
      <TopNav></TopNav>
      <router-view />
    </v-main>
    <Setting></Setting>
  </v-layout>
</template>

<style lang="scss" scoped></style>
