<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-09 18:59:57
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-18 10:27:10
 * @FilePath: \ems_manage\src\layout\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import Navigation from './coms/Navigation.vue'
import ButtonNavigation from './coms/ButtonNavigation.vue'
import Setting from './coms/Setting.vue'
import TopNav from './coms/TopNav.vue'
</script>

<template>
  <v-layout class="hmi-layout h-100 w-100">
    <Navigation></Navigation>
    <ButtonNavigation></ButtonNavigation>
    <v-main class="hmi-main overflow-auto">
      <TopNav></TopNav>
      <div class="hmi-content-wrapper">
        <router-view />
      </div>
    </v-main>
    <Setting></Setting>
  </v-layout>
</template>

<style lang="scss" scoped>
.hmi-layout {
  border-radius: 0 !important; /* HMI屏幕通常不需要圆角 */
  background: #f5f5f5;
}

.hmi-main {
  background: #f5f5f5;

  /* 确保内容区域适合HMI屏幕 */
  @media screen and (max-width: 1280px) and (min-width: 1024px) {
    padding-top: 0;
  }
}

.hmi-content-wrapper {
  padding: 16px;
  min-height: calc(100vh - 64px); /* 减去顶部导航高度 */

  /* HMI屏幕优化 */
  @media screen and (max-width: 1280px) and (min-width: 1024px) {
    padding: 12px;
    min-height: calc(100vh - 64px);
  }
}

/* 响应式优化 */
@media screen and (max-width: 1024px) {
  .hmi-content-wrapper {
    padding: 8px;
  }
}
</style>
