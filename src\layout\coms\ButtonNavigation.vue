<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-10 10:49:08
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-04-24 12:00:48
 * @FilePath: \ems_manage\src\views\layout\coms\Navigation.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <v-bottom-navigation
    v-model="value"
    :active="mobile"
    color="teal"
    grow
    bg-color="primary"
    base-color="#fff"
  >
    <template v-for="item in items">
      <v-btn :to="item.path" color="#fff" v-if="item.path">
        <v-icon>{{ item.icon }}</v-icon>

        {{ item.text }}
      </v-btn>
      <v-menu v-else>
        <template v-slot:activator="{ props }">
          <v-btn color="#fff" v-bind="props">
            <v-icon>{{ item.icon }}</v-icon>

            {{ item.text }}
          </v-btn>
        </template>

        <v-list bg-color="primary">
          <v-list-item
            v-for="(proItem, proIndex) in item.children"
            :key="proIndex"
            :prepend-icon="proItem.icon"
            color="#fff"
            base-color="#fff"
            :to="proItem.path"
            :value="proItem"
          >
            <v-list-item-title>{{ $t(proItem.text) }}</v-list-item-title>
          </v-list-item>
        </v-list>
      </v-menu>
    </template>
  </v-bottom-navigation>
</template>
<script lang="ts" setup>
import { ref, computed, toRefs } from 'vue'
import { useDisplay } from 'vuetify'
import { useUserStore } from '@/store/module/user'
import { useConfigStore } from '@/store/module/config'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { mobile, lg, md, sm, xs } = useDisplay()
const value = ref(1)
const { menuInfo } = toRefs(useUserStore())

const items = computed(() => {
  const arr = menuInfo.value.map((item) => {
    return {
      text: t(item.text),
      icon: item.icon,
      path: item.path,
      sort: item.sort
    }
  })
  if (lg.value) {
    return [...arr]
  } else if (md.value) {
    return [
      ...arr.filter((item, index) => index < 3),
      {
        text: t('更多'),
        icon: 'mdi-dots-horizontal',
        path: '',
        children: [...arr.filter((item, index) => index >= 3)]
      }
    ]
  } else if (sm.value) {
    return [
      ...arr.filter((item, index) => index < 2),
      {
        text: t('更多'),
        icon: 'mdi-dots-horizontal',
        path: '',
        children: [...arr.filter((item, index) => index >= 2)]
      }
    ]
  } else if (xs.value) {
    return [
      ...arr.filter((item, index) => index < 2),
      {
        text: t('更多'),
        icon: 'mdi-dots-horizontal',
        path: '',
        children: [...arr.filter((item, index) => index >= 2)]
      }
    ]
  }
})
</script>

<style scoped lang="scss"></style>
