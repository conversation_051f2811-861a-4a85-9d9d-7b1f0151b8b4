<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-03-10 11:32:51
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-08-26 16:23:00
 * @FilePath: \ems_manage_copy\src\components\lang-select\src\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, toRefs } from 'vue'
import { useLocale } from 'vuetify'
import { initLocale, langArr } from '@/locale'
import { useRouter } from 'vue-router'
import { useGlobalStore } from '@/store/global'

const router = useRouter()
const { current } = useLocale()
const lang = ref()
const init = () => {
  current.value = current.value ?? 'zhHans'
  lang.value = current.value
}
init()
const handleChangeLang = (lang) => {
  current.value = lang
  localStorage.setItem('lang', lang)
  initLocale(lang)
  router.go(0)
}
</script>

<template>
  <v-autocomplete
    v-model="lang"
    item-value="value"
    clearable
    :items="langArr"
    variant="outlined"
    hide-details
    @update:model-value="handleChangeLang"
  ></v-autocomplete>
</template>

<style lang="scss" scoped></style>
