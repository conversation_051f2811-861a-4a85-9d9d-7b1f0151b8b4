<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-07 17:18:43
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-05-13 17:58:23
 * @FilePath: \ems_manage\src\views\Log\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-07 17:18:43
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-04-24 15:08:36
 * @FilePath: \ems_manage\src\views\Log\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, toRefs } from 'vue'
import dayjs from '@/utils/date'
import { useLogStore } from '@/store/module/log'
import { useI18n } from 'vue-i18n'
import { useGlobalStore } from '@/store/global'
import { useAllowedDate } from '@/hook/useAllowedDate'
import { debounce } from 'lodash-es'

const { allowedDates } = useAllowedDate()
const {
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const { t } = useI18n()
const { logData, pageInfo, logQueryInfo } = toRefs(useLogStore())

const headers = ref([
  {
    key: 'param_type',
    sortable: false,
    title: t('参数类型'),
    align: 'center'
  },
  { key: 'param', title: t('属性'), sortable: false, align: 'center' },
  { key: 'new', title: t('新值'), sortable: false, align: 'center' },
  { key: 'old', title: t('旧值'), sortable: false, align: 'center' },
  { key: 'strategy', title: t('策略'), sortable: false, align: 'center' },
  { key: 'client_ip', title: t('主机'), sortable: false, align: 'center' },
  { key: 'user', title: t('操作人员'), sortable: false, align: 'center' },
  { key: 'time', title: t('发生时间'), sortable: false, align: 'center' },
  { key: 'remarks', title: t('备注'), sortable: false }
])

const date = ref([])
date.value = logQueryInfo.value.date ? [logQueryInfo.value.date] : []
const formatDate = ref()
formatDate.value = logQueryInfo.value.date
const isShowDate = ref(false)
const handleDateChange = async (e) => {
  formatDate.value = dayjs(e).format('YYYY-MM-DD')
  isShowDate.value = false
  logQueryInfo.value.date = formatDate.value
  loading.value = true
  await useLogStore().getLogDataFn()
  loading.value = false
}
const handleClearClick = async () => {
  logQueryInfo.value.date = ''
  loading.value = true
  await useLogStore().getLogDataFn()
  loading.value = false
}

const loading = ref(false)
const handleChangeSize = async ({ page, itemsPerPage }) => {
  pageInfo.value.pageSize = itemsPerPage
  pageInfo.value.pageIndex = page
  loading.value = true
  await useLogStore().getLogDataFn({
    pageSize: itemsPerPage,
    pageIndex: page
  })
  loading.value = false
}
const handleSearchChange = async () => {
  loading.value = true
  await useLogStore().getLogDataFn()
  loading.value = false
}
const searchChange = debounce(handleSearchChange, 500)

/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value) => {
  if (isShowKeyboard.value) return
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  logQueryInfo.value.paramType = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}
</script>

<template>
  <div class="pa-6 h-100 overflow-hidden d-flex">
    <v-card
      class="pa-4 h-100 w-100 rounded-lg px-4 overflow-auto no-scrollbar"
      elevation="4"
      :title="$t('操作日志')"
    >
      <template v-slot:text>
        <v-row no>
          <v-col>
            <v-text-field
              v-model="logQueryInfo.paramType"
              label="Search Parameter Type"
              prepend-inner-icon="mdi-magnify"
              variant="outlined"
              hide-details
              single-line
              :clearable="true"
              @click:control="handleShow($event, logQueryInfo.paramType)"
              @update:modelValue="searchChange"
            ></v-text-field>
          </v-col>
          <v-col>
            <v-menu
              v-model="isShowDate"
              location="bottom"
              :close-on-content-click="false"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  v-model="formatDate"
                  prepend-inner-icon="mdi-calendar-range"
                  :label="$t('日期')"
                  variant="outlined"
                  hide-details
                  single-line
                  :clearable="true"
                  v-bind="props"
                  @click:clear="handleClearClick"
                ></v-text-field>
              </template>

              <v-date-picker
                v-model="date"
                show-adjacent-months
                :allowed-dates="allowedDates"
                color="primary"
                elevation="4"
                class="date-picker"
                @update:modelValue="handleDateChange"
              ></v-date-picker>
            </v-menu>
          </v-col>
        </v-row>
      </template>

      <v-data-table-server
        :headers="headers"
        :items="logData"
        :loading="loading"
        :items-per-page="pageInfo.pageSize"
        :items-length="pageInfo.total"
        @update:options="handleChangeSize"
        :items-per-page-options="[
          { value: 10, title: '10' },
          { value: 25, title: '25' },
          { value: 50, title: '50' },
          { value: 100, title: '100' }
        ]"
        show-current-page
      >
        <template v-slot:loading>
          <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
        </template>
      </v-data-table-server>
    </v-card>
  </div>
</template>

<style lang="scss" scoped></style>
