<script setup>
import { ref, toRefs, watchEffect, onUnmounted, watch, computed } from 'vue'
import { useDeviceStore } from '@/store/module/device'
import { isEmpty } from 'lodash-es'

import DataItem from '@/components/data-item'

const emits = defineEmits(['backClick'])

const { clusterData, clusterPointData, batteryTypeDeviceId } = toRefs(
  useDeviceStore()
)
const props = defineProps({
  cellData: { type: Object, default: () => {} }
})
const isCluster = ref(2)
const packCalcData = ref([
  {
    value: 'maxV',
    title: '最高单体电压',
    unit: 'vUnit'
  },
  {
    value: 'maxT',
    title: '最高单体温度',
    unit: 'tUnit'
  },
  {
    value: 'minV',
    title: '最低单体电压',
    unit: 'vUnit'
  },
  {
    value: 'minT',
    title: '最低单体温度',
    unit: 'tUnit'
  }
])
const pointData = ref()
const handlePackItemClick = (key) => {
  pointData.value = props.cellData[key].data
  isCluster.value = 3
}

const handleBackClick = () => {
  if (isCluster.value == 2) {
    emits('backClick')
  } else if (isCluster.value == 3) {
    isCluster.value = 2
  }
}
</script>

<template>
  <div class="pa-4 w-100">
    <div v-if="isCluster == 2" class="flex flex-wrap w-100">
      <template v-if="!isEmpty(props.cellData)">
        <v-card
          variant="outlined"
          v-for="(value, key) in props.cellData"
          class="rounded-lg pa-4 ma-4"
          style="width: 300px; height: 250px"
          :key="key"
          @click="handlePackItemClick(key)"
        >
          <div class="flex align-center justify-space-between mb-2 w-100">
            <div class="flex align-center" style="width: 87%">
              <img
                src="../../assets/img/device-module.png"
                alt=""
                style="width: 25px"
              />
              <div class="ml-2 font-600 line1" style="width: 80%" :title="key">
                {{ key }}
              </div>
            </div>
            <img
              src="../../assets/img/more.png"
              alt=""
              style="width: 30px; height: 20px"
            />
          </div>
          <div
            v-for="item in packCalcData"
            :key="item.value"
            class="py-3 flex align-center"
          >
            <img
              src="../../assets/img/circle.png"
              alt=""
              style="width: 20px; height: 20px"
              class="mr-2"
            />
            <div
              style="width: 90%"
              class="line1"
              :title="`${$t(item.title)}：${value[item.value]} ${
                value[item.unit]
              }`"
            >
              {{ $t(item.title) }}：{{ value[item.value] }}
              {{ value[item.unit] }}
            </div>
          </div>
        </v-card>
      </template>
      <empty v-else />
    </div>
    <div v-if="isCluster == 3" class="w-100">
      <template v-if="!isEmpty(pointData)">
        <div class="cell-wrapper">
          <div
            v-for="(value, key) in pointData"
            :key="key"
            style="width: 122px"
            class="ma-2"
          >
            <v-hover
              v-if="packCalcData.findIndex((item) => item.value == key) == -1"
            >
              <template v-slot:default="{ isHovering, props }">
                <div
                  v-bind="props"
                  style="position: relative; width: 100%; height: 150px"
                  class="flex flex-column align-center"
                >
                  <div
                    style="
                      width: 40%;
                      height: 15%;
                      background-color: #e9e9e9;
                      border-radius: 20px;
                      position: absolute;
                      top: 0;
                      left: 30%;
                    "
                    class="flex flex-justify-center align-center"
                    :class="{ 'elevation-4': isHovering }"
                  >
                    <span v-if="isHovering" style="font-size: 14px">{{
                      key + 1
                    }}</span>
                  </div>
                  <div
                    style="
                      width: 100%;
                      height: 85%;
                      background-color: #e9e9e9;
                      border-radius: 20px;
                      margin-top: 10%;
                      padding-top: 10%;
                    "
                    class="flex flex-column align-center"
                    :class="{ 'elevation-4': isHovering }"
                  >
                    <div class="flex align-center">
                      <img
                        src="../../assets/img/cellV.svg"
                        alt=""
                        style="width: 18px"
                      />
                      <div style="color: #757575">{{ $t('电压') }}</div>
                    </div>
                    <div>
                      <span style="font-weight: 600">{{ value.voltage }}</span>
                      {{ value.vUnit }}
                    </div>
                    <div class="flex align-center">
                      <img
                        src="../../assets/img/cellT.svg"
                        alt=""
                        style="width: 18px"
                      />
                      <div style="color: #757575">{{ $t('温度') }}</div>
                    </div>
                    <div>
                      <span style="font-weight: 600">{{
                        value.temperature ? value.temperature : '--'
                      }}</span>
                      {{ value.tUnit }}
                    </div>
                  </div>
                </div>
              </template></v-hover
            >
          </div>
        </div>
      </template>
      <empty v-else />
    </div>

    <v-fab
      color="primary"
      icon="mdi-undo-variant"
      style="bottom: 120px; right: 100px; position: fixed"
      size="x-large"
      @click="handleBackClick"
    ></v-fab>
  </div>
</template>

<style lang="scss" scoped>
:deep(.v-card--variant-outlined) {
  border: thin solid #ccc;
}
.cell-wrapper {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(122px, 1fr));
  grid-gap: 20px;
}
</style>
