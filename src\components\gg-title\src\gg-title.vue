<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-08 14:53:49
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-08-08 14:55:30
 * @FilePath: \ems_manage\src\components\gg-title\src\gg-title.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<template>
  <div class="title">
    <span></span>
    <slot></slot>
  </div>
</template>

<style lang="scss" scoped>
.title {
  font-size: 18px;
  font-weight: 500;
  display: flex;
  align-items: center;
  span {
    display: inline-block;
    width: 5px;
    height: 18px;
    border-radius: 3px;
    margin-right: 10px;
    background-color: rgb(var(--v-theme-primary))
  }
}
</style>
