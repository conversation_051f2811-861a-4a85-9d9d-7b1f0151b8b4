<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-08 18:15:54
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-08-27 15:06:49
 * @FilePath: \ems_manage\src\App.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, toRefs, onUnmounted, onMounted } from 'vue'
import { registerMaotuCustomSvg } from '@/plugins/registerMaotuCustomSvg'
import { useGlobalStore } from '@/store/global'
import { useConfigStore } from '@/store/module/config'
import { useUserStore } from '@/store/module/user'
import { initLocale } from '@/locale'
import { useRoute } from 'vue-router'

import DragPanel from '@/components/drag-panel'
import Log from '@/components/log.vue'

const route = useRoute()
const {
  snackbar,
  snackbarText,
  lang,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardInput,
  keyboardMode,
  showPanel,
  panelType
} = toRefs(useGlobalStore())
const { logData, logWs } = toRefs(useConfigStore())

initLocale()
registerMaotuCustomSvg()

/**
 * 键盘
 */
const manyDict = ref('dict/chowder.json')
const singleDict = ref('dict/baseDict.json')
keyboardDialog.value = false
const onFocus = (e) => {
  showKeyboard.value = e
}
const handleFocus = (e) => {
  e.mode = keyboardMode.value
  keyboardInput.value = e
}
const handleCancel = () => {
  showKeyboard.value = false
  keyboardDialog.value = false
}

/**
 * 面板
 */
const updateVisible = (data) => {
  showPanel.value = data
  if (panelType.value == 'log') {
    logData.value = []
    logWs.value?.close()
  }
}
onMounted(() => {
  window.addEventListener('beforeunload', () => {
    showPanel.value = false
    logData.value = []
    logWs.value?.close()
  })
})
onUnmounted(() => {
  showPanel.value = false
  logData.value = []
  logWs.value?.close()
})

/**
 * 语言
 */
useGlobalStore().getLanguageListFn()
/**
 * 时间
 */
if (useUserStore().isLogin) useConfigStore().startLocalTimeUpdate()
</script>

<template>
  <v-app class="bg-background overflow-auto">
    <router-view />
  </v-app>
  <v-snackbar v-model="snackbar">
    {{ snackbarText }}
  </v-snackbar>
  <v-dialog
    v-model="keyboardDialog"
    :width="mobile ? '95vw' : '800px'"
    :max-width="mobile ? '95vw' : '900px'"
    :persistent="true"
    :no-click-animation="true"
    class="hmi-keyboard-dialog"
  >
    <v-card class="hmi-keyboard-card">
      <v-card-text class="pa-4">
        <v-text-field
          v-model="keyboardInputValue"
          variant="outlined"
          label=""
          hide-details
          @update:focused="onFocus"
          keyboard="true"
          @focus="handleFocus"
          autofocus
          class="hmi-keyboard-input"
          :style="{
            fontSize: mobile ? '16px' : '18px',
            minHeight: mobile ? '52px' : '56px'
          }"
        ></v-text-field>
        <keyboard
          :transitionTime="'0.3s'"
          :maxQuantify="10"
          :showKeyboard="showKeyboard"
          float
          :manyDict="manyDict"
          :singleDict="singleDict"
          :blurHide="false"
          :inputEvent="keyboardInput"
          @confirm="confirmCall"
          @cancel="handleCancel"
          class="hmi-keyboard"
        ></keyboard>
      </v-card-text>
    </v-card>
  </v-dialog>
  <DragPanel v-model:visible="showPanel" @update:visible="updateVisible">
    <template v-if="panelType == 'log'">
      <Log style="height: 100%" />
    </template>
  </DragPanel>
</template>

<style lang="scss" scoped>
/* HMI键盘对话框优化 */
.hmi-keyboard-dialog {
  :deep(.v-overlay__content) {
    margin: 0 !important;
    max-height: 90vh !important;
  }
}

.hmi-keyboard-card {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;

  @media screen and (max-width: 1024px) {
    border-radius: 8px !important;
    margin: 8px !important;
  }
}

.hmi-keyboard-input {
  margin-bottom: 16px !important;

  :deep(.v-field) {
    border-radius: 8px !important;

    .v-field__input {
      font-weight: 500 !important;
      padding: 0 16px !important;
    }

    .v-field__outline {
      --v-field-border-width: 2px !important;
    }

    &.v-field--focused {
      .v-field__outline {
        --v-field-border-width: 3px !important;
        color: var(--v-theme-primary) !important;
      }
    }
  }
}

/* 键盘组件优化 */
.hmi-keyboard {
  :deep(.keyboard-container) {
    border-radius: 8px !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  }

  :deep(.keyboard-key) {
    min-height: 48px !important;
    min-width: 48px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    border-radius: 6px !important;
    margin: 2px !important;
    transition: all 0.2s ease !important;

    &:hover {
      transform: translateY(-1px) !important;
      box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15) !important;
    }

    &:active {
      transform: translateY(0) !important;
      box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1) !important;
    }

    @media screen and (max-width: 1024px) {
      min-height: 44px !important;
      min-width: 44px !important;
      font-size: 15px !important;
      margin: 1px !important;
    }
  }

  :deep(.keyboard-confirm-btn) {
    background: linear-gradient(135deg, #28c79c 0%, #20a085 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    min-height: 48px !important;
    border-radius: 6px !important;

    &:hover {
      background: linear-gradient(135deg, #20a085 0%, #1a8a6e 100%) !important;
    }
  }

  :deep(.keyboard-cancel-btn) {
    background: linear-gradient(135deg, #ff6b6b 0%, #e55555 100%) !important;
    color: white !important;
    font-weight: 600 !important;
    min-height: 48px !important;
    border-radius: 6px !important;

    &:hover {
      background: linear-gradient(135deg, #e55555 0%, #cc3f3f 100%) !important;
    }
  }
}

/* Element Plus 输入框优化 */
:deep(.el-input-number) {
  .el-input-number__decrease,
  .el-input-number__increase {
    display: none !important;
  }
  .is-disabled {
    display: none !important;
  }
  .el-input__wrapper {
    padding: 0 0 0 15px !important;
    background: rgb(66, 66, 66) !important;
    box-shadow: 0 0 0 1px rgb(66, 66, 66) !important;
    border-radius: 8px !important;
    min-height: 56px !important;
  }
  .el-input__wrapper.is-focus {
    box-shadow: 0 0 0 2px var(--v-theme-primary) !important;
  }
  .el-input__wrapper:hover {
    box-shadow: 0 0 0 1px var(--v-theme-primary) !important;
  }
  .el-input__inner {
    height: 56px !important;
    font-size: 16px !important;
    font-weight: 500 !important;
    text-align: left !important;
    color: #fff !important;
  }
}

/* 触摸优化 */
.hmi-touch-area {
  min-height: 44px !important;
  min-width: 44px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  cursor: pointer !important;
  user-select: none !important;
  -webkit-tap-highlight-color: transparent !important;

  &:active {
    transform: scale(0.98) !important;
  }
}

/* 拖拽面板优化 */
:deep(.drag-panel) {
  border-radius: 12px !important;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.2) !important;

  .drag-panel-header {
    min-height: 48px !important;
    padding: 12px 16px !important;
    background: var(--v-theme-primary) !important;
    color: white !important;
    font-weight: 600 !important;
    border-radius: 12px 12px 0 0 !important;

    .drag-panel-title {
      font-size: 16px !important;
    }

    .drag-panel-close {
      min-width: 32px !important;
      min-height: 32px !important;
      border-radius: 6px !important;

      .v-icon {
        font-size: 20px !important;
      }
    }
  }

  .drag-panel-content {
    padding: 16px !important;

    @media screen and (max-width: 1024px) {
      padding: 12px !important;
    }
  }
}
</style>
