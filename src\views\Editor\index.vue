<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-16 11:44:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-23 12:07:17
 * @FilePath: \ems_manage\src\views\test.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import {
  onMounted,
  ref,
  nextTick,
  toRefs,
  computed,
  getCurrentInstance
} from 'vue'
import { MtEdit, MtPreview } from 'maotu'
import { useConfigStore } from '@/store/module/config'
import { useDeviceStore } from '@/store/module/device'
import { useGlobalStore } from '@/store/global'
import { useI18n } from 'vue-i18n'
import { setLeftWidth } from '@/utils'
import { useRouter, useRoute } from 'vue-router'

import { ElTable, ElTableColumn } from 'element-plus'

const { proxy } = getCurrentInstance()
const { t } = useI18n()
const { editorConfig, isModule, moduleData, pointData, iconsData } = toRefs(
  useConfigStore()
)
const { treeData } = toRefs(useDeviceStore())
const { snackbar, snackbarText } = toRefs(useGlobalStore())

const MtEditRef = ref()
const onPreviewClick = (exportJson) => {
  console.log(exportJson, '这是要传给预览组件的数据')
  dialog.value = true
  nextTick(() => {
    MtPreviewRef.value?.setImportJson(exportJson)
  })
}
const getData = () => {
  useConfigStore()
    .getWebConfigFn({ key: 'icon' })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
  useConfigStore()
    .topologyInformationFn()
    .then((res) => {
      console.log(editorConfig.value)
      MtEditRef.value?.setImportJson(editorConfig.value)
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
const onSaveClick = (exportJson) => {
  console.log(exportJson, '这是要保存的数据')
  try {
    localStorage.setItem('maotu_data', JSON.stringify(exportJson))
  } catch (error) {
    console.error(error)
  }
  useConfigStore()
    .topologyInfoSetFn(JSON.stringify(exportJson))
    .then((res) => {
      snackbar.value = true
      snackbarText.value = t('保存成功')
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
// 加载已有数据
onMounted(() => {
  getData()
})

const MtPreviewRef = ref()
const dialog = ref(false)

/**
 * 绑定属性
 */
const form = ref({
  icon_id: undefined,
  points: [
    {
      point_id: [],
      formula: undefined,
      unit: undefined,
      dataType: '1'
    }
  ]
})
const dataOptions = ref([
  {
    id: '0',
    title: '状态'
  },
  {
    id: '1',
    title: '模拟量'
  }
])
const bindDialog = ref(false)
const handleBindClick = (item) => {
  let value = iconsData?.value?.find((icon) => icon.icon_id == item.id)
  form.value = {
    icon_id: item.id,
    points: value
      ? value.points
      : [
          {
            point_id: [],
            formula: undefined,
            unit: undefined,
            dataType: '1'
          }
        ]
  }
  bindDialog.value = true
  nextTick(() => {
    setLeftWidth('dia-left')
  })
}
const loading = ref(false)
const submit = async () => {
  const { valid } = await proxy.$refs.addForm.validate()
  if (!valid) return
  loading.value = true
  let index = iconsData.value.findIndex(
    (icon) => icon.icon_id == form.value.icon_id
  )
  if (index !== -1) {
    iconsData.value.splice(index, 1, {
      icon_id: form.value.icon_id,
      points: form.value.points
    })
  } else {
    iconsData.value.push({
      icon_id: form.value.icon_id,
      points: form.value.points
    })
  }
  await setIconFn()
  snackbar.value = true
  snackbarText.value = '添加成功'
  loading.value = false
  bindDialog.value = false
}
const setIconFn = async () => {
  try {
    const res = await useConfigStore().setWebConfigFn(
      JSON.stringify({
        key: 'icon',
        config: JSON.stringify(iconsData.value)
      })
    )
    useConfigStore()
      .getWebConfigFn({ key: 'icon' })
      .then((res) => {})
      .catch((error) => {
        snackbar.value = true
        snackbarText.value = error
      })
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
    bindDialog.value = false
  }
}
const handleCancelClick = () => {
  loading.value = false
  bindDialog.value = false
}
const handlePointDeleteClick = (index) => {
  if (form.value.points.length == 1) {
    snackbar.value = true
    snackbarText.value = t('至少要有一条哦')
    return
  }
  form.value.points.splice(index, 1)
}
const handlePointAddClick = () => {
  if (form.value.points.length == 12) {
    snackbar.value = true
    snackbarText.value = t('最多只能添加12条哦')
    return
  }
  form.value.points.push({
    point_id: [],
    formula: undefined,
    unit: undefined,
    dataType: '1'
  })
}
const handleDeleteIconClick = async (item, deviceId) => {
  let index = iconsData.value.findIndex((icon) => icon.icon_id == item.id)
  if (index == -1) {
    snackbar.value = true
    snackbarText.value = '该icon并没有绑定设备'
    return
  }
  iconsData.value.splice(index, 1)
  await setIconFn()
  snackbar.value = true
  snackbarText.value = t('删除成功')
}
/**
 * 选择属性
 */
const selectDialog = ref(false)
const selectForm = ref({})
// 选择设备
const handleDeviceChange = (e) => {
  if (!e) return
  selectForm.value.moduleId = undefined
  selectForm.value.pointId = undefined
  useConfigStore().getTreePointDataFn({
    deviceId: e,
    dataType: selectForm.value.dataType
  })
}
// 选择模块
const pointDataOptions = computed(() => {
  if (isModule.value) {
    return pointData.value.find((item) => item.id == selectForm.value.moduleId)
      ?.children
  } else {
    return pointData.value
  }
})
const handleModuleChange = (e) => {
  if (!e) return
  selectForm.value.pointId = undefined
}
const handleSelectCancelClick = () => {
  selectDialog.value = false
}
const handleSelectClick = async (index, item) => {
  if (item.deviceId) {
    await useConfigStore().getTreePointDataFn({
      deviceId: item.deviceId,
      dataType: item.dataType
    })
    selectForm.value = {
      index,
      deviceId: item.deviceId,
      moduleId: item.moduleId,
      pointId: item.point_id,
      dataType: item.dataType,
      bit: null
    }
    selectDialog.value = true
  } else {
    selectForm.value = {
      index,
      deviceId: item.deviceId,
      moduleId: item.moduleId,
      pointId: item.point_id,
      dataType: item.dataType,
      bit: null
    }
    selectDialog.value = true
  }
}
const selectSubmit = () => {
  form.value.points[selectForm.value.index].deviceId = selectForm.value.deviceId
  form.value.points[selectForm.value.index].moduleId = selectForm.value.moduleId
  form.value.points[selectForm.value.index].point_id = selectForm.value.pointId
  form.value.points[selectForm.value.index].bit = selectForm.value.bit
  selectDialog.value = false
}

const headers = ref([
  {
    title: 'icon_id',
    align: 'center',
    key: 'icon_id',
    fixed: true
  },
  {
    title: '设备名称',
    align: 'center',
    key: 'device_name',
    fixed: true
  },
  {
    title: 'device_id',
    align: 'center',
    key: 'device_id'
  },
  { title: '操作', key: 'actions', sortable: false, align: 'center' }
])
const listDialog = ref(false)
const handleListClick = () => {
  useConfigStore()
    .getWebConfigFn({ key: 'icon' })
    .then((res) => {})
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
  listDialog.value = true
}

/**
 * 返回
 */
const router = useRouter()
const route = useRoute()
const onReturnClick = () => {
  router.push('/dashboard')
}
</script>

<template>
  <div id="box" class="w-100 h-100">
    <mt-edit
      ref="MtEditRef"
      @on-preview-click="onPreviewClick"
      @on-return-click="onReturnClick"
      @on-save-click="onSaveClick"
    >
      <template #deviceBind="{ item }">
        <div>
          <div class="mb-2">{{ item.id }}</div>
          <v-btn
            variant="outlined"
            class="w-100 mb-2"
            @click="handleBindClick(item)"
            >点击配置</v-btn
          >
          <v-btn
            variant="outlined"
            class="w-100 mb-2"
            @click="handleDeleteIconClick(item)"
            >删除</v-btn
          >
          <v-btn variant="outlined" class="w-100" @click="handleListClick"
            >查看配置列表</v-btn
          >
        </div>
      </template>
    </mt-edit>

    <v-dialog v-model="dialog" width="auto">
      <mt-preview ref="MtPreviewRef"></mt-preview>
    </v-dialog>

    <v-dialog v-model="bindDialog" width="auto">
      <v-card width="740" class="pa-4 rounded-lg">
        <v-card-title class="text-center mb-6">绑定设备属性</v-card-title>
        <v-form fast-fail @submit.prevent="submit" ref="addForm">
          <!-- <div class="flex">
            <div
              class="text-body-1 pb-34px pt-3 pr-4 flex justify-end align-center dia-left"
            >
              {{ $t('选择设备') }}
            </div>
            <v-col cols="10" class="pl-0 pr-2">
              <v-select
                v-model="form.device_id"
                item-value="id"
                clearable
                :items="treeData"
                variant="outlined"
                :placeholder="$t('选择设备')"
                class="w-100 mr-4 mt-2"
              ></v-select>
            </v-col>
          </div> -->
          <!-- <div class="flex">
            <div
              class="text-body-1 pb-34px pt-3 pr-4 flex justify-end align-center dia-left"
            >
              {{ $t('属性') }}
            </div> -->
          <v-col cols="12" class="pl-0 pr-2">
            <el-table :data="form.points" border style="width: 100%">
              <el-table-column prop="属性" label="属性" align="center">
                <template #default="scoped">
                  <!-- <el-cascader
                      v-model="scoped.row.point_id"
                      :props="props"
                      filterable
                      class="select-device"
                    /> -->
                  <v-text-field
                    v-model="scoped.row.point_id"
                    :rules="[(v) => !!v || $t('属性ID必填')]"
                    variant="outlined"
                    :placeholder="$t('属性ID')"
                    clearable
                    hide-details
                  ></v-text-field>
                  <v-btn
                    class="w-100 my-2"
                    @click="handleSelectClick(scoped.$index, scoped.row)"
                    >选择属性</v-btn
                  >
                </template>
              </el-table-column>
              <el-table-column
                prop="calcType"
                :label="$t('计算公式')"
                align="center"
              >
                <template #default="scoped">
                  <v-text-field
                    v-model="scoped.row.formula"
                    :placeholder="
                      $t('x+y+z,x为第一个属性值,y为第二个属性值...')
                    "
                    variant="outlined"
                    clearable
                    hide-details
                  ></v-text-field>
                </template>
              </el-table-column>
              <el-table-column
                prop="calcType"
                :label="$t('单位')"
                align="center"
              >
                <template #default="scoped">
                  <v-text-field
                    v-model="scoped.row.unit"
                    :placeholder="$t('单位')"
                    variant="outlined"
                    clearable
                    hide-details
                  ></v-text-field>
                </template>
              </el-table-column>
              <el-table-column prop="dataType" label="数据类型" align="center">
                <template #default="scoped">
                  <v-select
                    v-model="scoped.row.dataType"
                    item-value="id"
                    :items="dataOptions"
                    variant="outlined"
                    class="w-100"
                    hide-details
                  ></v-select>
                </template>
              </el-table-column>
              <el-table-column label="操作" width="100" align="center">
                <template #default="scoped">
                  <v-tooltip :text="$t('添加')" location="top">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        icon="mdi-plus-circle"
                        variant="plain"
                        v-bind="props"
                        style="width: 24px; height: 24px"
                        @click="handlePointAddClick(scoped.$index)"
                      ></v-btn>
                    </template>
                  </v-tooltip>
                  <v-tooltip :text="$t('删除')" location="top">
                    <template v-slot:activator="{ props }">
                      <v-btn
                        icon="mdi-close-circle"
                        variant="plain"
                        v-bind="props"
                        style="width: 24px; height: 24px"
                        @click="handlePointDeleteClick(scoped.$index)"
                      ></v-btn>
                    </template>
                  </v-tooltip>
                </template>
              </el-table-column>
            </el-table>
          </v-col>
          <!-- </div> -->
          <div class="d-flex justify-center">
            <v-btn
              class="mt-2 mr-4 px-8"
              height="50"
              @click="handleCancelClick"
              >{{ $t('取消') }}</v-btn
            >
            <v-btn
              class="mt-2 px-8"
              type="submit"
              height="50"
              :loading="loading"
              color="primary"
              >{{ $t('确定') }}</v-btn
            >
          </div>
        </v-form>
      </v-card>
    </v-dialog>

    <v-dialog v-model="selectDialog" width="auto">
      <v-card width="540" class="pa-4 rounded-lg">
        <v-card-title class="text-center mb-4">选择属性</v-card-title>
        <v-form fast-fail @submit.prevent="selectSubmit">
          <v-select
            v-model="selectForm.deviceId"
            item-value="id"
            clearable
            :label="$t('选择设备')"
            :items="treeData"
            variant="outlined"
            class="w-100 mr-4 mt-2"
            @update:modelValue="handleDeviceChange"
          ></v-select>
          <v-select
            v-model="selectForm.moduleId"
            item-value="id"
            clearable
            :label="$t('选择模块')"
            :items="moduleData"
            variant="outlined"
            class="w-100 mr-4 mt-2"
            @update:modelValue="handleModuleChange"
            v-if="isModule"
          ></v-select>
          <v-select
            v-model="selectForm.pointId"
            item-value="id"
            clearable
            multiple
            :label="$t('选择属性')"
            :items="pointDataOptions"
            variant="outlined"
            class="w-100 mr-4 mt-2"
          ></v-select>
          <v-text-field
            v-model="selectForm.bit"
            variant="outlined"
            :placeholder="$t('Bit位')"
            :label="$t('Bit位')"
            clearable
            hide-details
            v-if="selectForm.dataType == '0'"
          ></v-text-field>
          <div class="d-flex justify-center">
            <v-btn
              class="mt-2 mr-4 px-8"
              height="50"
              @click="handleSelectCancelClick"
              >{{ $t('取消') }}</v-btn
            >
            <v-btn
              class="mt-2 px-8"
              type="submit"
              height="50"
              color="primary"
              >{{ $t('确定') }}</v-btn
            >
          </div>
        </v-form>
      </v-card>
    </v-dialog>
    <v-dialog v-model="listDialog" width="auto">
      <v-card width="740" class="pa-4 rounded-lg">
        <v-card-title class="text-center mb-6">配置列表</v-card-title>
        <v-data-table
          :headers="headers"
          :items="iconsData"
          hide-default-footer
          fixed-header
          class="px-6"
          items-per-page="-1"
        >
          <template v-slot:item.actions="{ item }">
            <v-btn
              icon="mdi-delete"
              variant="plain"
              @click="handleDeleteIconClick(item)"
            ></v-btn>
          </template>
        </v-data-table>
        <div class="d-flex justify-center">
          <v-btn class="mt-2 mr-4 px-8" height="50" @click="listDialog = false"
            >关闭</v-btn
          >
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
.dia-left {
  flex: 0 0 auto; /* 不可拉伸，宽度不变 */
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容显示出来 */
  text-align: right;
}
:deep(.el-table) {
  --el-table-border-color: rgba(118, 118, 118, 0.6);
  --el-table-border: 1px solid rgba(118, 118, 118, 0.6);
  --el-table-text-color: #000;
  --el-table-header-text-color: #000;
  border-radius: 2px;
  .cell {
    font-weight: 400;
  }
  .el-table__empty-text {
    color: #000;
  }
}
:deep(.v-table) {
  border-radius: none;
  table {
    border-collapse: collapse;
  }
  td,
  th {
    border: thin solid rgba(var(--v-border-color), var(--v-border-opacity));
  }
}
</style>
