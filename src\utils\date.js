/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-07-08 15:34:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-15 12:08:47
 * @FilePath: \ems_manage\src\utils\date.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import dayjs from 'dayjs'
import timezone from 'dayjs/plugin/timezone'
import utc from 'dayjs/plugin/utc'
import isBetwenn from 'dayjs/plugin/isBetween'
import isSameOrAfter from 'dayjs/plugin/isSameOrAfter'
import isSameOrBefore from 'dayjs/plugin/isSameOrBefore'

// 加载插件
dayjs.extend(utc);
dayjs.extend(timezone);
dayjs.extend(isBetwenn);
dayjs.extend(isSameOrAfter)
dayjs.extend(isSameOrBefore)

export function formatDate(date, formatString = 'YYYY-MM-DD HH:mm:ss') {
  return dayjs(date).format(formatString);
}

export function generateTimePoints(interval, formatString = 'HH:mm', granularity) {
  const start = dayjs.utc(interval.start) // 转为本地时区
  const end = dayjs.utc(interval.end)

  const hoursArray = [];
  let current = start.clone();

  while (current.isSameOrBefore(end)) {
    hoursArray.push(current.format(formatString));
    current = current.add(granularity.value, granularity.unit);
  }

  return hoursArray;
}

export default dayjs