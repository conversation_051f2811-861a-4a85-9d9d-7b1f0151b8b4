// vite.config.js
import { defineConfig } from "file:///C:/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/ems_manage/node_modules/vite/dist/node/index.js";
import vue from "file:///C:/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/ems_manage/node_modules/@vitejs/plugin-vue/dist/index.mjs";
import path from "path";
import UnoCSS from "file:///C:/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/ems_manage/node_modules/unocss/dist/vite.mjs";
import legacy from "file:///C:/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/ems_manage/node_modules/@vitejs/plugin-legacy/dist/index.mjs";
import { visualizer } from "file:///C:/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/ems_manage/node_modules/rollup-plugin-visualizer/dist/plugin/index.js";
import { Plugin as importToCDN } from "file:///C:/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/ems_manage/node_modules/vite-plugin-cdn-import/dist/index.js";
import viteCompression from "file:///C:/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/ems_manage/node_modules/vite-plugin-compression/dist/index.mjs";
import ElementPlus from "file:///C:/Users/<USER>/Desktop/%E6%96%B0%E5%BB%BA%E6%96%87%E4%BB%B6%E5%A4%B9/ems_manage/node_modules/unplugin-element-plus/dist/vite.mjs";
var __vite_injected_original_dirname = "C:\\Users\\\<USER>\u6653\\Desktop\\\u65B0\u5EFA\u6587\u4EF6\u5939\\ems_manage";
var vite_config_default = defineConfig({
  base: "./",
  plugins: [
    vue(),
    UnoCSS(),
    // 打包分析
    visualizer({ open: true }),
    // 低版本浏览器兼容
    legacy({
      targets: ["ie >= 11", "chrome < 60"]
    }),
    // CDN引入
    // importToCDN({
    //   //（prodUrl解释： name: 对应下面modules的name，version: 自动读取本地package.json中dependencies依赖中对应包的版本号，path: 对应下面modules的path，当然也可写完整路径，会替换prodUrl）
    //   prodUrl: "https://cdn.bootcdn.net/ajax/libs/{name}/{version}/{path}",
    //   modules: [
    //     "dayjs",
    //     {
    //       name: 'axios',
    //       var: 'axios',
    //       path: 'https://cdn.jsdelivr.net/npm/axios@1.7.4/dist/axios.min.js'
    //     },
    //     {
    //       name: "echarts",
    //       var: "echarts",
    //       path: "echarts.min.js"
    //     }
    //   ],
    // }),
    // 开启gzip
    viteCompression({
      threshold: 0,
      deleteOriginFile: false,
      filter: () => true,
      ext: ".gz"
    }),
    ElementPlus()
  ],
  resolve: {
    // https://cn.vitejs.dev/config/#resolve-alias
    // 配置别名
    alias: {
      // 设置路径
      "~": path.resolve(__vite_injected_original_dirname, "./"),
      // 设置别名
      "@": path.resolve(__vite_injected_original_dirname, "./src")
    },
    // https://cn.vitejs.dev/config/#resolve-extensions
    extensions: [".mjs", ".js", ".ts", ".jsx", ".tsx", ".json", ".vue"]
  },
  // 代理配置
  server: {
    host: "0.0.0.0",
    proxy: {
      // https://cn.vitejs.dev/config/#server-proxy
      "/api": {
        // target: 'http://localhost:8081',
        target: "http://pd308.elecod-cloud.com/api/",
        // target: 'http://ems.elecod-cloud.com/api/',
        changeOrigin: true,
        rewrite: (p) => p.replace(/^\/api/, "")
      }
    },
    hmr: {
      overlay: false
    },
    strictPort: true,
    port: 8081
  },
  build: {
    // https://cn.vitejs.dev/guide/build.html#browser-compatibility
    sourcemap: false,
    // 消除打包大小超过500kb警告
    chunkSizeWarningLimit: 4e3
  },
  define: {
    "process.env.NODE_ENV": '"production"'
  },
  optimizeDeps: {
    include: [
      "dayjs",
      "axios",
      "pinia",
      "vue-i18n",
      "vuetify"
    ]
  }
});
export {
  vite_config_default as default
};
//# sourceMappingURL=data:application/json;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************
