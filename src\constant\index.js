export const allDeviceTypes = [
  {
    class: 'FireAlarmDevice',
    typeId: 850
  },
  {
    class: 'MeterDevice',
    typeId: 400
  },
  {
    class: 'AirConditionerDevice',
    typeId: 900
  },
  {
    class: 'BMSDevice',
    typeId: 200
  },
  {
    class: 'EMSDIDODevice',
    typeId: 950
  },
  {
    class: 'EMSDevice',
    typeId: 0
  },
  {
    class: 'ACHMIDevice',
    typeId: 100
  },
  {
    class: 'BAUDevice',
    typeId: 200
  },
  {
    class: 'DCHMIDevice',
    typeId: 100
  },
  {
    class: 'LoadMeterDevice',
    typeId: 100
  },
  {
    class: 'GridMeterDevice',
    typeId: 400
  },
]

export const hardwareModelOptions = [
  {
    value: 0,
    label: 'FCU2601'
  },
  {
    value: 1,
    label: 'U3212-2'
  },
  {
    value: 2,
    label: 'U3212-3'
  },
]

export const baudRateOptions = [
  {
    value: 1200,
    title: '1200bps'
  },
  {
    value: 2400,
    title: '2400bps'
  },
  {
    value: 4800,
    title: '4800bps'
  },
  {
    value: 9600,
    title: '9600bps'
  },
  {
    value: 19200,
    title: '19200bps'
  },
  {
    value: 38400,
    title: '38400bps'
  },
  {
    value: 57600,
    title: '57600bps'
  },
  {
    value: 115200,
    title: '115200bps'
  },
]

export const parityOptions = [
  {
    value: 'N',
    title: '无校验'
  },
  {
    value: 'O',
    title: '奇校验'
  },
  {
    value: 'E',
    title: '偶校验'
  },
]

export const dataBitOptions = [
  {
    value: 7,
    title: '7位数据位'
  },
  {
    value: 8,
    title: '8位数据位'
  },
]

export const stopBitOptions = [
  {
    value: 1,
    title: '1位停止位'
  },
  {
    value: 1.5,
    title: '1.5位停止位'
  },
  {
    value: 2,
    title: '2位停止位'
  },
]