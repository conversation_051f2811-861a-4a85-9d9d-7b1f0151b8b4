<script setup>
import { onMounted, ref, computed, toRefs, onUnmounted, nextTick } from 'vue'
import { MtPreview } from 'maotu'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'
import { useRoute, useRouter } from 'vue-router'
import { isEmpty, isEqual } from 'lodash-es'
import { useI18n } from 'vue-i18n'
import { evaluate } from 'mathjs'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const MtPreviewRef = ref()
const { editorConfig, iconsData, cycleTime } = toRefs(useConfigStore())
const { snackbar, snackbarText } = toRefs(useGlobalStore())
const isFullscreen = computed(() => {
  return route.name == 'WebPreview'
})

const unknown = ['x', 'y', 'z', 't', 'u', 'v', 'w', 'p', 'q', 'r', 's']
const getData = () => {
  if (!iconsData.value.length) return
  let data = []
  let newIconsData = JSON.parse(JSON.stringify(iconsData.value))
  iconsData.value.forEach((icon) => {
    data.push(
      icon.points.map((point) => {
        return {
          deviceId: point.deviceId,
          pointIds: point.point_id
        }
      })
    )
  })
  useConfigStore()
    .getRealtimePointDataFn(JSON.stringify(data.flat()))
    .then((res) => {
      let valueIconsData = res.map((item) => {
        return {
          deviceId: item.deviceId,
          point_id: item.values?.map((value) => {
            return Object.keys(value)[0]
          }),
          values: item.values
        }
      })
      newIconsData.forEach((icon) => {
        editorConfig.value?.json.forEach((item) => {
          if (icon.icon_id == item.id) {
            // 对应图标点位
            icon?.points.forEach((point, pointIndex) => {
              // 数据一一对应
              let valueIconSingle = valueIconsData.find((valueIcon) =>
                isEqual(valueIcon.point_id, point.point_id)
              )
              if (!isEmpty(valueIconSingle)) {
                point.value = valueIconSingle.values
              }
              // 对返回的数据value进行组合
              let pointId = []
              if (point.value.length) {
                point.value.forEach((proPoint) => {
                  pointId.push(Number(proPoint[Object.keys(proPoint)[0]]))
                })
              }
              // 文字
              let textChild = []
              if (!isEmpty(item.children)) {
                // 组合图标
                item?.children?.forEach((child, childIndex) => {
                  if (/^text-vue/g.test(child.id)) {
                    textChild.push(child)
                  }
                })
                // 把获取的数据赋值到文字上，这是有文字的时候
                if (textChild.length)
                  textChild.forEach((text, textIndex) => {
                    if (pointIndex == textIndex) {
                      if (isEmpty(point.formula)) {
                        text.props.text.val = pointId.length
                          ? `${pointId[0]} ${point.unit}`
                          : ''
                      } else {
                        let obj = {}
                        pointId.forEach((proPointId, proPointIdIndex) => {
                          obj[unknown[proPointIdIndex]] = proPointId
                        })
                        text.props.text.val = pointId.length
                          ? `${evaluate(point.formula, obj).toFixed(2)} ${
                              point.unit
                            }`
                          : ''
                      }
                    }
                  })
              } else {
                // 单个
                if (!isEmpty(point.formula)) {
                  let obj = {}
                  pointId.forEach((proPointId, proPointIdIndex) => {
                    obj[unknown[proPointIdIndex]] = proPointId
                  })
                  item.props.text = pointId.length
                    ? `${evaluate(point.formula, obj).toFixed(2)} ${point.unit}`
                    : ''
                } else {
                  item.props.text = pointId.length
                    ? `${pointId[0]} ${point.unit}`
                    : ''
                }
              }
              // 开关状态
              if (/^DuanluqiClose/g.test(item.id)) {
                if (point.dataType == '0') {
                  let status = (pointId[0] >> point.bit) & 1
                  item.props.backFill = status == 0 ? '#EDF1F5' : '#ff0000' // 数据类型为状态
                }
              }
            })
          }
        })
      })
      // 重新赋值拓扑图
      nextTick(() => {
        MtPreviewRef.value?.setImportJson(editorConfig.value)
      })
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
const time = ref(null)
const isUnmounted = ref(false) // 标志位
onMounted(async () => {
  await getTopologyJsonFn()
  await getIconsJsonFn()
  await getData()
  time.value = setInterval(() => {
    if (isUnmounted.value) {
      // 如果组件已卸载，则不再执行逻辑
      clearInterval(time.value)
      return
    }
    getData()
  }, cycleTime.value)
})
const getTopologyJsonFn = async () => {
  try {
    const res = await useConfigStore().topologyInformationFn()
    if (!isEmpty(editorConfig.value)) {
      nextTick(() => {
        MtPreviewRef.value?.setImportJson(editorConfig.value)
      })
    }
  } catch (error) {
    console.log(error)
    snackbar.value = true
    snackbarText.value = error
  }
}
const getIconsJsonFn = async () => {
  try {
    const res = await useConfigStore().getWebConfigFn({ key: 'icon' })
    getData()
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
  }
}
onUnmounted(() => {
  isUnmounted.value = true
  if (time.value) clearInterval(time.value)
})

/**
 * 全屏
 */
const handleScreenClick = (type) => {
  if (type == 'plus') {
    router.push('/webPreview')
  } else {
    router.push('/dashboard')
  }
}

/**
 * 放大缩小
 */
const handleZoomClick = (type) => {
  if (type == 'plus') {
    editorConfig.value.canvasCfg.scale += 0.05
  } else {
    editorConfig.value.canvasCfg.scale -= 0.05
  }
  MtPreviewRef.value?.setImportJson(editorConfig.value)
}

/**
 * 拖动
 */
const x = ref(0)
const y = ref(0)
const startx = ref('')
const starty = ref('')
const endx = ref(0)
const endy = ref(0)
//用mousedown/mousemove/mouseup事件实现鼠标拖拽图片移动效果
const mousedown = (e) => {
  // 绑定mousemove
  startx.value = e.pageX
  starty.value = e.pageY
  document.addEventListener('mousemove', mousemove)
  document.getElementById('pic').addEventListener('mouseup', mouseup)
}
const mousemove = (e) => {
  x.value = e.pageX - startx.value + endx.value
  y.value = e.pageY - starty.value + endy.value
}
const mouseup = () => {
  // 解除绑定mousemove
  document.removeEventListener('mousemove', mousemove, false)
  endx.value = x.value
  endy.value = y.value
}
const locationX = computed(() => x.value + 'px')
const locationY = computed(() => y.value + 'px')
</script>

<template>
  <template v-if="!isEmpty(editorConfig)">
    <div style="position: absolute; top: 20px; right: 20px; z-index: 1000">
      <v-btn
        icon="mdi-fullscreen"
        variant="text"
        v-if="!isFullscreen"
        @click="handleScreenClick('plus')"
      ></v-btn>
      <v-btn
        icon="mdi-fullscreen-exit"
        variant="text"
        v-else
        @click="handleScreenClick('min')"
      ></v-btn>
    </div>
    <div
      class="flex flex-column"
      style="position: absolute; left: 20px; top: 40%; z-index: 1000"
    >
      <v-tooltip :text="$t('放大')">
        <template v-slot:activator="{ props }">
          <v-btn
            icon="mdi-plus-circle-outline"
            v-bind="props"
            variant="text"
            @click="handleZoomClick('plus')"
          ></v-btn> </template
      ></v-tooltip>
      <v-tooltip :text="$t('缩小')">
        <template v-slot:activator="{ props }">
          <v-btn
            icon="mdi-minus-circle-outline"
            v-bind="props"
            variant="text"
            @click="handleZoomClick('min')"
          ></v-btn></template
      ></v-tooltip>
    </div>
    <mt-preview
      ref="MtPreviewRef"
      :canZoom="true"
      :canDrag="false"
      :showPopover="false"
      style="height: 100% !important"
      draggable="false"
      @mousedown="mousedown($event)"
      id="pic"
      class="pl-10 pt-10"
    ></mt-preview>
  </template>
  <template v-else>
    <div class="flex flex-wrap justify-center items-center h-100">
      <empty />
    </div>
  </template>
</template>

<style lang="scss" scoped>
:deep(.el-scrollbar__view) {
  height: 100% !important;
}
:deep(.canvasArea) {
  position: relative;
  left: v-bind(locationX);
  top: v-bind(locationY);
}
</style>
