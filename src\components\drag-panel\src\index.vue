<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-07-24 10:39:44
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-19 16:36:50
 * @FilePath: \ems_manage\src\components\drag-panel\src\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, onMounted, onBeforeUnmount, watch, nextTick } from 'vue'
import interact from 'interactjs'

const visible = defineModel('visible', false)

const emit = defineEmits(['update:visible'])

const panel = ref(null)
let panelX = ref(100)
let panelY = ref(100)

// 初始化拖拽
function initDrag() {
  if (!document.querySelector('.floating-panel')) return

  interact('.floating-panel')
    .resizable({
      // resize from all edges and corners
      edges: { left: true, right: true, bottom: true, top: true },

      listeners: {
        move(event) {
          var target = event.target
          var x = parseFloat(target.getAttribute('data-x')) || 0
          var y = parseFloat(target.getAttribute('data-y')) || 0

          // update the element's style
          target.style.width = event.rect.width + 'px'
          target.style.height = event.rect.height + 'px'

          // translate when resizing from top or left edges
          x += event.deltaRect.left
          y += event.deltaRect.top

          target.style.transform = 'translate(' + x + 'px,' + y + 'px)'

          target.setAttribute('data-x', x)
          target.setAttribute('data-y', y)
          // target.textContent =
          //   Math.round(event.rect.width) +
          //   '\u00D7' +
          //   Math.round(event.rect.height)
        }
      },
      modifiers: [
        // keep the edges inside the parent
        interact.modifiers.restrictEdges({
          outer: 'parent'
        }),

        // minimum size
        interact.modifiers.restrictSize({
          min: { width: 100, height: 50 }
        })
      ],

      inertia: true
    })
    .draggable({
      listeners: { move: window.dragMoveListener },
      inertia: true,
      modifiers: [
        interact.modifiers.restrictRect({
          restriction: 'parent',
          endOnly: true
        })
      ],
      ignoreFrom: '.floating-panel-cont *'
    })
}

function dragMoveListener(event) {
  var target = event.target
  // keep the dragged position in the data-x/data-y attributes
  var x = (parseFloat(target.getAttribute('data-x')) || 0) + event.dx
  var y = (parseFloat(target.getAttribute('data-y')) || 0) + event.dy

  // translate the element
  target.style.transform = 'translate(' + x + 'px, ' + y + 'px)'

  // update the posiion attributes
  target.setAttribute('data-x', x)
  target.setAttribute('data-y', y)
}

// this function is used later in the resizing and gesture demos
window.dragMoveListener = dragMoveListener

// 在组件挂载后初始化拖拽
watch(visible, (val) => {
  if (val) {
    nextTick(() => {
      initDrag()
    })
  } else {
    if (panel.value) {
      interact(panel.value).unset()
    }
  }
})

// 清理事件监听器
onBeforeUnmount(() => {
  if (panel.value) {
    interact(panel.value).unset()
  }
})

// 关闭面板
const close = () => {
  emit('update:visible', false)
}
</script>

<template>
  <v-card
    v-if="visible"
    ref="panel"
    :style="{ left: panelX + 'px', top: panelY + 'px' }"
    class="pa-4 rounded-lg floating-panel"
    elevation="4"
  >
    <div class="flex justify-between align-center">
      <div class="text-h6">DevTools Panel</div>
      <v-icon color="error" icon="mdi-window-close" @click="close"></v-icon>
    </div>
    <div
      class="floating-panel-cont"
      style="width: 100%; height: calc(100% - 30px)"
    >
      <slot></slot>
    </div>
  </v-card>
</template>

<style scoped>
.floating-panel {
  position: fixed;
  background: white;
  z-index: 4000001;
  cursor: move;
  height: 300px;
  width: 700px;
  min-width: 800px;
  /* max-width: 1500px;
  max-height: 1000px; */
}

.close-btn {
  position: absolute;
  top: 5px;
  right: 5px;
  width: 20px;
  height: 20px;
  background: red;
  color: white;
  border: none;
  border-radius: 50%;
  cursor: pointer;
}
</style>
