<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useParamStore } from '@/store/module/param'
import { useGlobalStore } from '@/store/global'
import { toRefs } from 'vue'
import dayjs, { generateTimePoints, formatDate } from '@/utils/date'
import { queryStrategyData } from '@/api/param'
import { isEmpty } from 'lodash-es'
import { useStatisticsStore } from '@/store/module/statistics'

import LineEchart from './lineEchart.vue'

const props = defineProps({
  date: {
    type: String,
    default: () => dayjs().format('YYYY-MM-DD')
  }
})

const countryOptions = defineModel('countryOptions', { default: [] })
const ACDeviceRequestParams = defineModel('ACDeviceRequestParams', {
  default: []
})

const emit = defineEmits(['close', 'apply'])

const { t } = useI18n()
const { snackbar, snackbarText } = toRefs(useGlobalStore())

const historyData = ref([])
const loading = ref(false)
const dateMenu = ref(false)
const selectedDate = ref(props.date)
const dateValue = ref([])

const algorithmOptions = ref([
  { title: t('精确生成'), value: 'merge' },
  { title: t('基础算法'), value: 'base' },
  { title: t('常规生成'), value: 'occupied' }
])

// 获取历史数据
const getHistoryData = async () => {
  loading.value = true
  try {
    const res = await queryStrategyData(
      JSON.stringify({ date: selectedDate.value })
    )
    if (res.data.ok) {
      historyData.value = res.data.items || []
      Promise.all(
        historyData.value.map((item) => getCountryData(item.params))
      ).then((result) => {
        result.forEach((item, resIndex) => {
          // 调度曲线
          let scheduleData = {
            times: [],
            datas: [],
            currency: '',
            name: '',
            date: ''
          }
          scheduleData.times = item.lineData.times
          let datas = []
          scheduleData.times.forEach((time, index) => {
            datas.push(0)
            historyData.value[resIndex].scheduleUnits.forEach(
              (scheduleItem) => {
                if (
                  dayjs(time).isBetween(
                    scheduleItem.start,
                    scheduleItem.end,
                    null,
                    '[)'
                  )
                ) {
                  datas[index] = scheduleItem.power
                }
              }
            )
          })
          scheduleData.datas = datas

          historyData.value[resIndex] = {
            ...historyData.value[resIndex],
            scheduleData,
            ...item
          }
        })
      })
    } else {
      historyData.value = []
      snackbar.value = true
      snackbarText.value = res.data.err || t('查询失败')
    }
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error.message || t('查询失败')
    historyData.value = []
  } finally {
    loading.value = false
  }
}

// 日期变化处理
const handleDateChange = (date) => {
  selectedDate.value = dayjs(date).format('YYYY-MM-DD')
  dateMenu.value = false
  getHistoryData()
  getACPowerData()
}

// 格式化时间
const formatTime = (timeStr) => {
  return dayjs(timeStr).format('MM-DD HH:mm:ss')
}

// 获取算法名称
const getAlgorithmName = (algorithm) => {
  const option = algorithmOptions.value.find((item) => item.value === algorithm)
  return option ? option.title : algorithm || 'N/A'
}

// 查看详情
const dialog = ref(false)
const detailsData = ref({})
const viewDetails = (item) => {
  detailsData.value = item
  dialog.value = true
}

// 应用历史策略
const applyHistory = (item) => {
  emit('apply', item)
}

// (地区、日期)电价
const countryDatePriceData = ref([])
const getCountryData = async ({ region, utcDate: date }) => {
  // 如果没有相应的国家和日期电价，则添加
  // 并进行处理
  let value = countryDatePriceData.value.find(
    (item) => item.region === region && item.date === date
  )
  if (value) return value
  try {
    const res = await useParamStore().countryDataFn({
      region,
      date
    })
    let obj = {
      region,
      date,
      ...res,
      lineData: {
        times: [],
        datas: [],
        currency: '',
        name: '',
        date: ''
      }
    }

    let times = generateTimePoints(res.timeInterval, 'YYYY-MM-DD HH:mm:ss', {
      value: 1,
      unit: 'minute'
    })
    // 电价曲线
    obj.lineData.times = times
    obj.lineData.charge_equal_discharge = res.charge_equal_discharge
    let priceDatas = []
    res.points.forEach((item) => {
      if (res.resolution == 60) {
        let arr = new Array(60).fill(
          res.charge_equal_discharge
            ? item.charge_price_amount
            : {
                charge_price_amount: item.charge_price_amount,
                discharge_price_amount: item.discharge_price_amount
              }
        )
        priceDatas = [...priceDatas, ...arr]
      } else {
        let arr = new Array(15).fill(
          res.charge_equal_discharge
            ? item.charge_price_amount
            : {
                charge_price_amount: item.charge_price_amount,
                discharge_price_amount: item.discharge_price_amount
              }
        )
        priceDatas = [...priceDatas, ...arr]
      }
    })
    obj.lineData.datas = priceDatas
    obj.lineData.currency = res.currency
    obj.lineData.name = countryOptions.value.find(
      (item) => item.code == region
    ).name
    obj.lineData.date = date
    // getACPowerData()

    countryDatePriceData.value.push(obj)
    return obj
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
  }
}

// 获取设备功率
const powerData = ref([])
const getACPowerData = async () => {
  powerData.value = []
  Promise.all([
    useStatisticsStore().getPointDataFn(
      JSON.stringify(
        ACDeviceRequestParams.value.map((item) => {
          return {
            device_id: item.device_id,
            point_id: item.point_id,
            date: selectedDate.value
          }
        })
      )
    )
  ])
    .then((results) => {
      results.forEach((item, resIndex) => {
        if (item?.datas.length) {
          item?.datas.forEach((item2, index2) => {
            let param = ACDeviceRequestParams.value.find(
              (param) =>
                param.point_id == item.pointId[index2].pointId &&
                param.device_name == item.pointId[index2].deviceName
            )
            powerData.value.push({
              ...param,
              datas: item2,
              times: item?.times[index2],
              unit: item.pointId[index2].unit
            })
          })
        }
      })
    })
    .catch((err) => {})
}

onMounted(() => {
  getHistoryData()
  getACPowerData()
})
</script>

<template>
  <v-card class="history-strategy-card">
    <!-- 头部 -->
    <v-card-title class="pa-4 bg-primary-lighten-5">
      <div class="d-flex align-center w-100">
        <v-icon
          icon="mdi-history"
          color="primary"
          class="mr-3"
          size="28"
        ></v-icon>
        <div>
          <div class="text-h5 font-weight-bold">{{ $t('历史策略记录') }}</div>
          <div class="text-caption text-grey-darken-1 mt-1">
            {{ $t('查看和管理历史调度策略') }}
          </div>
        </div>
        <v-spacer />
        <v-chip
          :color="historyData.length > 1 ? 'warning' : 'success'"
          variant="elevated"
          class="mr-3"
        >
          <v-icon start icon="mdi-database"></v-icon>
          {{ historyData.length }} {{ $t('条记录') }}
        </v-chip>
        <v-btn
          icon="mdi-close"
          variant="text"
          @click="$emit('close')"
          size="large"
        ></v-btn>
      </div>
    </v-card-title>

    <v-divider></v-divider>

    <!-- 查询区域 -->
    <v-card-text class="pa-4 bg-grey-lighten-5">
      <v-row align="center">
        <v-col cols="3">
          <v-menu v-model="dateMenu" :close-on-content-click="false">
            <template v-slot:activator="{ props }">
              <v-text-field
                :model-value="selectedDate"
                :label="$t('查询日期')"
                readonly
                v-bind="props"
                variant="outlined"
                density="comfortable"
                prepend-inner-icon="mdi-calendar"
                color="primary"
                hide-details
              />
            </template>
            <v-date-picker
              v-model="dateValue"
              @update:modelValue="handleDateChange"
              color="primary"
              elevation="8"
            />
          </v-menu>
        </v-col>
        <v-col cols="2">
          <v-btn
            color="primary"
            @click="getHistoryData"
            :loading="loading"
            variant="elevated"
            size="large"
          >
            <v-icon start icon="mdi-magnify"></v-icon>
            {{ $t('查询') }}
          </v-btn>
        </v-col>
        <v-spacer />
        <v-col cols="auto">
          <v-chip variant="outlined" color="info">
            <v-icon start icon="mdi-information"></v-icon>
            {{ $t('最新记录在顶部') }}
          </v-chip>
        </v-col>
      </v-row>
    </v-card-text>

    <v-divider></v-divider>

    <!-- 内容区域 -->
    <v-card-text class="pa-0" style="max-height: 70vh; overflow-y: auto">
      <!-- 加载状态 -->
      <div v-if="loading" class="text-center py-12">
        <v-progress-circular
          indeterminate
          color="primary"
          size="64"
          width="6"
        ></v-progress-circular>
        <div class="mt-4 text-h6 text-grey">{{ $t('加载中') }}...</div>
      </div>

      <!-- 空状态 -->
      <div v-else-if="!historyData.length" class="text-center py-12">
        <v-icon
          icon="mdi-calendar-remove"
          size="80"
          color="grey-lighten-2"
          class="mb-4"
        ></v-icon>
        <div class="text-h6 text-grey mb-2">{{ $t('该日期暂无历史记录') }}</div>
        <div class="text-body-2 text-grey">
          {{ $t('请选择其他日期或创建新的策略') }}
        </div>
      </div>

      <!-- 历史记录列表 -->
      <div v-else class="pa-4">
        <v-row>
          <v-col
            v-for="(item, index) in historyData"
            :key="index"
            cols="12"
            md="6"
            lg="6"
            class="mb-4"
          >
            <v-card
              :class="[
                'history-item-card h-100',
                index === 0 ? 'border-primary elevation-4' : 'elevation-2'
              ]"
              :color="index === 0 ? 'primary-lighten-5' : ''"
              hover
            >
              <!-- 卡片头部 -->
              <v-card-title class="pa-2 pb-2">
                <div class="d-flex align-center">
                  <v-avatar
                    :color="index === 0 ? 'primary' : 'grey'"
                    size="32"
                    class="mr-3"
                  >
                    <span class="text-white font-weight-bold">
                      {{ index === 0 ? '★' : index + 1 }}
                    </span>
                  </v-avatar>
                  <div class="flex-grow-1">
                    <div class="text-subtitle-1 font-weight-bold">
                      {{ getAlgorithmName(item.algorithm) }}
                    </div>
                    <div class="text-caption text-grey">
                      {{ formatTime(item.createdAtLocal) }}
                    </div>
                  </div>
                  <v-chip
                    :color="index === 0 ? 'primary' : 'grey'"
                    size="small"
                    variant="elevated"
                  >
                    {{ index === 0 ? $t('最新') : $t('历史') }}
                  </v-chip>
                </div>
              </v-card-title>

              <!-- 参数信息 -->
              <v-card-text class="px-3">
                <v-row dense class="mb-2">
                  <v-col cols="12">
                    <div class="param-item">
                      <v-icon
                        icon="mdi-map-marker"
                        size="16"
                        class="mr-2 text-primary"
                      ></v-icon>
                      <span class="text-caption text-grey"
                        >{{ $t('地区') }}:</span
                      >
                      <span class="text-body-2 font-weight-medium ml-1">
                        {{ item.params.region || 'N/A' }}
                      </span>
                    </div>
                  </v-col>
                </v-row>

                <!-- 关键参数网格 -->
                <v-row dense class="mb-2">
                  <v-col cols="6">
                    <div class="param-box">
                      <div class="text-caption text-grey">Current SOC</div>
                      <div class="text-h6 text-primary">
                        {{ item.params.outterCurrentSOC || 0 }}%
                      </div>
                    </div>
                  </v-col>
                  <v-col cols="6">
                    <div class="param-box">
                      <div class="text-caption text-grey">
                        {{ $t('调度点') }}
                      </div>
                      <div class="text-h6 text-success">
                        {{ item.scheduleUnits?.length || 0 }}
                      </div>
                    </div>
                  </v-col>
                </v-row>

                <!-- SOC 范围 -->
                <div class="mb-2">
                  <div class="text-caption text-grey mb-1">
                    SOC {{ $t('范围') }}
                  </div>
                  <v-progress-linear
                    :model-value="item.params.outterCurrentSOC || 0"
                    :min="item.params.minSOC || 0"
                    :max="item.params.maxSOC || 100"
                    color="primary"
                    height="8"
                    rounded
                    class="mb-1"
                  ></v-progress-linear>
                  <div
                    class="d-flex justify-space-between text-caption text-grey"
                  >
                    <span
                      >{{ $t('最小') }}: {{ item.params.minSOC || 0 }}%</span
                    >
                    <span
                      >{{ $t('最大') }}: {{ item.params.maxSOC || 0 }}%</span
                    >
                  </div>
                </div>

                <!-- 功率信息 -->
                <div class="mb-2">
                  <v-row dense>
                    <v-col cols="6">
                      <div class="power-item">
                        <v-icon
                          icon="mdi-battery-charging"
                          size="16"
                          color="success"
                        ></v-icon>
                        <span class="text-caption ml-1"
                          >{{ $t('充电') }}:
                          {{ item.params.maxChgPower || 0 }}kW</span
                        >
                      </div>
                    </v-col>
                    <v-col cols="6">
                      <div class="power-item">
                        <v-icon
                          icon="mdi-battery-minus"
                          size="16"
                          color="warning"
                        ></v-icon>
                        <span class="text-caption ml-1"
                          >{{ $t('放电') }}:
                          {{ item.params.maxDischgPower || 0 }}kW</span
                        >
                      </div>
                    </v-col>
                  </v-row>
                </div>

                <!-- 调度预览 -->
                <div v-if="item.scheduleUnits?.length" class="mb-2">
                  <div class="text-caption text-grey">
                    {{ $t('调度预览') }}
                  </div>
                  <LineEchart
                    style="height: 250px; width: 100%"
                    v-model:lineData="item.lineData"
                    v-model:scheduleData="item.scheduleData"
                    v-model:powerData="powerData"
                  />
                </div>
              </v-card-text>

              <!-- 操作按钮 -->
              <v-card-actions class="pa-2 pt-0">
                <v-btn
                  variant="outlined"
                  size="small"
                  @click="viewDetails({ ...item, index })"
                  class="flex-grow-1"
                  prepend-icon="mdi-eye"
                >
                  {{ $t('详情') }}
                </v-btn>
                <v-btn
                  color="primary"
                  size="small"
                  @click="applyHistory(item)"
                  class="flex-grow-1 ml-2"
                  :disabled="index === 0"
                  prepend-icon="mdi-check"
                >
                  {{ $t('应用') }}
                </v-btn>
              </v-card-actions>
            </v-card>
          </v-col>
        </v-row>
      </div>
    </v-card-text>

    <v-dialog v-model="dialog" max-width="800" persistent>
      <v-card class="rounded-lg">
        <v-card-title class="pa-4 pb-2 bg-primary-lighten-5">
          <div class="d-flex align-center justify-between w-100">
            <div class="flex align-center">
              <v-icon
                icon="mdi-information-outline"
                color="primary"
                class="mr-3"
                size="28"
              ></v-icon>
              <div>
                <div class="text-h5 font-weight-bold">{{ $t('策略详情') }}</div>
                <div class="text-caption text-grey-darken-1 mt-1">
                  {{ formatTime(detailsData.createdAtLocal) }}
                </div>
              </div>
            </div>
            <div>
              <v-chip variant="elevated">
                {{ getAlgorithmName(detailsData.algorithm) }}
              </v-chip>
              <v-btn
                icon="mdi-close"
                variant="text"
                @click="dialog = false"
                size="large"
                class="ml-2"
              ></v-btn>
            </div>
          </div>
        </v-card-title>

        <v-divider></v-divider>

        <div class="pa-4">
          <!-- 基础信息 -->
          <div class="mb-6">
            <div class="text-h6 mb-2 d-flex align-center">
              <v-icon icon="mdi-cog" color="primary" class="mr-2"></v-icon>
              {{ $t('基础配置') }}
            </div>
            <div class="flex justify-between w-100">
              <div
                class="pa-2 border-1 border-solid border-black w-49% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('算法类型') }}
                </div>
                <div class="text-body-1 font-weight-medium">
                  {{ getAlgorithmName(detailsData.algorithm) }}
                </div>
              </div>
              <div
                class="pa-2 border-1 border-solid border-black w-49% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('地区') }}
                </div>
                <div class="text-body-1 font-weight-medium">
                  {{ detailsData.params.region }}
                </div>
              </div>
            </div>
          </div>

          <!-- SOC配置 -->
          <div class="mb-6">
            <div class="text-h6 mb-2 d-flex align-center">
              <v-icon icon="mdi-battery" color="success" class="mr-2"></v-icon>
              {{ $t('SOC配置') }}
            </div>
            <div class="flex justify-between w-100">
              <div
                class="pa-2 border-1 border-solid border-black w-32% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('最大SOC') }}
                </div>
                <div class="text-h6 text-success">
                  {{ detailsData.params.maxSOC }}%
                </div>
              </div>
              <div
                class="pa-2 border-1 border-solid border-black w-32% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('最小SOC') }}
                </div>
                <div class="text-h6 text-warning">
                  {{ detailsData.params.minSOC }}%
                </div>
              </div>
              <div
                class="pa-2 border-1 border-solid border-black w-32% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('使用外部SOC') }}
                </div>
                <v-chip
                  :color="
                    detailsData.params.useOutterCurrentSOC ? 'success' : 'grey'
                  "
                  size="small"
                >
                  {{
                    detailsData.params.useOutterCurrentSOC ? $t('是') : $t('否')
                  }}
                </v-chip>
                <div
                  v-if="detailsData.params.useOutterCurrentSOC"
                  class="text-caption mt-1"
                >
                  {{ $t('外部SOC') }}:
                  {{ detailsData.params.outterCurrentSOC }}%
                </div>
              </div>
            </div>
          </div>

          <!-- 功率配置 -->
          <div class="mb-6">
            <div class="text-h6 mb-2 d-flex align-center">
              <v-icon icon="mdi-flash" color="warning" class="mr-2"></v-icon>
              {{ $t('功率配置') }}
            </div>
            <div class="flex justify-between w-100">
              <div
                class="pa-2 border-1 border-solid border-black w-32% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('最大充电功率') }}
                </div>
                <div class="text-h6 text-primary">
                  {{ detailsData.params.maxChgPower }} kW
                </div>
              </div>
              <div
                class="pa-2 border-1 border-solid border-black w-32% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('最大放电功率') }}
                </div>
                <div class="text-h6 text-error">
                  {{ detailsData.params.maxDischgPower }} kW
                </div>
              </div>
              <div
                class="pa-2 border-1 border-solid border-black w-32% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('电池容量') }}
                </div>
                <div class="text-h6 text-info">
                  {{ detailsData.params.batteryCapacity }} kWh
                </div>
              </div>
            </div>
          </div>

          <!-- 效率配置 -->
          <div class="mb-6">
            <div class="text-h6 mb-2 d-flex align-center">
              <v-icon icon="mdi-chart-line" color="info" class="mr-2"></v-icon>
              {{ $t('效率配置') }}
            </div>
            <div class="flex justify-between w-100">
              <div
                class="pa-2 border-1 border-solid border-black w-32% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('充电效率') }}
                </div>
                <div class="text-h6 text-success">
                  {{ (detailsData.params.etaChg * 100).toFixed(1) }}%
                </div>
              </div>
              <div
                class="pa-2 border-1 border-solid border-black w-32% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('放电效率') }}
                </div>
                <div class="text-h6 text-warning">
                  {{ (detailsData.params.etaDis * 100).toFixed(1) }}%
                </div>
              </div>
              <div
                class="pa-2 border-1 border-solid border-black w-32% rounded-l"
              >
                <div class="text-caption text-grey mb-1">
                  {{ $t('价格阈值比例') }}
                </div>
                <div class="text-h6 text-info">
                  {{
                    (detailsData.params.priceThresholdRatio * 100).toFixed(1)
                  }}%
                </div>
              </div>
            </div>
          </div>
        </div>

        <v-divider></v-divider>

        <v-card-actions class="pa-4">
          <v-spacer />
          <v-btn color="grey" variant="text" @click="dialog = false">
            {{ $t('关闭') }}
          </v-btn>
          <v-btn
            color="primary"
            variant="elevated"
            @click="applyHistory(detailsData)"
            :disabled="detailsData.index === 0"
            prepend-icon="mdi-check"
          >
            {{ $t('应用此策略') }}
          </v-btn>
        </v-card-actions>
      </v-card>
    </v-dialog>
  </v-card>
</template>

<style scoped>
.history-strategy-card {
  border-radius: 12px !important;
}

.history-item-card {
  border-radius: 8px !important;
  transition: all 0.3s ease;
}

.history-item-card:hover {
  transform: translateY(-2px);
}

.param-item {
  display: flex;
  align-items: center;
  margin-bottom: 4px;
}

.param-box {
  text-align: center;
  padding: 8px;
  background: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

.power-item {
  display: flex;
  align-items: center;
}

.schedule-chips {
  max-height: 60px;
  overflow-y: auto;
}

.border-primary {
  border: 2px solid rgb(var(--v-theme-primary)) !important;
}
</style>
