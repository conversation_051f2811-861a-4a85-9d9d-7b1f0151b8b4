<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-06 14:45:00
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-16 11:24:33
 * @FilePath: \ems_manage\src\views\Dashboard\lineEchart.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, watch, computed } from 'vue'
import { maxBy } from 'lodash-es'

const props = defineProps({
  lineData: Array
})

const times = ref([])
const series = ref([])
const options = computed(() => {
  return {
    tooltip: {
      trigger: 'axis',
      axisPointer: {
        type: 'cross'
      },
      padding: [5, 10],
      formatter: (params) => {
        let htmlStart = `<div><div>${params[0].axisValueLabel}</div>`
        params.forEach((param, index) => {
          if (!props.lineData.length) return
          htmlStart += `
              <div style="display: flex;align-items: center;">
                <span style="display: inline-block;background: ${
                  param.color
                };width: 10px; height: 10px;border-radius: 50%;margin-right: 5px;"></span>
                ${param.seriesName}：
                <span style="font-weight: 600;margin-right: 3px;">${
                  param.value !== null && param.value !== undefined
                    ? param.value[1]
                    : '--'
                }</span> ${
            props.lineData[index]?.unit !== undefined
              ? props.lineData[index]?.unit
              : ''
          }
              </div>`
        })
        return htmlStart + '</div>'
      }
    },
    legend: {
      type: 'scroll',
      textStyle: {
        fontSize: 12,
        fontWeight: 400
        // color: 'rgba(0, 0, 0, .9)',
        // lineHeight: 18
      },
      itemGap: 30,
      top: 5
    },
    grid: {
      left: '5%',
      right: '8%',
      bottom: '8%',
      containLabel: true
    },
    xAxis: {
      axisLine: {
        lineStyle: {
          // color: '#fff'
        }
      },
      axisTick: {
        //y轴刻度线
        show: true
      },
      splitLine: {
        //分割线
        show: false, //控制分割线是否显示
        lineStyle: {
          //分割线的样式
          color: 'rgba(81, 82, 85, 0.3)',
          width: 1,
          type: 'solid'
        }
      },
      type: 'time',
      boundaryGap: false
      // data: times.value
    },
    yAxis: {
      type: 'value',
      axisLine: {
        lineStyle: {
          // color: '#fff'
        }
      },
      splitLine: {
        //分割线
        show: false, //控制分割线是否显示
        lineStyle: {
          //分割线的样式
          color: 'rgba(81, 82, 85, 0.3)',
          width: 1,
          type: 'solid'
        }
      },
      minInterval: 5
    },
    dataZoom: [
      {
        type: 'inside',
        start: 0
      },
      {
        start: 0
      }
    ],
    series: series.value
  }
})
const mapData = () => {
  series.value = []
  console.log(props.lineData)
  props.lineData.forEach((item) => {
    let data = item.times.map((item2, index2) => {
      return [item2, item.datas[index2]]
    })
    let name = `${item.device_name}${
      item.module_name !== null ? '_' + item.module_name + '_' : '_'
    } ${item.title}`
    series.value.push({
      name,
      type: 'line',
      symbol: 'none',
      data,
      unit: item.unit
    })
  })
}
mapData()
watch(
  () => props.lineData,
  () => {
    mapData()
  },
  {
    deep: true
  }
)
</script>

<template>
  <div>
    <BaseEchart
      width="100%"
      height="100%"
      :options="options"
      ref="myChart"
    ></BaseEchart>
  </div>
</template>

<style lang="scss" scoped></style>
