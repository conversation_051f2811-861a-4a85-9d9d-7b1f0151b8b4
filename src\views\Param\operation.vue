<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-29 14:53:50
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-04-24 16:39:13
 * @FilePath: \ems_manage\src\views\Param\strategy.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import {
  ref,
  toRefs,
  getCurrentInstance,
  onMounted,
  computed,
  nextTick
} from 'vue'
import { useGlobalStore } from '@/store/global'
import { useParamStore } from '@/store/module/param'
import { useI18n } from 'vue-i18n'
import { isBetween, setLeftWidth } from '@/utils'
import { useUserStore } from '@/store/module/user'
import { useLocale, useDisplay } from 'vuetify'

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { hybridInfo } = toRefs(useParamStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  keyboardRange,
  isShowKeyboard
} = toRefs(useGlobalStore())
const { userInfo } = toRefs(useUserStore())
const { current } = useLocale()
const { mobile } = useDisplay()

const getData = () => {
  useParamStore()
    .hybridEnergyInfoFn()
    .then((res) => {
      sendForm.value = {
        ...hybridInfo.value
      }
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
getData()

/**
 * 下发策略
 */
const sendForm = ref({
  average_soc: '0',
  backup_soc: '0',
  charging_current_limit: '0',
  discharge_current_limit: '0',
  energy_storage_discharge: '0',
  engine_capacity: '0',
  engine_on: '1',
  overvoltage_protection: '0',
  overvoltage_recovery: '0',
  power_grid_model: '0',
  regulation_strategy: '1',
  reverse_power_protection: '1',
  stop_charging_point: '0',
  stop_discharge_point: '0',
  target_value: '0',
  transformer_capacity: '0',
  undervoltage_protection: '0',
  undervoltage_recovery: '0'
})
const model = computed(() => {
  return (value) => (sendForm[value] == '0' ? t('不使能') : t('使能'))
})
const handleSendClick = (value, range, prop) => {
  currentInput.value = prop
  if (value) {
    const regex = /^-?\d+(\.\d+)?$/
    if (!regex.test(value)) {
      snackbar.value = true
      snackbarText.value = t('只能为数字')
      getData()
      return
    }
    if (value === '' && isShowKeyboard.value) {
      snackbar.value = true
      snackbarText.value = t('数据不能为空')
      getData()
      return
    }
  } else {
    if (value === '' && isShowKeyboard.value) {
      snackbar.value = true
      snackbarText.value = t('数据不能为空')
      getData()
      return
    }
  }
  if (range) {
    if (!isBetween(Number(value), range[0], range[1])) {
      snackbar.value = true
      snackbarText.value = t('range', range)
      getData()
      return
    }
  }
  let data = {
    user: userInfo.value.user_name
    // average_soc: sendForm.value.average_soc,
    // backup_soc: sendForm.value.backup_soc,
    // charging_current_limit: sendForm.value.charging_current_limit,
    // discharge_current_limit: sendForm.value.discharge_current_limit,
    // energy_storage_discharge: sendForm.value.energy_storage_discharge,
    // engine_capacity: sendForm.value.engine_capacity,
    // engine_on: sendForm.value.engine_on,
    // overvoltage_protection: sendForm.value.overvoltage_protection,
    // overvoltage_recovery: sendForm.value.overvoltage_recovery,
    // power_grid_model: sendForm.value.power_grid_model,
    // regulation_strategy: sendForm.value.regulation_strategy,
    // reverse_power_protection: sendForm.value.reverse_power_protection,
    // stop_charging_point: sendForm.value.stop_charging_point,
    // stop_discharge_point: sendForm.value.stop_discharge_point,
    // target_value: sendForm.value.target_value,
    // transformer_capacity: sendForm.value.transformer_capacity,
    // undervoltage_protection: sendForm.value.undervoltage_protection,
    // undervoltage_recovery: sendForm.value.undervoltage_recovery
  }
  if (keyboardDialog.value) {
    data[currentInput.value] = keyboardInputValue.value
  } else {
    data[currentInput.value] = value
  }
  useParamStore()
    .hybridEnergySetFn(JSON.stringify(data))
    .then((res) => {
      snackbar.value = true
      snackbarText.value = t('下发成功')
      if (keyboardDialog.value) {
        sendForm.value[currentInput.value] = keyboardInputValue.value
        showKeyboard.value = false
        keyboardDialog.value = false
      }
      getData()
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
      getData()
    })
}
const handleControlClick = (value, prop) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  handleSendClick(value, undefined, currentInput.value)
}

onMounted(() => {
  setLeftWidth('left')
})

/**
 * 键盘
 */
keyboardMode.value = 'di_git'
const handleShow = (e, value, prop, range) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
  keyboardRange.value = range
}
confirmCall.value = () => {
  if (!keyboardInputValue.value) {
    snackbar.value = true
    snackbarText.value = t('数据不能为空')
    return
  }
  if (
    !isBetween(
      Number(keyboardInputValue.value),
      keyboardRange.value[0],
      keyboardRange.value[1]
    )
  ) {
    snackbar.value = true
    snackbarText.value = t('range', keyboardRange.value)
    return
  }
  handleSendClick(
    keyboardInputValue.value,
    keyboardRange.value,
    currentInput.value
  )
}

const tab = ref(1)
const handleWindowChange = () => {
  nextTick(() => {
    setLeftWidth('left')
  })
}

/**
 * 电池soc
 */
const batteryValues = ref([15, 65, 100])
const batteryColors = ref(['#fe5f69', '#ffc95c', '#64ba8c'])
const EngineStarting = computed(() => {
  return {
    value: ((289 - 16) / 100) * Number(sendForm.value.stop_discharge_point),
    color: '#ec532b'
  }
}) // 油机启动
const line = computed(() => {
  return {
    value: ((289 - 16) / 100) * Number(sendForm.value.backup_soc),
    color: '#000'
  }
}) // 备电soc
const chargeSocHeight = computed(() => {
  return {
    value: ((289 - 16) / 100) * Number(sendForm.value.stop_charging_point),
    color: '#7357ff'
  }
}) // 停止充电

const batteryValueHeight = computed(
  () => ((289 - 16) / 100) * Number(sendForm.value.average_soc) + 'px'
)
const batteryColor = computed(() => {
  {
    for (let i = 0; i < batteryValues.value.length; i++) {
      if (Number(sendForm.value.average_soc) <= batteryValues.value[i]) {
        return batteryColors.value[i]
      }
    }
    return batteryColors.value[batteryColors.value.length - 1]
  }
})
</script>

<template>
  <div class="pa-6 overflow-auto">
    <v-card class="pa-4 w-100 rounded-lg no-scrollbar mb-4" elevation="4">
      <div class="d-flex justify-between align-center">
        <div class="text-h6">{{ $t('运行策略') }}</div>
      </div>
      <div :class="[mobile && 'flex-column', 'flex']">
        <div :style="{ width: mobile ? '100%' : '70%' }">
          <v-tabs
            v-model="tab"
            color="primary"
            class="my-4"
            @update:modelValue="handleWindowChange"
          >
            <v-tab :text="$t('电池设置')" :value="1"></v-tab>
            <v-tab :text="$t('油机设置')" :value="2"></v-tab>
            <v-tab :text="$t('并离网设置')" :value="3"></v-tab>
            <v-tab :text="$t('防逆流设置')" :value="4"></v-tab>
          </v-tabs>
          <v-tabs-window v-model="tab" class="w-100">
            <v-tabs-window-item :value="1" class="w-100">
              <v-col cols="12" lg="8" md="12" sm="12" class="pa-0 w-100">
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('停止充电点') }}</div>
                    <v-tooltip
                      :text="$t('当SOC达到设定值，储能停止充电。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.stop_charging_point"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.stop_charging_point,
                          'stop_charging_point',
                          [0, 100]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">0 ~ 100 %</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.stop_charging_point,
                          [0, 100],
                          'stop_charging_point'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('备电SOC') }}</div>
                    <v-tooltip
                      :text="$t('并网模式下，当SOC达到设定值，储能停止放电。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.backup_soc"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.backup_soc,
                          'backup_soc',
                          [0, 95]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">0 ~ 95 %</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.backup_soc,
                          [0, 95],
                          'backup_soc'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('停止放电点') }}</div>
                    <v-tooltip
                      :text="
                        $t(
                          '当SOC达到设定值，储能停止放电，离网模式下，启动油机。'
                        )
                      "
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <template v-if="userInfo.permission_level == 4">
                    <v-col cols="5" class="pl-0 flex">
                      <v-text-field
                        v-model="sendForm.stop_discharge_point"
                        variant="outlined"
                        label=""
                        hide-details
                        @click:control="
                          handleShow(
                            $event,
                            sendForm.stop_discharge_point,
                            'stop_discharge_point',
                            [0, 95]
                          )
                        "
                      ></v-text-field>
                    </v-col>
                    <v-col cols="2" class="pl-0">
                      <div class="text-body-1 py-4 px-0">0 ~ 95 %</div>
                    </v-col>
                    <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                      <v-btn
                        @click="
                          handleSendClick(
                            sendForm.stop_discharge_point,
                            [0, 95],
                            'stop_discharge_point'
                          )
                        "
                        >{{ $t('下发') }}</v-btn
                      >
                    </v-col>
                  </template>
                  <template v-else>
                    <v-col cols="5" class="pl-0 flex">
                      <v-text-field
                        v-model="sendForm.stop_discharge_point"
                        variant="outlined"
                        label=""
                        hide-details
                        @click:control="
                          handleShow(
                            $event,
                            sendForm.stop_discharge_point,
                            'stop_discharge_point',
                            [10, 95]
                          )
                        "
                      ></v-text-field>
                    </v-col>
                    <v-col cols="2" class="pl-0">
                      <div class="text-body-1 py-4 px-0">10 ~ 95 %</div>
                    </v-col>
                    <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                      <v-btn
                        @click="
                          handleSendClick(
                            sendForm.stop_discharge_point,
                            [10, 95],
                            'stop_discharge_point'
                          )
                        "
                        >{{ $t('下发') }}</v-btn
                      >
                    </v-col>
                  </template>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('充电限流值') }}</div>
                    <v-tooltip
                      :text="$t('设置电池充电时的电流最大值。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.charging_current_limit"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.charging_current_limit,
                          'charging_current_limit',
                          [0, 5000]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">0 ~ 5000 A</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.charging_current_limit,
                          [0, 5000],
                          'charging_current_limit'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('放电限流值') }}</div>
                    <v-tooltip
                      :text="$t('设置电池放电时的电流最大值。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.discharge_current_limit"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.discharge_current_limit,
                          'discharge_current_limit',
                          [0, 5000]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">0 ~ 5000 A</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.discharge_current_limit,
                          [0, 5000],
                          'discharge_current_limit'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('欠压保护') }}</div>
                    <v-tooltip :text="$t('电池放电保护电压。')" location="top">
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.undervoltage_protection"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.undervoltage_protection,
                          'undervoltage_protection',
                          [50, 1000]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">50 ~ 1000 V</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.undervoltage_protection,
                          [50, 1000],
                          'undervoltage_protection'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('欠压恢复') }}</div>
                    <v-tooltip
                      :text="$t('电池可放电恢复电压。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.undervoltage_recovery"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.undervoltage_recovery,
                          'undervoltage_recovery',
                          [50, 1000]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">50 ~ 1000 V</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.undervoltage_recovery,
                          [50, 1000],
                          'undervoltage_recovery'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('过压保护') }}</div>
                    <v-tooltip
                      :text="$t('电池充电电保护电压。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.overvoltage_protection"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.overvoltage_protection,
                          'overvoltage_protection',
                          [50, 1000]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">50 ~ 1000 V</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.overvoltage_protection,
                          [50, 1000],
                          'overvoltage_protection'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('过压恢复') }}</div>
                    <v-tooltip
                      :text="$t('电池可充电恢复电压。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.overvoltage_recovery"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.overvoltage_recovery,
                          'overvoltage_recovery',
                          [50, 1000]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">50 ~ 1000 V</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.overvoltage_recovery,
                          [50, 1000],
                          'overvoltage_recovery'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
              </v-col>
            </v-tabs-window-item>
            <v-tabs-window-item :value="2" class="w-100">
              <v-col cols="12" lg="8" md="12" sm="12" class="pa-0 w-100">
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('油机启用') }}</div>
                    <v-tooltip
                      :text="$t('启用后，油机会在储能停止放电时启动油机。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-switch
                      v-model="sendForm.engine_on"
                      :label="`${model('engine_on')}`"
                      false-value="0"
                      true-value="1"
                      hide-details
                      color="primary"
                      @update:modelValue="
                        handleControlClick(sendForm.engine_on, 'engine_on')
                      "
                    />
                  </v-col>
                  <v-col cols="2" class="pl-0"> </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.engine_on,
                          undefined,
                          'engine_on'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('油机容量') }}</div>
                    <v-tooltip
                      :text="$t('当油机工作时，储能会在设定容量内进行充电。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.engine_capacity"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.engine_capacity,
                          'engine_capacity',
                          [0, 3000]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">0 ~ 3000 kVA</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.engine_capacity,
                          [0, 3000],
                          'engine_capacity'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
              </v-col>
            </v-tabs-window-item>
            <v-tabs-window-item :value="3" class="w-100">
              <v-col cols="12" lg="8" md="12" sm="12" class="pa-0 w-100">
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('电网模式') }}</div>
                    <v-tooltip
                      :text="
                        $t(
                          '当有市电时，并网优先会优先使用市电，离网优先会优先离网供电。'
                        )
                      "
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" lg="7" md="5" sm="5" class="pl-0 flex">
                    <v-radio-group
                      v-model="sendForm.power_grid_model"
                      inline
                      hide-details
                      @update:modelValue="
                        handleControlClick(
                          sendForm.power_grid_model,
                          'power_grid_model'
                        )
                      "
                    >
                      <v-radio :label="$t('并网优先')" value="0"></v-radio>
                      <v-radio :label="$t('离网优先')" value="1"></v-radio>
                    </v-radio-group>
                  </v-col>
                  <v-col cols="2" class="pl-0"> </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.power_grid_model,
                          undefined,
                          'power_grid_model'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('变压器容量') }}</div>
                    <v-tooltip
                      :text="$t('当并网工作时，储能会在设定容量内进行充电。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" lg="7" md="5" sm="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.transformer_capacity"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.transformer_capacity,
                          'transformer_capacity',
                          [0, 5000]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">0 ~ 5000 kVA</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.transformer_capacity,
                          [0, 5000],
                          'transformer_capacity'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
              </v-col>
            </v-tabs-window-item>
            <v-tabs-window-item :value="4" class="w-100">
              <v-col cols="12" lg="8" md="12" sm="12" class="pa-0 w-100">
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('防逆流') }}</div>
                    <v-tooltip
                      :text="$t('使能后，防止光伏和储能的能量馈入电网。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-switch
                      v-model="sendForm.reverse_power_protection"
                      :label="`${model('reverse_power_protection')}`"
                      false-value="0"
                      true-value="1"
                      hide-details
                      color="primary"
                      @update:modelValue="
                        handleControlClick(
                          sendForm.reverse_power_protection,
                          'reverse_power_protection'
                        )
                      "
                    />
                  </v-col>
                  <v-col cols="2" class="pl-0"> </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.reverse_power_protection,
                          undefined,
                          'reverse_power_protection'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('储能放电') }}</div>
                    <v-tooltip
                      :text="$t('设定储能放电最大值。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.energy_storage_discharge"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.energy_storage_discharge,
                          'energy_storage_discharge',
                          [0, 5000]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">0 ~ 5000 kW</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.energy_storage_discharge,
                          [0, 5000],
                          'energy_storage_discharge'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('调节策略') }}</div>
                    <v-tooltip
                      :text="$t('降低放电功率，允许充电调节。')"
                      location="top"
                    >
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col :cols="5" class="px-0 flex">
                    <v-radio-group
                      v-model="sendForm.regulation_strategy"
                      inline
                      hide-details
                      @update:modelValue="
                        handleControlClick(
                          sendForm.regulation_strategy,
                          'regulation_strategy'
                        )
                      "
                    >
                      <v-radio :label="$t('降功率调节')" value="0"></v-radio>
                      <v-radio
                        :label="$t('储能允许充电调节')"
                        value="1"
                      ></v-radio>
                    </v-radio-group>
                  </v-col>
                  <!-- <template v-if="current == 'zhHans'"> -->
                  <v-col cols="2" class="pl-0"> </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.regulation_strategy,
                          undefined,
                          'regulation_strategy'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                  <!-- </template> -->
                  <!-- <template v-else>
                    <v-btn
                      class="ml--12"
                      @click="
                        handleSendClick(
                          sendForm.regulation_strategy,
                          undefined,
                          'regulation_strategy'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </template> -->
                </div>
                <div class="flex align-center row">
                  <div
                    class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
                  >
                    <div class="mr-1">{{ $t('目标值') }}</div>
                    <v-tooltip :text="$t('逆流调节目标值。')" location="top">
                      <template v-slot:activator="{ props }">
                        <v-icon
                          icon="mdi-help-circle"
                          size="small"
                          v-bind="props"
                        ></v-icon> </template
                    ></v-tooltip>
                  </div>
                  <v-col cols="5" class="pl-0 flex">
                    <v-text-field
                      v-model="sendForm.target_value"
                      variant="outlined"
                      label=""
                      hide-details
                      @click:control="
                        handleShow(
                          $event,
                          sendForm.target_value,
                          'target_value',
                          [-20, 0]
                        )
                      "
                    ></v-text-field>
                  </v-col>
                  <v-col cols="2" class="pl-0">
                    <div class="text-body-1 py-4 px-0">-20 ~ 0 kW</div>
                  </v-col>
                  <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                    <v-btn
                      @click="
                        handleSendClick(
                          sendForm.target_value,
                          [-20, 0],
                          'target_value'
                        )
                      "
                      >{{ $t('下发') }}</v-btn
                    >
                  </v-col>
                </div>
              </v-col>
            </v-tabs-window-item>
          </v-tabs-window>
        </div>
        <div
          :style="{ width: mobile ? '100%' : '30%' }"
          class="flex flex-column align-center"
        >
          <v-tabs color="primary" class="my-4"> </v-tabs>
          <div>
            <div
              style="position: relative; width: 200px; height: 340px"
              class="flex flex-column align-center"
            >
              <div
                style="
                  width: 40%;
                  height: 15%;
                  background-color: #d7dae3;
                  border-radius: 20px;
                  position: absolute;
                  top: 0;
                  left: 30%;
                "
                class="flex flex-justify-center align-center"
              >
                <span style="font-size: 14px">{{ '' }}</span>
              </div>
              <div
                style="
                  width: 100%;
                  height: 85%;
                  background-color: #d7dae3;
                  border-radius: 20px;
                  margin-top: 10%;
                  z-index: 999;
                  padding: 8px;
                  position: relative;
                "
              >
                <div
                  style="
                    background-color: #eeeff3;
                    border-radius: 15px;
                    position: relative;
                  "
                  class="w- 100 h-100 flex flex-column-reverse overflow-hidden"
                >
                  <div
                    :style="{
                      height: batteryValueHeight,
                      backgroundColor: batteryColor,
                      borderBottomLeftRadius: '15px',
                      borderBottomRightRadius: '15px',
                      borderTopLeftRadius:
                        sendForm.average_soc == 100 ? '15px' : '0',
                      borderTopRightRadius:
                        sendForm.average_soc == 100 ? '15px' : '0'
                    }"
                  ></div>
                  <!-- 字体 -->
                  <div
                    class="soc-text font-600"
                    :style="{
                      top:
                        93 >= sendForm.average_soc
                          ? 273 - batteryValueHeight.split('px')[0] - 20 + 'px'
                          : 273 - batteryValueHeight.split('px')[0] + 'px'
                    }"
                  >
                    {{ sendForm.average_soc }}%
                  </div>
                </div>
                <!-- 阶段提示 -->
                <div
                  style="position: absolute; left: -280px"
                  :style="{
                    bottom: line.value + 'px'
                  }"
                >
                  <div
                    class="flex align-center"
                    :style="{ height: 279 - line.value + 'px' }"
                  >
                    <div
                      style="
                        background: rgba(0, 0, 0, 0.2);
                        border: 2px solid #000;
                        width: 200px;
                        color: rgba(0, 0, 0, 0.8);
                        border-radius: 6px;
                        min-height: 50px;
                      "
                      class="pa-1"
                    >
                      {{ $t('储能和光伏联合供电') }}
                    </div>
                    <div
                      style="
                        width: 100px;
                        height: 2px;
                        background: rgba(0, 0, 0, 0.6);
                      "
                    ></div>
                    <div
                      style="
                        width: 20px;
                        height: 20px;
                        background: rgba(0, 0, 0, 0.6);
                        border-radius: 50%;
                      "
                      class="flex justify-center align-center"
                    >
                      <div
                        style="
                          width: 12px;
                          height: 12px;
                          background: #fff;
                          border-radius: 50%;
                        "
                      ></div>
                    </div>
                  </div>
                </div>
                <div
                  style="position: absolute; left: -280px"
                  :style="{
                    bottom:
                      (line.value - EngineStarting.value) / 2 +
                      EngineStarting.value -
                      25 +
                      'px'
                  }"
                >
                  <div class="flex align-center">
                    <div
                      style="
                        background: rgba(0, 0, 0, 0.2);
                        border: 2px solid #000;
                        width: 200px;
                        height: 50px;
                        color: rgba(0, 0, 0, 0.8);
                        border-radius: 6px;
                      "
                      class="pa-1"
                    >
                      {{ $t('离网供电') }}
                    </div>
                    <div
                      style="
                        width: 100px;
                        height: 2px;
                        background: rgba(0, 0, 0, 0.6);
                      "
                    ></div>
                    <div
                      style="
                        width: 20px;
                        height: 20px;
                        background: rgba(0, 0, 0, 0.6);
                        border-radius: 50%;
                      "
                      class="flex justify-center align-center"
                    >
                      <div
                        style="
                          width: 12px;
                          height: 12px;
                          background: #fff;
                          border-radius: 50%;
                        "
                      ></div>
                    </div>
                  </div>
                </div>
                <!-- 备电soc -->
                <div
                  style="
                    position: absolute;
                    width: 50%;
                    height: 1px;
                    border-top: 2px dashed;
                    z-index: 10000;
                  "
                  :style="{
                    bottom: line.value + 8 + 'px',
                    borderColor: line.color,
                    right: '-49%'
                  }"
                ></div>
                <div
                  style="position: absolute; width: 100px"
                  :style="{
                    bottom: line.value - 20 + 'px',
                    right: '-100px'
                  }"
                >
                  {{ $t('备电SOC') }}
                </div>
                <div
                  class="soc-dash"
                  :style="{
                    bottom: line.value + 8 + 'px',
                    borderColor: line.color
                  }"
                ></div>
                <!-- 停止充电 -->
                <div
                  style="
                    position: absolute;
                    width: 50%;
                    height: 1px;
                    border-top: 2px dashed;
                    z-index: 10000;
                  "
                  :style="{
                    top: 279 - chargeSocHeight.value + 'px',
                    borderColor: chargeSocHeight.color,
                    right: '-49%'
                  }"
                ></div>
                <div
                  style="position: absolute; width: 160px"
                  :style="{
                    top: 279 - chargeSocHeight.value + 'px',
                    right: '-160px'
                  }"
                >
                  {{ $t('停止充电点') }}
                </div>
                <div
                  class="soc-dash"
                  :style="{
                    bottom: chargeSocHeight.value + 8 + 'px',
                    borderColor: chargeSocHeight.color
                  }"
                ></div>
                <!-- 油机启动 -->
                <div
                  style="
                    position: absolute;
                    width: 50%;
                    height: 1px;
                    border-top: 2px dashed;
                    z-index: 10000;
                  "
                  :style="{
                    top: 279 - EngineStarting.value + 'px',
                    borderColor: EngineStarting.color,
                    right: '-49%'
                  }"
                ></div>
                <div
                  style="position: absolute; width: 160px"
                  :style="{
                    top: 279 - EngineStarting.value + 'px',
                    right: '-160px'
                  }"
                >
                  {{ $t('停止放电点') }}
                </div>
                <div
                  class="soc-dash"
                  :style="{
                    bottom: EngineStarting.value + 8 + 'px',
                    borderColor: EngineStarting.color
                  }"
                ></div>
              </div>
            </div>
          </div>
          <!-- 提示 -->
          <!-- <div class="flex mt-2">
            <div class="flex align-center mr-3">
              <div
                style="width: 30px; height: 2px; border: 2px dashed #c4ebad"
                :style="{ borderColor: chargeSocHeight.color }"
                class="mr-2"
              ></div>
              <div>停止充电SOC</div>
            </div>
            <div class="flex align-center mr-3">
              <div
                style="width: 30px; height: 2px; border: 2px dashed #3fb1e3"
                :style="{ borderColor: EngineStarting.color }"
                class="mr-2"
              ></div>
              <div>油机启动</div>
            </div>
          </div> -->
        </div>
      </div>
    </v-card>
  </div>
</template>

<style lang="scss" scoped>
:deep(.plus) {
  border-radius: 0 !important;
  .v-icon {
    font-size: 50px;
  }
}
:deep(.v-card--variant-outlined) {
  border: thin solid #ccc;
}
.left {
  flex: 0 0 auto; /* 不可拉伸，宽度不变 */
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容显示出来 */
  text-align: right;
}
.row {
  height: 80px;
  width: 100%;
}
.soc-dash {
  position: absolute;
  width: 100%;
  height: 1px;
  border-top: 2px dashed;
}
.soc-text {
  position: absolute;
  text-align: center;
  width: 100%;
}

.arrow-left {
  display: block;
  width: 15px;
  height: 15px;
  border-top: 2px solid #000;
  border-left: 2px solid #000;
}

.arrow-left {
  transform: rotate(-45deg);
}
.arrow-left::after {
  content: '';
  display: block;
  width: 2px;
  height: 150px;
  background-color: black;
  transform: rotate(-45deg) translate(52px, 20px);
  left: 0;
  top: 0;
}
</style>
