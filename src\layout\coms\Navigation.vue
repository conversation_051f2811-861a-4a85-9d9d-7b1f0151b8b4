<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-10 10:49:08
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-18 11:07:10
 * @FilePath: \ems_manage\src\views\layout\coms\Navigation.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->

<script setup>
import { ref, computed, toRefs, getCurrentInstance, reactive } from 'vue'
import { useDisplay, useLocale } from 'vuetify'
import { useUserStore } from '@/store/module/user'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { initLocale } from '@/locale'
import { useGlobalStore } from '@/store/global'
import { useConfigStore } from '@/store/module/config'

const { snackbar, snackbarText } = toRefs(useGlobalStore())
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const router = useRouter()
const { userInfo, menuInfo } = toRefs(useUserStore())
const { systemInfo } = toRefs(useConfigStore())

const { mobile } = useDisplay()
const isShow = computed(() => !mobile.value)
const rail = ref(false)

const items = computed(() => {
  const arr = menuInfo.value.map((item) => {
    return {
      text: t(item.text),
      icon: item.icon,
      path: item.path,
      sort: item.sort,
      isHidden: item.isHidden
    }
  })
  if (mobile.value) {
    return [...arr]
  } else {
    return [...arr.filter((item) => item.path != '/user')]
  }
})

const handleFlxMenu = () => {
  rail.value = !rail.value
}

/**
 * logo
 */
const logo = ref()
const getData = () => {
  useConfigStore()
    .getSystemInfoFn()
    .then((res) => {
      logo.value = systemInfo.value.logo
        ? 'data:image/png;base64,' +
          decodeURIComponent(decodeURIComponent(systemInfo.value.logo))
        : ''
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
getData()

const handleItemClick = (item) => {
  if (item.path == '/login') {
    useUserStore().loginOut()
  }
}
</script>

<template>
  <v-navigation-drawer
    v-model="isShow"
    :rail="rail"
    expand-on-hover
    rail-width="77"
    permanent
    color="secondary"
    :disable-resize-watcher="true"
  >
    <template #prepend>
      <v-list class="mx-2 logo pa-0" nav>
        <v-list-item rounded="xl" :class="[!systemInfo.logo && 'pa-2']">
          <template #prepend v-if="!systemInfo.logo">
            <v-avatar color="#fff" size="large"
              ><span class="text-h6">EMS</span></v-avatar
            >
          </template>
          <v-list-item-title class="text-body-1" v-if="!systemInfo.logo">
            {{ userInfo.user_name }}</v-list-item-title
          >
          <template v-if="systemInfo.logo">
            <v-list-item-title class="text-body-1" v-if="!rail">
              <img :src="logo" alt="logo" style="width: 150px" />
            </v-list-item-title>
          </template>
          <template #append>
            <v-btn
              icon="mdi-pin"
              :variant="rail ? 'plain' : 'tonal'"
              @click="handleFlxMenu"
            ></v-btn>
          </template>
        </v-list-item>
      </v-list>
      <v-divider></v-divider>
    </template>

    <v-list :lines="false" nav>
      <template v-for="(item, i) in items" :key="i">
        <template v-if="!item.children">
          <v-list-item
            rounded="xl"
            height="60"
            :to="item.path"
            :key="i"
            :prepend-icon="item.icon"
            :value="item.text"
            @click="handleItemClick(item)"
            v-if="item.isHidden != 'false'"
          >
            <v-list-item-title class="text-body-1">{{
              item.text
            }}</v-list-item-title>
          </v-list-item>
        </template>
        <template v-else>
          <v-list>
            <v-list-item :title="$t(item.text)"></v-list-item>
            <v-list-group>
              <v-list-item
                v-for="proItem in item.children"
                :key="proItem.text"
                rounded="xl"
                height="60"
                :to="proItem.path"
                :prepend-icon="proItem.icon"
                :value="proItem.text"
              >
                <v-list-item-title class="text-body-1">{{
                  item.text
                }}</v-list-item-title>
              </v-list-item>
            </v-list-group>
          </v-list>
        </template>
      </template>
    </v-list>
  </v-navigation-drawer>
</template>

<style scoped lang="scss">
.logo {
  .v-list-item--nav {
    padding-inline: 8px;
  }
}
.v-list-item--nav {
  padding-inline: 16px;
}
</style>
