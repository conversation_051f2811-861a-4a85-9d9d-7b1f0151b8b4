<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-06-20 16:05:34
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-06-24 09:56:15
 * @FilePath: \ems_manage\resetPwd\.html
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>重置密码</title>
    <link
      rel="stylesheet"
      href="https://cdn.jsdelivr.net/npm/vuetify@3.8.10/dist/vuetify.min.css"
    />
    <script src="https://unpkg.com/vue@3/dist/vue.global.prod.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/vuetify@3.8.10/dist/vuetify.min.js"></script>
    <script src="https://unpkg.com/axios/dist/axios.min.js"></script>
    <style>
      html,
      body,
      #app {
        margin: 0;
        padding: 0;
        width: 100%;
        height: 100vh;
      }
      .content {
        margin: 0 auto;
      }
    </style>
  </head>
  <body>
    <div id="app">
      <div class="content">
        <v-text-field
          label="请求地址"
          variant="solo"
          v-model="requestAddress"
          style="width: 300px"
        ></v-text-field>
        <v-btn @click="resetPwd"> 重置密码 </v-btn>
      </div>

      <v-snackbar v-model="snackbar"> {{ snackbarText }} </v-snackbar>
    </div>

    <script>
      const { createApp, ref } = Vue
      const { createVuetify } = Vuetify

      const vuetify = createVuetify()
      const app = createApp({
        setup() {
          const requestAddress = ref('')
          const snackbar = ref(false)
          const snackbarText = ref('')
          const resetPwd = async () => {
            if (requestAddress.value === '') {
              snackbar.value = true
              snackbarText.value = '请先输入请求地址'
              return
            }
            let api = `${requestAddress.value}/boxihrmler4545hh45rggagj`
            const secretKeyOptions = [
              {
                key: 'GetOverhere',
                value: 'jgn44415df@#%@3'
              },
              {
                key: 'AreYouAGoodBoy',
                value: 'ugh~GetOff!'
              },
              {
                key: 'Reconnect',
                value: 'Back2Initialization'
              },
              {
                key: 'CocoboloDesk',
                value: 'OneDayYouWill_GetIt!'
              }
            ]
            for (let i = 0; i < secretKeyOptions.length; i++) {
              try {
                await axios.put(api, {
                  [key]: value
                })
              } catch (error) {
                snackbar.value = true
                snackbarText.value = key + ': ' + error
              }
            }
          }
          return {
            resetPwd,
            snackbar,
            snackbarText,
            requestAddress
          }
        }
      })
      app.use(vuetify).mount('#app')
    </script>
  </body>
</html>
