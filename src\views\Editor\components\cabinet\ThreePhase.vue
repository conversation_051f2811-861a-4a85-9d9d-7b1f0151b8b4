<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-23 09:55:27
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String // #C4EBAD
})
</script>

<template>
  <svg
    t="1729648528838"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="23623"
    width="200"
    height="200"
  >
    <path
      d="M293.546667 1020.586667c-77.653333 0-151.04-30.72-205.653334-85.333334-54.613333-54.613333-84.48-128-83.626666-204.8 0-77.653333 30.72-150.186667 85.333333-204.8 54.613333-54.613333 126.293333-84.48 203.093333-84.48h1.706667c159.573333 0 289.28 129.706667 289.28 289.28 0 158.72-129.706667 289.28-290.133333 290.133334z m-0.853334-562.346667c-72.533333 0-139.946667 28.16-191.146666 79.36-51.2 51.2-80.213333 120.32-80.213334 192.853333 0 72.533333 27.306667 140.8 79.36 192.853334 51.2 52.053333 120.32 80.213333 192.853334 80.213333 150.186667-0.853333 273.066667-123.733333 273.066666-273.066667 0-150.186667-122.026667-272.213333-272.213333-272.213333h-1.706667z"
      p-id="23624"
      :fill="props.fill"
    ></path>
    <path
      d="M512 583.68c-77.653333 0-150.186667-30.72-204.8-86.186667-54.613333-54.613333-84.48-128-84.48-204.8C223.573333 133.12 354.133333 3.413333 512.853333 3.413333 672.426667 3.413333 802.133333 133.973333 802.133333 293.546667c0 158.72-129.706667 289.28-288.426666 290.133333H512z m0-17.066667h1.706667c149.333333-0.853333 271.36-123.733333 271.36-273.066666 0-150.186667-122.026667-273.066667-272.213334-273.066667-149.333333 0-272.213333 122.026667-273.066666 272.213333 0 72.533333 28.16 141.653333 79.36 192.853334 51.2 52.053333 119.466667 81.066667 192.853333 81.066666z"
      p-id="23625"
      :fill="props.fill"
    ></path>
    <path
      d="M727.893333 1020.586667c-160.426667 0-290.133333-130.56-290.133333-290.133334s130.56-289.28 290.133333-289.28 289.28 130.56 289.28 290.133334-129.706667 289.28-289.28 289.28z m0.853334-562.346667c-150.186667 0-273.066667 122.026667-273.066667 272.213333 0 150.186667 122.026667 273.066667 272.213333 273.066667 150.186667 0 273.066667-122.026667 273.066667-272.213333-0.853333-150.186667-122.88-273.066667-272.213333-273.066667z"
      p-id="23626"
      :fill="props.fill"
    ></path>
  </svg>
</template>
