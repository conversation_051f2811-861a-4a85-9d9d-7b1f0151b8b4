<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-23 09:57:34
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String // #C4EBAD
})
</script>

<template>
  <svg
    t="1729648644943"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="23775"
    width="200"
    height="200"
  >
    <path
      d="M288.026285 256.027428a31.962287 31.962287 0 0 0-32.035428 32.035427v447.91086a31.962287 31.962287 0 1 0 63.997715 0V288.135995a31.962287 31.962287 0 0 0-32.035427-32.035427zM511.981715 256.027428a31.962287 31.962287 0 0 0-32.035427 32.035427v447.91086a31.962287 31.962287 0 1 0 63.997714 0V288.135995A31.962287 31.962287 0 0 0 511.981715 256.027428z m224.02857 0a31.962287 31.962287 0 0 0-32.035427 32.035427v447.91086a31.962287 31.962287 0 1 0 63.997714 0V288.135995a31.962287 31.962287 0 0 0-32.035427-32.035427z"
      fill="#000000"
      p-id="23776"
      :fill="props.fill"
    ></path>
    <path
      d="M959.965716 256.027428V128.031999a64.070855 64.070855 0 0 0-63.997715-63.997715h-63.997714V31.998857a31.962287 31.962287 0 1 0-63.997715 0v32.035427H639.977144V31.998857a31.962287 31.962287 0 1 0-63.997715 0v32.035427h-127.995428V31.998857a31.962287 31.962287 0 1 0-63.997715 0v32.035427H255.990857V31.998857a31.962287 31.962287 0 1 0-63.997714 0v32.035427H127.995429a64.070855 64.070855 0 0 0-63.997715 63.997715V256.027428A64.070855 64.070855 0 0 0 0 320.025142v383.986286c0 35.326738 28.670976 63.997714 63.997714 63.997715v127.995428c0 35.326738 28.670976 63.997714 63.997715 63.997715h63.997714c0 35.326738 28.670976 63.997714 63.997714 63.997714h191.993144A64.070855 64.070855 0 0 0 511.981715 960.002286h63.997714c0 35.326738 28.670976 63.997714 63.997715 63.997714h191.993143a64.070855 64.070855 0 0 0 63.997714-63.997714 64.070855 64.070855 0 0 0 63.997715-63.997715V768.009143A64.070855 64.070855 0 0 0 1023.96343 704.011428v-383.986286a64.070855 64.070855 0 0 0-63.997714-63.997714z m0 447.984h-63.997715v191.993143h-63.997714v63.997715H639.977144v-63.997715H447.984001v63.997715H255.990857v-63.997715H127.995429V704.011428H63.997714v-383.986286h63.997715V128.031999h767.972572v191.993143h63.997715v383.986286z"
      fill="#000000"
      p-id="23777"
      :fill="props.fill"
    ></path>
  </svg>
</template>
