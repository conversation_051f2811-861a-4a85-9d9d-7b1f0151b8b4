/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-02 15:30:12
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-22 14:46:43
 * @FilePath: \ems_manage\src\locale\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createI18n } from 'vue-i18n'
import { en, zhHans } from 'vuetify/locale'
import enCustom from './en'
import zhHansCustom from './zhHans'

const messages = {
  en: {
    ...enCustom,
    $vuetify: {
      ...en
    },
  },
  zhHans: {
    ...zhHansCustom,
    $vuetify: {
      ...zhHans
    },
  },
}

export const i18n = createI18n({
  legacy: false, // Vuetify does not support the legacy mode of vue-i18n
  locale: 'en',
  fallbackLocale: 'en',
  messages,
})

export const initLocale = (lang) => {
  let lang1 = lang ? lang : localStorage.getItem('lang')
  let lang2 = lang1 ? lang1 : 'en'
  i18n.global.locale.value = lang2
}

export const langArr = [
  {
    value: 'zhHans',
    title: '简体中文'
  },
  {
    value: 'en',
    title: 'English'
  }
]
