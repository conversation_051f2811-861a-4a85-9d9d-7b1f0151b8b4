<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-10-21 20:02:37
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-23 16:27:17
 * @FilePath: \ems_manage\src\views\Editor\components\cabinet\deviceName.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
// 文字的内容决定了长度 所以没办法预先定义中心点 导致连线有偏移
const props = defineProps({
  fontFamily: {
    type: String,
    default: ''
  },
  fontSize: {
    type: Number,
    default: 15
  },
  text: {
    type: String,
    default: ''
  },
  fill: {
    type: String,
    default: ''
  }
})
</script>

<template>
  <svg xmlns="http://www.w3.org/2000/svg" version="1.1">
    <text
      x="0"
      y="55"
      :font-family="props.fontFamily"
      :font-size="props.fontSize"
      :fill="props.fill"
    >
      {{ props.text }}
    </text>
  </svg>
</template>
