<!--
 * HMI优化输入框组件
 * 针对10寸触摸屏优化的输入框组件
-->
<template>
  <v-text-field
    :class="[
      'hmi-input',
      'hmi-touch-friendly',
      size === 'small' ? 'hmi-input-small' : '',
      size === 'large' ? 'hmi-input-large' : '',
      readonly ? 'hmi-input-readonly' : ''
    ]"
    :model-value="modelValue"
    :label="label"
    :placeholder="placeholder"
    :type="type"
    :readonly="readonly"
    :disabled="disabled"
    :required="required"
    :rules="rules"
    :error-messages="errorMessages"
    :hint="hint"
    :persistent-hint="persistentHint"
    :prepend-icon="prependIcon"
    :append-icon="appendIcon"
    :prepend-inner-icon="prependInnerIcon"
    :append-inner-icon="appendInnerIcon"
    :variant="variant"
    :density="density"
    :clearable="clearable"
    v-bind="$attrs"
    @update:model-value="handleInput"
    @focus="handleFocus"
    @blur="handleBlur"
    @click:prepend="$emit('click:prepend', $event)"
    @click:append="$emit('click:append', $event)"
    @click:prepend-inner="$emit('click:prepend-inner', $event)"
    @click:append-inner="$emit('click:append-inner', $event)"
    @click:clear="$emit('click:clear', $event)"
  >
    <template v-if="$slots.prepend" #prepend>
      <slot name="prepend"></slot>
    </template>
    <template v-if="$slots.append" #append>
      <slot name="append"></slot>
    </template>
    <template v-if="$slots['prepend-inner']" #prepend-inner>
      <slot name="prepend-inner"></slot>
    </template>
    <template v-if="$slots['append-inner']" #append-inner>
      <slot name="append-inner"></slot>
    </template>
  </v-text-field>
</template>

<script setup>
import { computed } from 'vue'
import { useDisplay } from 'vuetify'

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'text'
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  readonly: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  rules: {
    type: Array,
    default: () => []
  },
  errorMessages: {
    type: [String, Array],
    default: ''
  },
  hint: {
    type: String,
    default: ''
  },
  persistentHint: {
    type: Boolean,
    default: false
  },
  prependIcon: {
    type: String,
    default: ''
  },
  appendIcon: {
    type: String,
    default: ''
  },
  prependInnerIcon: {
    type: String,
    default: ''
  },
  appendInnerIcon: {
    type: String,
    default: ''
  },
  variant: {
    type: String,
    default: 'outlined'
  },
  density: {
    type: String,
    default: 'comfortable'
  },
  clearable: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits([
  'update:modelValue',
  'focus',
  'blur',
  'click:prepend',
  'click:append',
  'click:prepend-inner',
  'click:append-inner',
  'click:clear'
])

const { mobile } = useDisplay()

const handleInput = (value) => {
  emit('update:modelValue', value)
}

const handleFocus = (event) => {
  emit('focus', event)
}

const handleBlur = (event) => {
  emit('blur', event)
}
</script>

<style lang="scss" scoped>
.hmi-input {
  /* 基础样式 */
  :deep(.v-field) {
    min-height: 56px !important;
    border-radius: 8px !important;
    
    .v-field__input {
      font-size: 16px !important;
      font-weight: 500 !important;
      padding: 0 16px !important;
      min-height: 56px !important;
    }
    
    .v-field__outline {
      --v-field-border-width: 2px !important;
    }
    
    &.v-field--focused {
      .v-field__outline {
        --v-field-border-width: 3px !important;
      }
    }
  }
  
  /* 标签样式 */
  :deep(.v-label) {
    font-size: 16px !important;
    font-weight: 500 !important;
    color: rgba(0, 0, 0, 0.7) !important;
    
    &.v-field-label--floating {
      font-size: 14px !important;
      font-weight: 600 !important;
      color: var(--v-theme-primary) !important;
    }
  }
  
  /* 图标样式 */
  :deep(.v-icon) {
    font-size: 22px !important;
    color: rgba(0, 0, 0, 0.6) !important;
  }
  
  /* 提示文本 */
  :deep(.v-messages) {
    font-size: 14px !important;
    font-weight: 500 !important;
    margin-top: 4px !important;
  }
  
  /* 错误状态 */
  &.v-input--error {
    :deep(.v-field__outline) {
      color: var(--v-theme-error) !important;
    }
    
    :deep(.v-messages) {
      color: var(--v-theme-error) !important;
    }
  }
}

/* 尺寸变体 */
.hmi-input-small {
  :deep(.v-field) {
    min-height: 48px !important;
    
    .v-field__input {
      font-size: 14px !important;
      min-height: 48px !important;
      padding: 0 12px !important;
    }
  }
  
  :deep(.v-label) {
    font-size: 14px !important;
    
    &.v-field-label--floating {
      font-size: 12px !important;
    }
  }
  
  :deep(.v-icon) {
    font-size: 20px !important;
  }
}

.hmi-input-large {
  :deep(.v-field) {
    min-height: 64px !important;
    
    .v-field__input {
      font-size: 18px !important;
      min-height: 64px !important;
      padding: 0 20px !important;
    }
  }
  
  :deep(.v-label) {
    font-size: 18px !important;
    
    &.v-field-label--floating {
      font-size: 16px !important;
    }
  }
  
  :deep(.v-icon) {
    font-size: 24px !important;
  }
}

/* 只读状态 */
.hmi-input-readonly {
  :deep(.v-field) {
    background-color: rgba(0, 0, 0, 0.04) !important;
    
    .v-field__input {
      color: rgba(0, 0, 0, 0.8) !important;
    }
  }
}

/* 移动端优化 */
@media screen and (max-width: 1024px) {
  .hmi-input {
    :deep(.v-field) {
      min-height: 52px !important;
      
      .v-field__input {
        font-size: 15px !important;
        min-height: 52px !important;
        padding: 0 14px !important;
      }
    }
    
    :deep(.v-label) {
      font-size: 15px !important;
      
      &.v-field-label--floating {
        font-size: 13px !important;
      }
    }
    
    :deep(.v-icon) {
      font-size: 20px !important;
    }
  }
  
  .hmi-input-small {
    :deep(.v-field) {
      min-height: 44px !important;
      
      .v-field__input {
        font-size: 13px !important;
        min-height: 44px !important;
      }
    }
  }
  
  .hmi-input-large {
    :deep(.v-field) {
      min-height: 60px !important;
      
      .v-field__input {
        font-size: 17px !important;
        min-height: 60px !important;
      }
    }
  }
}
</style>
