<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-10 17:56:39
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String // #C4EBAD
})
</script>

<template>
  <svg
    t="1729648259704"
    class="icon"
    viewBox="0 0 1024 1024"
    version="1.1"
    xmlns="http://www.w3.org/2000/svg"
    p-id="21683"
    width="200"
    height="200"
  >
    <path
      d="M651.471042 158.768544H524.023366V18.035049h-24.046732v140.733495h-127.447676v705.050166H499.976634V1003.951038h24.046732v-140.132328h127.447676z m-24.046731 681.003435h-230.848622V182.815276h230.848622z"
      p-id="21684"
      :fill="props.fill"
    ></path>
  </svg>
</template>
