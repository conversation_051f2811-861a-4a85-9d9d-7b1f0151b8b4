<script setup>
import { ref, toRefs, getCurrentInstance } from 'vue'
import { useGlobalStore } from '@/store/global'
import { useI18n } from 'vue-i18n'
import {
  databaseTables,
  deleteDatabaseTable,
  databaseState,
  rebootEMS,
  rebootEMSs,
  getStorageCycle,
  setStorageCycle
} from '@/api/deviceConfig'

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const headers = ref([
  {
    title: '#',
    key: 'id',
    sortable: false
  },
  {
    title: t('表名'),
    key: 'tableName',
    sortable: false
  },
  { title: t('操作'), key: 'action', sortable: false }
])
const tableData = ref([])
const getList = async () => {
  const res = await databaseTables()
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  tableData.value = res.data.tables.map((item, index) => {
    return {
      id: index,
      tableName: item
    }
  })
}
getList()
const state = ref()
const getState = async () => {
  const res = await databaseState()
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  state.value = res.data.state
}
getState()

const handleRemoveClick = async (item) => {
  tableName.value = item.tableName
  removeDialog.value = true
}
const removeDialog = ref(false)
const handleRemoveCancelClick = () => {
  removeDialog.value = false
}
const tableName = ref()
const handleRemoveConfirm = async () => {
  await removeFn()
  // removeDialog.value = false
  // pwdDialog.value = true
}
const removeFn = async () => {
  const res = await deleteDatabaseTable({ tableName: tableName.value })
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  snackbar.value = true
  snackbarText.value = t('删除成功')
  removeDialog.value = false
  pwdDialog.value = false
  getList()
}
// 重启
const handleRestartClick = () => {
  password.value = undefined
  tableName.value = undefined
  pwdDialog.value = true
}
// 密码弹框
const pwdDialog = ref(false)
const password = ref()
const eye = ref(false)
const handleEyeClick = () => (eye.value = !eye.value)
const handlePwdCancelClick = () => {
  pwdDialog.value = false
}
const handlePwdConfirm = async () => {
  if (!password.value) {
    snackbar.value = true
    snackbarText.value = t('请输入密码')
    return
  }
  const res = await rebootEMS(JSON.stringify({ password: password.value }))
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  try {
    // if (tableName.value) await removeFn()
    const res2 = await rebootEMSs({ name: res.data })
    if (res2.code !== 201) {
      snackbar.value = true
      snackbarText.value = res2.msg
      return
    }
    pwdDialog.value = false
    // if (!tableName.value) {
    snackbar.value = true
    snackbarText.value = t('重启成功')
    // }
  } catch (error) {
    console.log(error)
    pwdDialog.value = false
  }
}

/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value) => {
  if (isShowKeyboard.value) return
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  password.value = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}

/**
 * 存储周期
 */
const storageCycle = ref(0)
const getStorageCycleFn = async () => {
  const res = await getStorageCycle()
  if (res.code !== 200) {
    snackbar.value = true
    snackbarText.value = res.msg
    return
  }
  storageCycle.value = res.data.syncDataInterval
}
getStorageCycleFn()
const storageCycleDialog = ref(false)
const handleSetStorageCycleClick = () => {
  storageCycleDialog.value = true
}
const handleStorageCycleCancelClick = () => {
  storageCycleDialog.value = false
}
const handleStorageCycleConfirm = () => {
  if (!storageCycle.value) {
    snackbar.value = true
    snackbarText.value = t('请输入存储周期')
    return
  }
  setStorageCycle({ syncDataInterval: Number(storageCycle.value) })
    .then(() => {
      snackbar.value = true
      snackbarText.value = t('设置成功')
      storageCycleDialog.value = false
      getStorageCycleFn()
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
</script>

<template>
  <div class="w-100 px-2">
    <v-card class="pa-4 w-100 rounded-lg no-scrollbar" elevation="4">
      <div class="mb-4">
        <v-btn
          height="48px"
          :color="state == '1' ? '#28c79c' : '#ff6b6b'"
          prepend-icon="mdi-circle-medium"
          class="mr-2"
          style="color: #fff !important"
          >{{ state == '1' ? $t('已连接') : $t('未连接') }}</v-btn
        >
        <v-btn
          height="48px"
          color="primary"
          @click="handleRestartClick"
          class="mr-2"
          >{{ $t('重启EMS程序') }}</v-btn
        >
        <v-btn height="48px" color="primary" @click="handleSetStorageCycleClick"
          >{{ $t('存储周期') }}: {{ storageCycle }} s</v-btn
        >
      </div>
      <v-data-table
        :headers="headers"
        :items="tableData"
        hide-default-footer
        items-per-page="-1"
      >
        <template v-slot:item.action="{ item }">
          <v-btn class="ml-2" @click="handleRemoveClick(item)">{{
            $t('删除')
          }}</v-btn>
        </template>
      </v-data-table>

      <v-dialog v-model="removeDialog" width="auto">
        <v-card width="540" class="pa-4 rounded-lg">
          <v-card-title>{{ $t('系统提示') }}</v-card-title>
          <v-card-text>{{ $t('是否确认删除该表？') }}</v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="grey-darken-1"
              variant="text"
              @click="handleRemoveCancelClick"
            >
              {{ $t('取消') }}
            </v-btn>
            <v-btn color="primary" variant="text" @click="handleRemoveConfirm">
              {{ $t('确定') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="storageCycleDialog" width="auto">
        <v-card width="540" class="pa-4 rounded-lg">
          <v-card-title>{{ $t('设置存储周期') }}</v-card-title>
          <v-card-text class="my-0">
            <div class="mb-2">{{ $t('请输入数字（s）') }}</div>
            <v-text-field
              :rules="[(v) => !!v || $t('必填')]"
              variant="outlined"
              class="mb-2"
              @click:control="handleShow($event, storageCycle)"
              hide-details
              v-model="storageCycle"
              type="number"
            ></v-text-field>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="grey-darken-1"
              variant="text"
              @click="handleStorageCycleCancelClick"
            >
              {{ $t('取消') }}
            </v-btn>
            <v-btn
              color="primary"
              variant="text"
              @click="handleStorageCycleConfirm"
            >
              {{ $t('确定') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>

      <v-dialog v-model="pwdDialog" width="auto">
        <v-card width="540" class="pa-4 rounded-lg">
          <v-card-title>{{ $t('系统提示') }}</v-card-title>
          <v-card-text class="my-0">
            <div class="mb-2">{{ $t('请输入密码') }}</div>
            <v-text-field
              v-model="password"
              :rules="[(v) => !!v || $t('密码必填')]"
              variant="outlined"
              class="mb-2"
              :type="eye ? 'text' : 'password'"
              :append-inner-icon="eye ? 'mdi-eye' : 'mdi-eye-closed'"
              @click:appendInner.stop="handleEyeClick()"
              @click:control="handleShow($event, password)"
              hide-details
            ></v-text-field>
          </v-card-text>
          <v-card-actions>
            <v-spacer></v-spacer>
            <v-btn
              color="grey-darken-1"
              variant="text"
              @click="handlePwdCancelClick"
            >
              {{ $t('取消') }}
            </v-btn>
            <v-btn color="primary" variant="text" @click="handlePwdConfirm">
              {{ $t('确定') }}
            </v-btn>
          </v-card-actions>
        </v-card>
      </v-dialog>
    </v-card>
  </div>
</template>

<style lang="scss" scoped></style>
