/**
 * HMI组件库
 * 针对10寸触摸屏优化的组件集合
 */

import HmiButton from './HmiButton.vue'
import HmiInput from './HmiInput.vue'
import HmiCard from './HmiCard.vue'

// 组件列表
const components = {
  HmiButton,
  HmiInput,
  HmiCard
}

// 安装函数
const install = (app) => {
  Object.keys(components).forEach(key => {
    app.component(key, components[key])
  })
}

// 支持按需导入
export {
  HmiButton,
  HmiInput,
  HmiCard
}

// 支持全量导入
export default {
  install,
  ...components
}
