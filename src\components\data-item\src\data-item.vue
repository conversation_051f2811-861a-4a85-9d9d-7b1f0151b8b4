<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-01-17 11:01:45
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-08-26 16:29:24
 * @FilePath: \ems_manage\src\components\data-item\src\data-item.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { computed, ref, toRefs } from 'vue'
import { useDeviceStore } from '@/store/module/device'
import { useI18n } from 'vue-i18n'
import { writePointData } from '@/api/device'
import { useGlobalStore } from '@/store/global'
import { useUserStore } from '@/store/module/user'
import { useLocale } from 'vuetify'

const prop = defineProps({
  item: {
    type: Object,
    default: () => ({
      units: '',
      value: '',
      point_name: ''
    })
  },
  isStatus: {
    type: Boolean,
    default: false
  },
  isTime: {
    type: Boolean,
    default: false
  },
  deviceName: {
    type: String
  }
})

const { current } = useLocale()
const { t } = useI18n()
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const { userInfo } = toRefs(useUserStore())
const isShowEdit = computed(() => userInfo.value.permission_level == 4)

const getStatusTypeFn = (status) => {
  return useDeviceStore().getStatusTypeFn(status)
}

const dialog = ref(false)
const handleItemClick = (status) => {
  if (!status) return
  dialog.value = true
}
const borderStyle = computed(() => {
  return (hover, status) => {
    if (hover) {
      return status == 1 ? '1px solid #00000080' : '1px solid #e9e9e9'
    } else {
      return status == 1 ? '1px solid #fcca1e' : '1px solid #e9e9e9'
    }
  }
})

/**
 * 修改
 */
const editLoading = ref(false)
const editValue = ref()
const editDialog = ref(false)
const valueMapOptions = ref([])
const toggle = ref()
const isShowSelect = ref(false)
const handleEditClick = () => {
  if (prop.item.valueMap) {
    isShowSelect.value = true
    toggle.value = 1
  }
  valueMapOptions.value = prop.item.valueMap
    ? prop.item.valueMap.map((item, index) => {
        return {
          title: item,
          value: index
        }
      })
    : []
  let value = parseFloat(prop.item.value)
  editValue.value = isNaN(value) ? '' : value
  editDialog.value = true
}
const handleSaveEdit = async () => {
  try {
    editLoading.value = true
    const res = await writePointData({
      point_id: prop.item.point_id,
      value: parseFloat(editValue.value),
      device_name: prop.deviceName
    })
    if (res.code == 200) {
      editLoading.value = false
      snackbar.value = true
      snackbarText.value = res.msg
    }
    snackbar.value = true
    snackbarText.value = t('下发成功')
    editLoading.value = false
    editDialog.value = false
  } catch (error) {
    editLoading.value = false
    snackbar.value = true
    snackbarText.value = error
  }
}
</script>

<template>
  <v-hover>
    <template v-slot:default="{ isHovering, props }">
      <v-card
        variant="tonal"
        class="py-3 px-3"
        v-bind="props"
        :elevation="isHovering ? 4 : 0"
        color="grey-darken-1"
        :style="{
          border: borderStyle(isHovering, prop.item.status)
        }"
        @click="handleItemClick(prop.item.status)"
      >
        <template v-if="isHovering && prop.item.status == 1">
          <v-tooltip :text="$t('点击显示更多')" location="top">
            <template v-slot:activator="{ props }">
              <div class="more-mask" v-bind="props">
                <img
                  src="../../../assets/img/more.svg"
                  alt=""
                  style="width: 80px"
                />
              </div> </template
          ></v-tooltip>
        </template>
        <div
          class="flex align-center justify-center"
          :style="{
            color: '#000',
            height: '40px'
          }"
        >
          <template v-if="prop.isStatus">
            <img
              src="../../../assets/img/icon-red.png"
              alt=""
              style="width: 20px; height: 20px"
              class="mr-2"
              v-if="getStatusTypeFn(item.value) == 2"
            />
            <img
              src="../../../assets/img/icon-enable.png"
              alt=""
              style="width: 20px; height: 20px"
              class="mr-2"
              v-else-if="getStatusTypeFn(item.value) == 1"
            />
            <img
              src="../../../assets/img/circle.png"
              alt=""
              style="width: 20px; height: 20px"
              class="mr-2"
              v-else
            />
          </template>
          <template v-else>
            <img
              src="../../../assets/img/circle.png"
              alt=""
              style="width: 20px; height: 20px"
              class="mr-2"
            />
          </template>
          <span>{{ prop.item.point_name }}</span>
          <v-icon
            v-if="prop.item?.writable == 1 && isShowEdit"
            size="small"
            icon="mdi-pencil"
            color="primary"
            class="ml-1 edit-icon"
            @click.stop="handleEditClick()"
          />
        </div>
        <div
          class="font-600 my-1 flex justify-center overflow-hidden"
          :style="{
            color: '#ffaa43',
            height: '30px',
            lineHeight: '30px'
          }"
        >
          <div v-html="prop.item.value"></div>
          <span class="ml-1">{{ prop.item.units }}</span>
          <div v-if="prop.item.status == 1">···</div>
        </div>
        <div
          class="w-100"
          :style="{
            color: '#000',
            textAlign: 'center',
            height: '30px',
            lineHeight: '30px'
          }"
          v-if="isTime"
        >
          {{ prop.item.time ? prop.item.time : '--' }}
        </div>
      </v-card>
    </template>
  </v-hover>

  <v-dialog v-model="dialog" width="auto">
    <v-card
      class="py-4 px-4 flex flex-column align-center"
      color="#fff"
      width="440"
    >
      <div
        class="flex align-center"
        :style="{
          color: '#000'
        }"
      >
        <template v-if="prop.isStatus">
          <img
            src="../../../assets/img/icon-red.png"
            alt=""
            style="width: 20px; height: 20px"
            class="mr-2"
            v-if="getStatusTypeFn(item.value) == 2"
          />
          <img
            src="../../../assets/img/icon-enable.png"
            alt=""
            style="width: 20px; height: 20px"
            class="mr-2"
            v-else-if="getStatusTypeFn(item.value) == 1"
          />
          <img
            src="../../../assets/img/circle.png"
            alt=""
            style="width: 20px; height: 20px"
            class="mr-2"
            v-else
          />
        </template>
        <template v-else>
          <img
            src="../../../assets/img/circle.png"
            alt=""
            style="width: 20px; height: 20px"
            class="mr-2"
          />
        </template>
        <span>{{ prop.item.point_name }}</span>
      </div>
      <div
        class="font-600 my-1 flex"
        :style="{
          color: '#ffaa43'
        }"
      >
        <div v-html="prop.item.value"></div>
        {{ prop.item.units }}
      </div>
      <div
        :style="{
          color: '#000'
        }"
      >
        {{ prop.item.time ? prop.item.time : '--' }}
      </div>
    </v-card>
  </v-dialog>

  <v-dialog v-model="editDialog" width="500">
    <v-card class="pa-4 rounded-lg">
      <v-card-title>{{ $t('参数下发') }}</v-card-title>
      <v-card-text>
        <v-btn-toggle
          v-model="toggle"
          variant="outlined"
          divided
          v-if="isShowSelect"
          class="mb-6"
        >
          <v-btn>{{ $t('数值') }}</v-btn>
          <v-btn>{{ $t('选择') }}</v-btn>
        </v-btn-toggle>
        <v-select
          v-model="editValue"
          item-value="value"
          clearable
          :label="prop.item.point_name"
          :items="valueMapOptions"
          variant="outlined"
          hide-details
          v-if="toggle == 1"
        />
        <v-text-field
          v-model="editValue"
          :label="prop.item.point_name"
          :suffix="prop.item.units"
          variant="outlined"
          type="Number"
          hide-details
          v-else
        />
      </v-card-text>
      <v-card-actions>
        <v-spacer />
        <v-btn @click="editDialog = false">{{ $t('取消') }}</v-btn>
        <v-btn color="primary" :loading="editLoading" @click="handleSaveEdit">{{
          $t('保存')
        }}</v-btn>
      </v-card-actions>
    </v-card>
  </v-dialog>
</template>

<style lang="scss" scoped>
.more-mask {
  background: #00000080;
  display: flex;
  justify-content: center;
  align-items: center;
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.edit-icon {
  cursor: pointer;
  opacity: 0.7;

  &:hover {
    opacity: 1;
    transform: scale(1.1);
    transition: all 0.2s ease;
  }
}
</style>
