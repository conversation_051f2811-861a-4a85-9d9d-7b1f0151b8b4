/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-08 18:39:38
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-09-18 14:30:16
 * @FilePath: \ems_manage\src\store\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createPinia } from 'pinia'
import piniaPluginPersistedstate from 'pinia-plugin-persistedstate'

const pinia = createPinia()
pinia.use(piniaPluginPersistedstate)

export default pinia