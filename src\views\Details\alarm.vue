<script setup>
import { ref, toRefs, nextTick } from 'vue'
import dayjs from '@/utils/date'
import { useDeviceStore } from '@/store/module/device'
import { useI18n } from 'vue-i18n'
import { useGlobalStore } from '@/store/global'
import { useAllowedDate } from '@/hook/useAllowedDate'
import { setLeftWidth } from '@/utils'
import { debounce } from 'lodash-es'

import { ElDatePicker } from 'element-plus'

const { allowedDates } = useAllowedDate()
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const { pageInfo, faultData, faultQueryInfo } = toRefs(useDeviceStore())

const { t } = useI18n()
const search = ref('')
const headers = ref([
  {
    key: 'device_name',
    title: t('设备名称'),
    sortable: false,
    align: 'center'
  },
  {
    key: 'module_name',
    title: t('告警对象'),
    sortable: false,
    align: 'center'
  },
  { key: 'content', title: t('告警名称'), sortable: false, align: 'center' },
  {
    key: 'alarm_level',
    title: t('告警等级'),
    sortable: false,
    align: 'center'
  },
  { key: 'start_time', title: t('发生时间'), sortable: false, align: 'center' },
  { key: 'end_time', title: t('结束时间'), sortable: false, align: 'center' },
  { title: t('操作'), key: 'actions', sortable: false, align: 'center' }
])

const date = ref([])
const isShowDate = ref(false)
date.value = faultQueryInfo.value.date ? [faultQueryInfo.value.date] : []
const handleDateChange = async (e) => {
  faultQueryInfo.value.date = dayjs(e).format('YYYY-MM-DD')
  isShowDate.value = false
  loading.value = true
  try {
    await useDeviceStore().getFaultDataFn()
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}
const handleClearClick = async () => {
  faultQueryInfo.value.date = ''
  loading.value = true
  try {
    await useDeviceStore().getFaultDataFn()
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}

const loading = ref(false)
const handleChangeSize = async ({ page, itemsPerPage }) => {
  pageInfo.value.pageSize = itemsPerPage
  pageInfo.value.pageIndex = page
  loading.value = true
  try {
    await useDeviceStore().getFaultDataFn({
      pageSize: itemsPerPage,
      pageIndex: page
    })
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}
const getData = async () => {
  loading.value = true
  try {
    await useDeviceStore().getFaultDataFn({
      pageSize: pageInfo.value.pageSize,
      pageIndex: pageInfo.value.pageIndex
    })
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}
const gradeOptions = ref([
  { value: 1, title: t('等级一') },
  { value: 2, title: t('等级二') },
  { value: 3, title: t('等级三') }
])
const statusOptions = ref([
  { value: 0, title: t('全部') },
  { value: 1, title: t('未处理') },
  { value: 2, title: t('已处理') }
])
const handleSearchChange = async () => {
  loading.value = true
  try {
    await useDeviceStore().getFaultDataFn({
      pageSize: pageInfo.value.pageSize,
      pageIndex: pageInfo.value.pageIndex
    })
    loading.value = false
  } catch (error) {
    console.error(error)
    loading.value = false
  }
}
const refreshChange = debounce(handleSearchChange, 200)

/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value) => {
  if (isShowKeyboard.value) return
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  search.value = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}

/**
 * 录波
 */
const exportRecordLoading = ref(false)
const handleExportRecordClick = async (fileName) => {
  exportRecordLoading.value = true
  try {
    const res = await useDeviceStore().exportAlarmRecordFileFn({ fileName })
    if (res?.msg) {
      snackbar.value = true
      snackbarText.value = res?.msg
      return
    }
    exportRecordLoading.value = false
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    exportRecordLoading.value = false
  }
}

const getLevelText = (level) => {
  if (level === '1') {
    return t('等级一')
  } else if (level === '2') {
    return t('等级二')
  } else if (level === '3') {
    return t('等级三')
  }
}

/**
 * 导出
 */
const dialog = ref(false)
const form = ref({})
const handleExportClick = () => {
  let nowDate = dayjs(new Date())
  form.value = {
    start: nowDate.subtract(7, 'day').format('YYYY-MM-DD'),
    end: nowDate.format('YYYY-MM-DD'),
    formatDate: [
      nowDate.subtract(7, 'day').format('YYYY-MM-DD'),
      nowDate.format('YYYY-MM-DD')
    ]
  }
  dialog.value = true
  nextTick(() => {
    setLeftWidth('dia-left')
  })
}
const handleCancelDiaClick = () => {
  let nowDate = dayjs(new Date())
  form.value = {
    start: nowDate.subtract(7, 'day').format('YYYY-MM-DD'),
    end: nowDate.format('YYYY-MM-DD'),
    formatDate: [
      nowDate.subtract(7, 'day').format('YYYY-MM-DD'),
      nowDate.format('YYYY-MM-DD')
    ]
  }
  dialog.value = false
}
const exportLoading = ref(false)
const handleConfirmDiaClick = () => {
  if (!form.value.start && !form.value.end) {
    snackbar.value = true
    snackbarText.value = t('请选择日期')
    return
  }
  if (!checkDateRange(form.value.start, form.value.end)) return
  exportLoading.value = true
  useDeviceStore()
    .exportAlarmDataFn({
      start: form.value.start,
      end: form.value.end
    })
    .then((res) => {
      exportLoading.value = false
      dialog.value = false
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
      exportLoading.value = false
    })
}
const checkDateRange = (start, end) => {
  // 解析输入的日期字符串为 dayjs 对象
  const startDate = dayjs(start)
  const endDate = dayjs(end)

  // 检查结束日期是否在开始日期之前
  if (!startDate.isBefore(end)) {
    snackbar.value = true
    snackbarText.value = t('结束日期必须晚于开始日期')
    return false
  }

  // 计算开始日期加上三个月后的日期
  const threeMonthsLater = startDate.add(3, 'month')

  // 比较结束日期是否在这个范围内
  if (!endDate.isBefore(threeMonthsLater)) {
    snackbar.value = true
    snackbarText.value = t('时间范围超过三个月')
    return false
  }

  return true
}
const handleDayExportChange = (e) => {
  if (!e) return
  form.value.start = dayjs(e[0]).format('YYYY-MM-DD')
  form.value.end = dayjs(e[1]).format('YYYY-MM-DD')
}
const shortcuts = [
  {
    text: t('最近一周'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 7)
      return [start, end]
    }
  },
  {
    text: t('最近一个月'),
    value: () => {
      const end = new Date()
      const start = new Date()
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30)
      return [start, end]
    }
  }
]
const disabledDate = (date) => {
  return date.getTime() > new Date().getTime()
}

defineExpose({
  getData
})
</script>

<template>
  <div class="pa-4">
    <div class="flex align-center mb-2">
      <v-row>
        <v-col cols="4">
          <v-menu
            v-model="isShowDate"
            location="bottom"
            :close-on-content-click="false"
          >
            <template v-slot:activator="{ props }">
              <v-text-field
                v-model="faultQueryInfo.date"
                prepend-inner-icon="mdi-calendar-range"
                :label="$t('日期')"
                variant="outlined"
                hide-details
                single-line
                :clearable="true"
                v-bind="props"
                @click:clear="handleClearClick"
              ></v-text-field>
            </template>

            <v-date-picker
              v-model="date"
              show-adjacent-months
              :allowed-dates="allowedDates"
              color="primary"
              elevation="4"
              class="date-picker"
              @update:modelValue="handleDateChange"
            ></v-date-picker>
          </v-menu>
        </v-col>
        <v-col cols="4">
          <v-select
            v-model="faultQueryInfo.level"
            :label="$t('告警等级')"
            clearable
            item-value="value"
            :items="gradeOptions"
            variant="outlined"
            class="w-100 mr-4"
            @update:modelValue="handleSearchChange"
            hide-details
          ></v-select>
        </v-col>
        <v-col cols="4">
          <v-select
            v-model="faultQueryInfo.expiredOrNot"
            :label="$t('告警状态')"
            clearable
            item-value="value"
            :items="statusOptions"
            variant="outlined"
            class="w-100 mr-4"
            @update:modelValue="handleSearchChange"
            hide-details
          ></v-select>
        </v-col>
      </v-row>
      <v-btn
        color="primary"
        class="px-8 ml-4"
        height="50"
        @click="handleExportClick"
        >{{ $t('导出报表') }}</v-btn
      >
      <v-btn
        color="primary"
        class="px-8 ml-4"
        height="50"
        @click="refreshChange"
        prepend-icon="mdi-refresh"
        >{{ $t('刷新') }}</v-btn
      >
    </div>

    <v-data-table-server
      :headers="headers"
      :items="faultData"
      :loading="loading"
      :items-per-page="pageInfo.pageSize"
      :items-length="pageInfo.total"
      @update:options="handleChangeSize"
      :items-per-page-options="[
        { value: 10, title: '10' },
        { value: 25, title: '25' },
        { value: 50, title: '50' },
        { value: 100, title: '100' }
      ]"
      show-current-page
    >
      <template v-slot:loading>
        <v-skeleton-loader type="table-row@10"></v-skeleton-loader>
      </template>
      <template v-slot:item.alarm_level="{ item }">
        <v-chip
          :color="'red'"
          :text="getLevelText(item.alarm_level)"
          class="text-uppercase"
          size="small"
          label
        ></v-chip>
      </template>
      <template v-slot:item.actions="{ item }">
        <v-btn rounded="lg" :disabled="item.end_time ? true : false">{{
          item.end_time ? $t('已处理') : $t('未处理')
        }}</v-btn>
        <v-btn
          class="ml-2 my-1"
          @click="handleExportRecordClick(item.alarm_record_filename)"
          v-if="item.alarm_record_filename"
          :disabled="
            item.alarm_record_filename &&
            item.alarm_record_filename === 'pending'
          "
        >
          <template #prepend v-if="item.alarm_record_filename === 'pending'">
            <v-progress-circular indeterminate :size="20"></v-progress-circular>
          </template>
          {{ $t('故障详情') }}</v-btn
        >
      </template>
    </v-data-table-server>

    <v-dialog v-model="dialog" width="auto">
      <v-card width="640" class="pa-4 rounded-lg">
        <v-card-title class="text-center mb-2">{{
          $t('导出报表')
        }}</v-card-title>
        <div class="flex">
          <div
            class="text-body-1 pb-2 pt-3 pr-4 flex justify-end align-center dia-left"
          >
            {{ $t('选择日期') }}
          </div>
          <v-col cols="9" class="pl-0 pr-2">
            <el-date-picker
              v-model="form.formatDate"
              type="daterange"
              unlink-panels
              range-separator="-"
              :start-placeholder="$t('开始日期')"
              :end-placeholder="$t('结束日期')"
              :shortcuts="shortcuts"
              size="large"
              :clearable="false"
              :disabled-date="disabledDate"
              class="w-100"
              style="height: 56px"
              @change="handleDayExportChange"
            />
          </v-col>
        </div>
        <div class="d-flex justify-center mt-2">
          <v-btn
            class="mt-2 mr-4 px-8"
            height="50"
            @click="handleCancelDiaClick"
            >{{ $t('取消') }}</v-btn
          >
          <v-btn
            class="mt-2 px-8"
            height="50"
            :loading="exportLoading"
            color="primary"
            @click="handleConfirmDiaClick"
            >{{ $t('确定') }}</v-btn
          >
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.el-input__wrapper) {
  height: auto;
  padding: 16px;
  box-shadow: 0 0 0 1px rgba(118, 118, 118, 0.6);
  .el-range__icon {
    color: #000;
    font-size: 16px;
  }
  .el-range-input {
    color: #000;
    height: 26px;
    font-size: 16px;
  }
}
:deep(.el-input__wrapper.is-focus) {
  box-shadow: 0 0 0 2px #000;
}
:deep(.el-input__wrapper:hover) {
  box-shadow: 0 0 0 2px #000;
}
.dia-left {
  flex: 0 0 auto; /* 不可拉伸，宽度不变 */
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容显示出来 */
  text-align: right;
}
</style>
