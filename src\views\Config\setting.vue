<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-25 18:41:41
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-04-24 16:23:33
 * @FilePath: \ems_manage\src\views\Config\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, toRefs, getCurrentInstance } from 'vue'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { ipData } = toRefs(useConfigStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())

const getData = () => {
  useConfigStore()
    .getIpFn()
    .then((res) => {
      form.value = {
        ...ipData.value
      }
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
getData()

const form = ref({
  mode: 'auto',
  name: 'eth0',
  ip: '',
  netmask: '',
  gateway: ''
})
const regex =
  /((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})(\.((2(5[0-5]|[0-4]\d))|[0-1]?\d{1,2})){3}/
const ipRules = ref([
  (v) => !!v || t('IP地址必填'),
  (v) => {
    return regex.test(v) || t('格式不正确')
  }
])
const netmaskRules = ref([
  (v) => !!v || t('子网掩码必填'),
  (v) => regex.test(v) || t('格式不正确')
])
const gatewayRules = ref([
  (v) => !!v || t('网关必填'),
  (v) => regex.test(v) || t('格式不正确')
])
const loading = ref(false)
const submit = async () => {
  const { valid } = await proxy.$refs.formRef.validate()
  if (!valid) return
  loading.value = true
  try {
    const res = await useConfigStore().ipSetFn(JSON.stringify(form.value))
    snackbar.value = true
    snackbarText.value = t('保存成功')
    loading.value = false
    getData()
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
  }
}

/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value, prop) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  form.value[currentInput.value] = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}

const ipTypeList = ref([
  {
    id: 'auto',
    title: t('自动')
  },
  {
    id: 'manual',
    title: t('手动')
  }
])

defineExpose({
  getData
})
</script>

<template>
  <div class="w-100 px-2">
    <v-card class="pa-4 w-100 rounded-lg no-scrollbar" elevation="4">
      <v-form @submit.prevent="submit" ref="formRef">
        <v-row>
          <v-col cols="12" lg="6" md="12" sm="12" xs="12">
            <v-select
              v-model="form.mode"
              item-value="id"
              clearable
              :items="ipTypeList"
              variant="outlined"
              class="w-100 mr-4 mt-2"
              :placeholder="$t('选择类型')"
              :label="$t('选择类型')"
            ></v-select>
          </v-col>
        </v-row>
        <v-row v-if="form.mode == 'manual'">
          <v-col cols="12" lg="6" md="12" sm="12" xs="12">
            <v-text-field
              v-model="form.ip"
              :label="$t('IP地址')"
              variant="outlined"
              append-inner-icon="mdi-ip"
              :rules="ipRules"
              @click:control="handleShow($event, form.ip, 'ip')"
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row v-if="form.mode == 'manual'">
          <v-col cols="12" lg="6" md="12" sm="12" xs="12">
            <v-text-field
              v-model="form.netmask"
              :label="$t('子网掩码')"
              variant="outlined"
              append-inner-icon="mdi-wan"
              :rules="netmaskRules"
              @click:control="handleShow($event, form.netmask, 'netmask')"
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row v-if="form.mode == 'manual'">
          <v-col cols="12" lg="6" md="12" sm="12" xs="12">
            <v-text-field
              v-model="form.gateway"
              :label="$t('网关')"
              variant="outlined"
              append-inner-icon="mdi-router-network"
              :rules="gatewayRules"
              @click:control="handleShow($event, form.gateway, 'gateway')"
            ></v-text-field>
          </v-col>
        </v-row>
        <v-row>
          <v-col cols="12">
            <v-btn
              class="px-8"
              type="submit"
              height="50"
              :loading="loading"
              color="primary"
              >{{ $t('保存') }}</v-btn
            >
          </v-col>
        </v-row>
      </v-form>
    </v-card>
  </div>
</template>

<style lang="scss" scoped></style>
