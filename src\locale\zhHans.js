/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-02 15:56:23
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-08-14 19:12:52
 * @FilePath: \ems_manage\src\locale\zhHans.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export default {
  设备详情: '设备详情',
  账号: '账号',
  账号必填: '账号必填',
  密码必填: '密码必填',
  密码: '密码',
  记住密码: '记住密码',
  登录: '登录',
  数据概括: '数据概括',
  设备详情: '设备详情',
  数据分析: '数据分析',
  电量统计: '电量统计',
  参数设置: '参数设置',
  策略管理: '策略管理',
  日志管理: '日志管理',
  配置中心: '配置中心',
  编辑图: '编辑图',
  退出: '退出',
  两次输入的密码不一致: '两次输入的密码不一致',
  修改成功: '修改成功',
  重置成功: '重置成功',
  重置密码: '重置密码',
  旧密码: '旧密码',
  新密码: '新密码',
  确认新密码: '确认新密码',
  取消: '取消',
  确定: '确定',
  修改密码: '修改密码',
  无数据: '无数据',
  采集器: '采集器',
  设备参数获取失败: '设备参数获取失败',
  请先选择一个正确的设备: '请先选择一个正确的设备',
  数据名称: '数据名称',
  最大值: '最大值',
  数值: '数值',
  发生日期: '发生日期',
  最小值: '最小值',
  设备: '设备',
  参数: '参数',
  设备列表: '设备列表',
  搜索: '搜索',
  区分大小写搜索: '区分大小写搜索',
  导出报表: '导出报表',
  单位: '单位',
  保存成功: '保存成功',
  保存配置: '保存配置',
  删除卡片: '删除卡片',
  显示系统通讯拓扑图: '显示系统通讯拓扑图',
  结构拓扑图: '结构拓扑图',
  配置管理: '配置管理',
  添加卡片: '添加卡片',
  删除: '删除',
  添加新卡片: '添加新卡片',
  标题必填: '标题必填',
  标题: '标题',
  选择设备: '选择设备',
  选择模块: '选择模块',
  选择属性: '选择属性',
  选择图表类型: '选择图表类型',
  折线图: '折线图',
  柱状图: '柱状图',
  图表颜色: '图表颜色',
  项目名称: '项目名称',
  项目地址: '项目地址',
  装机容量: '装机容量',
  装机功率: '装机功率',
  保存: '保存',
  系统配置: '系统配置',
  首页配置: '首页配置',
  充放电信息: '充放电信息',
  今日放电量: '今日放电量',
  累计放电量: '累计放电量',
  今日充电量: '今日充电量',
  累计充电量: '累计充电量',
  告警信息: '告警信息',
  系统信息: '系统信息',
  系统状态: '系统状态',
  运行: '运行',
  并离网模式: '并离网模式',
  通讯状态: '通讯状态',
  并网: '并网',
  离网: '离网',
  在线: '在线',
  离线: '离线',
  设备名称: '设备名称',
  告警对象: '告警对象',
  告警名称: '告警名称',
  告警等级: '告警等级',
  发生时间: '发生时间',
  结束时间: '结束时间',
  操作: '操作',
  等级一: '等级一',
  等级二: '等级二',
  等级三: '等级三',
  已处理: '已处理',
  未处理: '未处理',
  设备概览: '设备概览',
  故障告警: '故障告警',
  运行数据: '运行数据',
  运行状态: '运行状态',
  '1月': '1月',
  '2月': '2月',
  '3月': '3月',
  '4月': '4月',
  '5月': '5月',
  '6月': '6月',
  '7月': '7月',
  '8月': '8月',
  '9月': '9月',
  '10月': '10月',
  '11月': '11月',
  '12月': '12月',
  放电量: '放电量',
  充电量: '充电量',
  日: '日',
  月: '月',
  年: '年',
  总电量: '总电量',
  '00时': '00时',
  '01时': '01时',
  '02时': '02时',
  '03时': '03时',
  '04时': '04时',
  '05时': '05时',
  '06时': '06时',
  '07时': '07时',
  '08时': '08时',
  '09时': '09时',
  '10时': '10时',
  '11时': '11时',
  '12时': '12时',
  '13时': '13时',
  '14时': '14时',
  '15时': '15时',
  '16时': '16时',
  '17时': '17时',
  '18时': '18时',
  '19时': '19时',
  '20时': '20时',
  '21时': '21时',
  '22时': '22时',
  '23时': '23时',
  '01日': '01日',
  '02日': '02日',
  '03日': '03日',
  '04日': '04日',
  '05日': '05日',
  '06日': '06日',
  '07日': '07日',
  '08日': '08日',
  '09日': '09日',
  '10日': '10日',
  '11日': '11日',
  '12日': '12日',
  '13日': '13日',
  '14日': '14日',
  '15日': '15日',
  '16日': '16日',
  '17日': '17日',
  '18日': '18日',
  '19日': '19日',
  '20日': '20日',
  '21日': '21日',
  '22日': '22日',
  '23日': '23日',
  '24日': '24日',
  '25日': '25日',
  '26日': '26日',
  '27日': '27日',
  '28日': '28日',
  '29日': '29日',
  '30日': '30日',
  '31日': '31日',
  参数类型: '参数类型',
  参数值: '参数值',
  执行结果: '执行结果',
  操作人员: '操作人员',
  备注: '备注',
  操作日志: '操作日志',
  使能: '使能',
  不使能: '不使能',
  开机: '开机',
  关机: '关机',
  成功: '成功',
  失败: '失败',
  下发成功: '下发成功',
  有功功率: '有功功率',
  无功功率: '无功功率',
  功率因数: '功率因数',
  防逆流: '防逆流',
  系统开关机: '系统开关机',
  下发: '下发',
  查看日志: '查看日志',
  功率: '功率',
  添加方案: '添加方案',
  修改方案: '修改方案',
  至少要有一条哦: '至少要有一条哦',
  最多只能添加12条哦: '最多只能添加12条哦',
  执行成功: '执行成功',
  星期一: '星期一',
  星期二: '星期二',
  星期三: '星期三',
  星期四: '星期四',
  星期五: '星期五',
  星期六: '星期六',
  星期日: '星期日',
  策略模式: '策略模式',
  削峰填谷: '削峰填谷',
  策略方式: '策略方式',
  周: '周',
  策略配置: '策略配置',
  是否启用策略: '是否启用策略',
  启用: '启用',
  停止: '停止',
  策略方案: '策略方案',
  模式选择: '模式选择',
  方案列表: '方案列表',
  修改: '修改',
  放电: '放电',
  充电: '充电',
  添加: '添加',
  方案名称: '方案名称',
  方案名称必填: '方案名称必填',
  时段: '时段',
  开始时间必填: '开始时间必填',
  开始时间: '开始时间',
  结束时间必填: '结束时间必填',
  结束时间: '结束时间',
  功率必填: '功率必填',
  添加成功: '添加成功',
  修改成功: '修改成功',
  删除成功: '删除成功',
  相加: '相加',
  数据是否组合: '数据是否组合',
  否: '否',
  是: '是',
  断开: '断开',
  闭合: '闭合',
  正常: '正常',
  故障: '故障',
  无效: '无效',
  满充: '满充',
  满放: '满放',
  告警: '告警',
  强充请求: '强充请求',
  欠压保护: '欠压保护',
  待机: '待机',
  制冷: '制冷',
  制热: '制热',
  风扇: '风扇',
  除湿: '除湿',
  关闭: '关闭',
  开启: '开启',
  动作: '动作',
  无: '无',
  有: '有',
  打开: '打开',
  可用: '可用',
  不可用: '不可用',
  高压已断开: '高压已断开',
  高压已连接: '高压已连接',
  需要: '需要',
  不需要: '不需要',
  紧急停止: '紧急停止',
  更多: '更多',
  用户管理: '用户管理',
  无告警: '无告警',
  '恭喜，您的设备很安全。': '恭喜，您的设备很安全。',
  放大: '放大',
  缩小: '缩小',
  实时功率: '实时功率',
  电流: '电流',
  电压: '电压',
  计量点电表: '计量点电表',
  直流: '直流',
  交流: '交流',
  采样测点: '采样测点',
  低压电网: '低压电网',
  选择参数: '选择参数',
  运行策略: '运行策略',
  并网模式: '并网模式',
  备电SOC: '备电SOC',
  变压器容量: '变压器容量',
  电网充电功率: '电网充电功率',
  离网模式: '离网模式',
  油机启动SOC: '油机启动SOC',
  油机充电使能: '油机充电使能',
  油机充电功率: '油机充电功率',
  高压总开关: '高压总开关',
  油机开关: '油机开关',
  '注：开关控制类，点击即触发！': '注：开关控制类，点击即触发！',
  设备概括: '设备概括',
  设备总数: '设备总数',
  告警总数: '告警总数',
  条: '条',
  个: '个',
  开: '开',
  关: '关',
  隐藏: '隐藏',
  隐藏键盘: '隐藏键盘',
  显示键盘: '显示键盘',
  返回: '返回',
  已锁定大写: '已锁定大写',
  大写: '大写',
  切换大写: '切换大写',
  小写: '小写',
  空格: '空格',
  符号: '符号',
  数字: '数字',
  确认: '确认',
  最高单体电压: '最高单体电压',
  最高单体温度: '最高单体温度',
  最低单体电压: '最低单体电压',
  最低单体温度: '最低单体温度',
  温度: '温度',
  策略启用: '策略启用',
  电池设置: '电池设置',
  油机设置: '油机设置',
  并离网设置: '并离网设置',
  防逆流设置: '防逆流设置',
  SOC停止充电点: 'SOC停止充电点',
  SOC停止放电点: 'SOC停止放电点',
  油机启用: '油机启用',
  油机容量: '油机容量',
  电网模式: '电网模式',
  并网优先: '并网优先',
  离网优先: '离网优先',
  储能放电: '储能放电',
  调节策略: '调节策略',
  降功率调节: '降功率调节',
  储能允许充电调节: '储能允许充电调节',
  目标值: '目标值',
  储能和光伏联合供电: '储能和光伏联合供电',
  离网供电: '离网供电',
  停止充电点: '停止充电点',
  停止放电点: '停止放电点',
  '当SOC达到设定值，储能停止充电。': '当SOC达到设定值，储能停止充电。',
  '并网模式下，当SOC达到设定值，储能停止放电。':
    '并网模式下，当SOC达到设定值，储能停止放电。',
  '当SOC达到设定值，储能停止放电，离网模式下，启动油机。':
    '当SOC达到设定值，储能停止放电，离网模式下，启动油机。',
  '启用后，油机会在储能停止放电时启动油机。':
    '启用后，油机会在储能停止放电时启动油机。',
  '当油机工作时，储能会在设定容量内进行充电。':
    '当油机工作时，储能会在设定容量内进行充电。',
  '当有市电时，并网优先会优先使用市电，离网优先会优先离网供电。':
    '当有市电时，并网优先会优先使用市电，离网优先会优先离网供电。',
  '当并网工作时，储能会在设定容量内进行充电。':
    '当并网工作时，储能会在设定容量内进行充电。',
  '使能后，防止光伏和储能的能量馈入电网。':
    '使能后，防止光伏和储能的能量馈入电网。',
  '设定储能放电最大值。': '设定储能放电最大值。',
  '降低放电功率，允许充电调节。': '降低放电功率，允许充电调节。',
  '逆流调节目标值。': '逆流调节目标值。',
  充电限流值: '充电限流值',
  '设置电池充电时的电流最大值。': '设置电池充电时的电流最大值。',
  放电限流值: '放电限流值',
  '设置电池放电时的电流最大值。': '设置电池放电时的电流最大值。',
  欠压保护: '欠压保护',
  '电池放电保护电压。': '电池放电保护电压。',
  欠压恢复: '欠压恢复',
  '电池可放电恢复电压。': '电池可放电恢复电压。',
  过压保护: '过压保护',
  '电池充电电保护电压。': '电池充电电保护电压。',
  过压恢复: '过压恢复',
  '电池可充电恢复电压。': '电池可充电恢复电压。',
  range: '范围在{0} ~ {1}之间',
  修改卡片: '修改卡片',
  显示实时数据曲线: '显示实时数据曲线',
  键盘: '键盘',
  生效: '生效',
  不生效: '不生效',
  '设置储能有功功率。': '设置储能有功功率。',
  '设置储能无功功率。': '设置储能无功功率。',
  '设置储能功率因素。': '设置储能功率因素。',
  '运行策略是否生效。': '运行策略是否生效。',
  键盘已开启: '键盘已开启。',
  键盘已关闭: '键盘已关闭。',
  数据不能为空: '数据不能为空。',
  实时时间: '实时时间',
  '系统按照指定模式工作或停止运行。': '系统按照指定模式工作或停止运行。',
  '控制电池高压开关状态。': '控制电池高压开关状态。',
  '手动开关油机信号。': '手动开关油机信号。',
  请选择设备: '请选择设备',
  请选择日期: '请选择日期',
  项目配置: '项目配置',
  系统设置: '系统设置',
  策略运行: '策略运行',
  策略停止: '策略停止',
  系统开启: '系统开启',
  系统关闭: '系统关闭',
  开始日期: '开始日期',
  结束日期: '结束日期',
  最近一周: '最近一周',
  最近一个月: '最近一个月',
  最近三个月: '最近三个月',
  最近一年: '最近一年',
  开始年份: '开始年份',
  结束年份: '结束年份',
  今年: '今年',
  去年: '去年',
  本月: '本月',
  选择类型: '选择类型',
  选择日期: '选择日期',
  开始月份: '开始月份',
  结束月份: '结束月份',
  日: '日',
  月: '月',
  年: '年',
  IP地址: 'IP地址',
  子网掩码: '子网掩码',
  网关: '网关',
  自动: '自动',
  手动: '手动',
  IP地址必填: 'IP地址必填',
  子网掩码必填: '子网掩码必填',
  网关必填: '网关必填',
  格式不正确: '格式不正确',
  '选择日：导出的是每一天的时刻统计报表':
    '选择日：导出的是每一天的时刻统计报表。',
  '选择月：导出的是每个月的天统计表': '选择月：导出的是每个月的天统计表。',
  '选择年：导出的是每天的月份统计表': '选择年：导出的是每天的月份统计表。',
  只能为数字: '只能为数字。',
  市电开关: '市电开关',
  '手动开关市电信号。': '手动开关市电信号。',
  双分控制: '双分控制',
  '油机信号和市电信号都断开。': '油机信号和市电信号都断开。',
  系统提示: '系统提示',
  '确认下发该参数？': '确认下发该参数？',
  执行: '执行',
  平均: '平均',
  清除电量: '清除电量',
  您的权限不足: '您的权限不足',
  清除成功: '清除成功',
  '显示系统概括信息，项目名称、地址等': '显示系统概括信息，项目名称、地址等',
  '显示系统运行状态，是否离线，通讯是否在线等':
    '显示系统运行状态，是否离线，通讯是否在线等',
  显示最新的告警信息: '显示最新的告警信息',
  显示当前时间: '显示当前时间',
  中: '中',
  英: '英',
  激活: '激活',
  系统启动: '系统启动',
  上下高压: '上下高压',
  空调控制模式: '空调控制模式',
  温控优先: '温控优先',
  能效优先: '能效优先',
  '强制启动空调的电池电芯温差，默认为8': '强制启动空调的电池电芯温差，默认为8',
  电池电芯温差: '电池电芯温差',
  制冷设定温度: '制冷设定温度',
  制冷控制回差: '制冷控制回差',
  制热设定温度: '制热设定温度',
  制热控制回差: '制热控制回差',
  湿度设定值: '湿度设定值',
  除湿回差: '除湿回差',
  柜内温度过高点: '柜内温度过高点',
  柜内温度过低点: '柜内温度过低点',
  柜内湿度过高点: '柜内湿度过高点',
  柜内允许最低温度: '柜内允许最低温度',
  柜内允许最高温度: '柜内允许最高温度',
  后备模式: '后备模式',
  手动模式: '手动模式',
  '当电池电量低于该值时，启动油机': '当电池电量低于该值时，启动油机',
  停止油机SOC: '停止油机SOC',
  '当电池电量大于该值时，关闭油机': '当电池电量大于该值时，关闭油机',
  策略参数设置: '策略参数设置',
  电池保护设置: '电池保护设置',
  空调参数设置: '空调参数设置',
  启动油机SOC: '启动油机SOC',
  停止油机SOC回差: '停止油机SOC回差',
  '整个系统总开关。': '整个系统总开关。',
  '控制油机启动和停止，仅在光伏消纳生效。':
    '控制油机启动和停止，仅在光伏消纳生效。',
  '后备模式时，当电池SOC小于等于该值时，自动启动柴油发电机。':
    '后备模式时，当电池SOC小于等于该值时，自动启动柴油发电机。',
  '后备模式时，当电池SOC大于启动油机SOC加停止油机SOC回差时，自动体制柴油发电机。':
    '后备模式时，当电池SOC大于启动油机SOC加停止油机SOC回差时，自动体制柴油发电机。',
  '温控优先：当电池柜温度或湿度达到设定的阈值后，空调立即启动。当电池柜内温度和湿度达到关闭的阈值时将继续运行五分钟以保证下一轮充放处在合适的环境。':
    '温控优先：当电池柜温度或湿度达到设定的阈值后，空调立即启动。当电池柜内温度和湿度达到关闭的阈值时将继续运行五分钟以保证下一轮充放处在合适的环境。',
  '能效优先：当电池柜温度或湿度达到设定的阈值持续五分钟后，空调才启动（湿度存在波动，五分钟是确认时间），当电池柜内温度和湿度达到关闭的阈值时，立即关闭空调。':
    '能效优先：当电池柜温度或湿度达到设定的阈值持续五分钟后，空调才启动（湿度存在波动，五分钟是确认时间），当电池柜内温度和湿度达到关闭的阈值时，立即关闭空调。',
  '当电池电芯温差大于该值时，空调强制启动。':
    '当电池电芯温差大于该值时，空调强制启动。',
  '当电池柜温度大于该值时，达到空调启动条件。':
    '当电池柜温度大于该值时，达到空调启动条件。',
  '当电池柜温度低于制冷设定温度-制冷控制回差时，达到空调关闭条件。':
    '当电池柜温度低于制冷设定温度-制冷控制回差时，达到空调关闭条件。',
  '当电池柜温度小于该值时，达到空调启动条件。':
    '当电池柜温度小于该值时，达到空调启动条件。',
  '当电池柜温度大于制热设定温度+制热控制回差时，达到空调关闭条件。':
    '当电池柜温度大于制热设定温度+制热控制回差时，达到空调关闭条件。',
  '当电池柜湿度大于该值时，达到空调启动条件。':
    '当电池柜湿度大于该值时，达到空调启动条件。',
  '当电池柜湿度小于湿度设定值-除湿回差时，达到空调关闭条件。':
    '当电池柜湿度小于湿度设定值-除湿回差时，达到空调关闭条件。',
  '当电池柜内温度大于该值时，触发告警。':
    '当电池柜内温度大于该值时，触发告警。',
  '当电池柜内温度小于该值时，触发告警。':
    '当电池柜内温度小于该值时，触发告警。',
  '当电池柜内湿度大于该值时，触发告警。':
    '当电池柜内湿度大于该值时，触发告警。',
  '系统运行或待机时，都不允许低于该值，低于该值时强制启动空调制热以保证随时能够上启动系统。':
    '系统运行或待机时，都不允许低于该值，低于该值时强制启动空调制热以保证随时能够上启动系统。',
  '系统运行或待机时，都不允许高于该值，高于该值时强制启动空调制冷以保证随时能够上启动系统。':
    '系统运行或待机时，都不允许高于该值，高于该值时强制启动空调制冷以保证随时能够上启动系统。',
  光伏限功率设置: '光伏限功率设置',
  电网充电: '电网充电',
  油机充电: '油机充电',
  充电功率: '充电功率',
  '是否允许从电网取电进行充电。': '是否允许从电网取电进行充电。',
  '是否允许从柴油发电机取电充电。': '是否允许从柴油发电机取电充电。',
  '充电功率。': '充电功率。',
  '光伏最大允许充电功率。': '光伏最大允许充电功率。',
  '未激活策略，请联系管理人员激活。': '未激活策略，请联系管理人员激活。',
  未激活: '未激活',
  已激活: '已激活',
  策略列表: '策略列表',
  有功功率设置: '有功功率设置',
  无功功率设置: '无功功率设置',
  功率因数设置: '功率因数设置',
  点击显示更多: '点击显示更多',
  油机启动: '油机启动',
  close: '断开',
  open: '闭合',
  yes: '是',
  no: '否',
  normal: '正常',
  fault: '故障',
  alarm: '告警',
  光伏消纳: '光伏消纳',
  选择策略: '选择策略',
  计算公式: '计算公式',
  'x+y+z,x为第一个属性值,y为第二个属性值...':
    'x+y+z,x为第一个属性值,y为第二个属性值...',
  设备展示配置: '设备展示配置',
  模块类型: '模块类型',
  配置: '配置',
  添加配置: '添加配置',
  修改配置: '修改配置',
  包总数: '包总数',
  包: '包',
  录波: '录波',
  故障详情: '故障详情',
  手动调节: '手动调节',
  系统文件配置: '系统文件配置',
  文件名称: '文件名称',
  备份成功: '备份成功',
  恢复备份成功: '恢复备份成功',
  查看: '查看',
  备份: '备份',
  恢复备份: '恢复备份',
  '是否覆盖已有文件？': '是否覆盖已有文件？',
  设备编号: '设备编号',
  设备类名: '设备类名',
  设备类型: '设备类型',
  '设备序列号(云平台)': '设备序列号(云平台)',
  '设备模块编号(云平台)': '设备模块编号(云平台)',
  '设备模块类型(云平台)': '设备模块类型(云平台)',
  添加设备: '添加设备',
  修改设备: '修改设备',
  协议类型: '协议类型',
  适配器协议类型: '适配器协议类型',
  适配器数量: '适配器数量',
  从站地址: '从站地址',
  协议文件: '协议文件',
  控制文件: '控制文件',
  读写周期: '读写周期',
  协议信息: '协议信息',
  XML协议文件: 'XML协议文件',
  故障录波JSON文件: '故障录波JSON文件',
  适配器类型: '适配器类型',
  设备IP: '设备IP',
  设备端口: '设备端口',
  串口文件路径: '串口文件路径',
  串口波特率: '串口波特率',
  校验位: '校验位',
  数据位: '数据位',
  停止位: '停止位',
  JSON控制文件: 'JSON控制文件',
  请输入密码: '请输入密码',
  应用: '应用',
  '是否应用？': '是否应用？',
  请输入所要备份的文件名: '请输入所要备份的文件名',
  必填: '必填',
  应用成功: '应用成功',
  应用失败: '应用失败',
  退出: '退出',
  更多: '更多',
  设备名称重复了: '设备名称重复了',
  数据库管理: '数据库管理',
  表名: '表名',
  已连接: '已连接',
  未连接: '未连接',
  '是否确认删除该表？': '是否确认删除该表？',
  重启EMS程序: '重启EMS程序',
  重启成功: '重启成功',
  项目信息: '项目信息',
  软件版本: '软件版本',
  硬件版本: '硬件版本',
  系统语言: '系统语言',
  系统键盘: '系统键盘',
  设备管理: '设备管理',
  全部: '全部',
  告警状态: '告警状态',
  额定功率: '额定功率',
  备电保持SOC: '备电保持SOC',
  油机停止SOC: '油机停止SOC',
  油机停止SOC一定要大于油机启动SOC: '油机停止SOC一定要大于油机启动SOC',
  结束日期必须晚于开始日期: '结束日期必须晚于开始日期',
  时间范围超过三个月: '时间范围超过三个月',
  日期: '日期',
  刷新: '刷新',
  模拟: '模拟',
  状态: '状态',
  清空: '清空',
  游客登录: '游客登录',
  请选择参数: '请选择参数',
  请输入存储周期: '请输入存储周期',
  设置成功: '设置成功',
  设置存储周期: '设置存储周期',
  存储周期: '存储周期',
  '请输入数字（s）': '请输入数字（s）',
  选择地区: '选择地区',
  调度获取成功: '调度获取成功',
  请先设置区域: '请先设置区域',
  策略生成: '策略生成',
  常规生成: '常规生成',
  精确生成: '精确生成',
  更新策略: '更新策略',
  是否使用currentSOC: '是否使用currentSOC',
  无调度行为: '无调度行为',
  下发关机: '下发关机',
  设置0功率: '设置0功率',
  循环时长: '循环时长',
  设置时区: '设置时区',
  后台日志: '后台日志',
  建立连接: '建立连接',
  连接关闭: '连接关闭',
  连接出错啦: '连接出错啦',
  日志等级: '日志等级',
  '是否离开此页面？': '是否离开此页面？',
  分钟: '分钟',
  总收入: '总收入',
  总支出: '总支出',
  净利润: '净利润',
  电网支出: '电网支出',
  电网收入: '电网收入',
  节能收益: '节能收益',
  充电电价: '充电电价',
  放电电价: '放电电价',
  光伏日充电量: '光伏日充电量',
  电网日充电量: '电网日充电量',
  电网日放电量: '电网日放电量',
  日充电量: '日充电量',
  日放电量: '日放电量',
  系统收益: '系统收益',
  最新: '最新',
  请先配置系统收益信息: '请先配置系统收益信息',
  收入区域: '收入区域',
  收入场景: '收入场景',
  选择场景: '选择场景',
  收益详情: '收益详情',
  收益趋势: '收益趋势',
  历史策略: '历史策略',
  查询失败: '查询失败',
  历史策略记录: '历史策略记录',
  查看和管理历史调度策略: '查看和管理历史调度策略',
  条记录: '条记录',
  查询日期: '查询日期',
  查询: '查询',
  最新记录在顶部: '最新记录在顶部',
  加载中: '加载中',
  该日期暂无历史记录: '该日期暂无历史记录',
  请选择其他日期或创建新的策略: '请选择其他日期或创建新的策略',
  历史: '历史',
  调度点: '调度点',
  范围: '范围',
  最小: '最小',
  最大: '最大',
  调度预览: '调度预览',
  详情: '详情',
  策略详情: '策略详情',
  基础配置: '基础配置',
  算法类型: '算法类型',
  SOC配置: 'SOC配置',
  最大SOC: '最大SOC',
  最小SOC: '最小SOC',
  使用外部SOC: '使用外部SOC',
  外部SOC: '外部SOC',
  功率配置: '功率配置',
  最大充电功率: '最大充电功率',
  最大放电功率: '最大放电功率',
  电池容量: '电池容量',
  效率配置: '效率配置',
  充电效率: '充电效率',
  放电效率: '放电效率',
  价格阈值比例: '价格阈值比例',
  应用此策略: '应用此策略',
  属性: '属性',
  新值: '新值',
  旧值: '旧值',
  策略: '策略',
  主机: '主机',
  连接中: '连接中',
  日志控制面板: '日志控制面板',
  会话默认日志等级: '会话默认日志等级',
  选择默认等级: '选择默认等级',
  模块控制: '模块控制',
  全部禁用: '全部禁用',
  全部启用: '全部启用',
  模块等级: '模块等级',
  '暂无可用模块，请检查连接状态': '暂无可用模块，请检查连接状态',
  实时日志: '实时日志',
  参数下发: '参数下发',
  选择: '选择',
  导入语言文件: '导入语言文件',
  语言文件: '语言文件',
  语言文件名: '语言文件名',
  '例如：zh_CN.ems': '例如：zh_CN.ems',
  语言文件内容: '语言文件内容',
  '请粘贴完整的语言文件内容...': '请粘贴完整的语言文件内容...',
  覆盖模式: '覆盖模式',
  不覆盖模式: '不覆盖模式',
  如果文件已存在将被覆盖: '如果文件已存在将被覆盖',
  如果文件已存在将导入失败: '如果文件已存在将导入失败',
  语言文件名不能为空: '语言文件名不能为空',
  语言文件内容不能为空: '语言文件内容不能为空',
  请输入语言文件名: '请输入语言文件名',
  请输入语言文件内容: '请输入语言文件内容',
  语言文件导入成功: '语言文件导入成功',
  导入: '导入',
  拖拽文件到此处或点击选择文件: '拖拽文件到此处或点击选择文件',
  '支持 .ems 格式': '支持 .ems 格式',
  文件读取失败: '文件读取失败'
}
