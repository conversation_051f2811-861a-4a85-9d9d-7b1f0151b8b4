<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-29 14:53:50
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-11 14:28:36
 * @FilePath: \ems_manage\src\views\Param\strategy.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import {
  ref,
  toRefs,
  getCurrentInstance,
  onMounted,
  nextTick,
  computed
} from 'vue'
import { useGlobalStore } from '@/store/global'
import { useParamStore } from '@/store/module/param'
import { useUserStore } from '@/store/module/user'
import { useLogStore } from '@/store/module/log'
import { useI18n } from 'vue-i18n'
import { useDisplay } from 'vuetify'
import { isBetween, setLeftWidth } from '@/utils'
import dayjs from '@/utils/date'

import { ElLoading } from 'element-plus'

const props = defineProps({
  modelValue: {
    type: String
  }
})
const { userInfo } = toRefs(useUserStore())
const { mobile } = useDisplay()
const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { strategyData } = toRefs(useParamStore())
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  keyboardRange,
  isShowKeyboard
} = toRefs(useGlobalStore())
const getData = () => {
  useParamStore()
    .getStrategyDataFn(JSON.stringify({ name: props.modelValue }))
    .then((res) => {
      sendForm.value = strategyData.value
      nextTick(() => {
        setLeftWidth('left')
      })
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}
getData()

/**
 * tab
 */
const tab = ref(1)
const handleWindowChange = () => {
  nextTick(() => {
    setLeftWidth('left')
  })
}

/**
 * 下发策略
 */
const sendForm = ref({
  currentMode: undefined,
  antiFlow: undefined,
  pvPowerLimit: undefined,
  batteryBackupHoldSOC: undefined,
  backup: {
    generatorStopSOCDiff: undefined,
    chgPower: undefined,
    allowGridChg: undefined,
    allowGeneratorChg: undefined
  },
  airconditioner: {
    airControlMode: undefined,
    forceAirConBatTempDiff: undefined,
    coolingSetTemp: undefined,
    coolingControlHysteresis: undefined,
    heatingSetTemp: undefined,
    heatingControlHysteresis: undefined,
    humiditySetValue: undefined,
    humidityControlHysteresis: undefined,
    cabinetTempHighPoint: undefined,
    cabinetTempLowPoint: undefined,
    cabinetHumidityHighPoint: undefined,
    cabinetAllowMinTemp: undefined,
    cabinetAllowMaxTemp: undefined
  },
  manual: {
    activePower: undefined,
    powerFactor: undefined,
    reactivePower: undefined,
    generatorBootSOC: undefined,
    generatorStopSOC: undefined
  },
  pcsparam: {
    chgCurLimit: undefined,
    dischgCurLimit: undefined,
    overVolProtectionPoint: undefined,
    overVolRecoveryPoint: undefined,
    stopChgPoint: undefined,
    stopDischgPoint: undefined,
    underVolProtectionPoint: undefined,
    underVolRecoveryPoint: undefined
  }
})
const enFiled = {
  currentMode: 'Strategy Pattern',
  antiFlow: 'Reverse Power Protection',
  pvPowerLimit: 'Photovoltaic Power Limit Setting',
  batteryBackupHoldSOC: 'Battery Backup Holding SOC',
  generatorBootSOC: 'Diesel Generator Start SOC',
  generatorStopSOC: 'Diesel Generator Stop SOC',
  generatorStopSOCDiff: 'Stop The Diesel Generator SOC Hysteresis',
  chgPower: 'Charging Power',
  allowGridChg: 'Grid Charging Power',
  allowGeneratorChg: 'Diesel Generator Charging',
  airControlMode: 'Air Conditioning Control Mode',
  forceAirConBatTempDiff: 'Battery Cell Temperature Difference',
  coolingSetTemp: 'Refrigeration Set Temperature',
  coolingControlHysteresis: 'Refrigeration Control Differential',
  heatingSetTemp: 'Heating Set Temperature',
  heatingControlHysteresis: 'Heating Control Hysteresis',
  humiditySetValue: 'Humidity Set Point',
  humidityControlHysteresis: 'Dehumidification Hysteresis',
  cabinetTempHighPoint: 'The temperature inside the cabinet is too high',
  cabinetTempLowPoint: 'The temperature inside the cabinet is too low',
  cabinetHumidityHighPoint: 'The humidity in the cabinet is too high',
  cabinetAllowMinTemp: 'Minimum temperature allowed in the cabinet',
  cabinetAllowMaxTemp: 'Maximum allowable temperature inside the cabinet',
  activePower: 'Active Power Setting',
  powerFactor: 'Power Factor Setting',
  reactivePower: 'Reactive Power Setting',
  systemSwitch: 'System Switch',
  generatorSwitch: 'Diesel Engine Switch',
  bmsSwitch: 'High Pressure Up And Down',
  chgCurLimit: 'Charging Current Limit',
  dischgCurLimit: 'Discharge Current Limit',
  overVolProtectionPoint: 'Overvoltage Protection',
  overVolRecoveryPoint: 'Overvoltage Recovery',
  stopChgPoint: 'Stop charging point',
  stopDischgPoint: 'Stop discharge point',
  underVolProtectionPoint: 'Undervoltage Protection',
  underVolRecoveryPoint: 'Undervoltage Recovery'
}
const model = computed(() => {
  return (value) => {
    if (value.indexOf('.') !== -1) {
      let props = value.split('.')
      return sendForm.value[props[0]][props[1]] == 0 ? t('不使能') : t('使能')
    } else {
      return sendForm.value[value] == 0 ? t('不使能') : t('使能')
    }
  }
})

onMounted(() => {
  setLeftWidth('left')
})

/**
 * 键盘
 */
keyboardMode.value = 'di_git'
const handleShow = (e, value, prop, range) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
  keyboardRange.value = range
}
confirmCall.value = () => {
  handleSendClick(keyboardInputValue.value, undefined, currentInput.value)
}
const setLoading = ref()
const handleSendClick = (value, range, prop) => {
  currentInput.value = prop
  if (value) {
    const regex = /^-?\d+(\.\d+)?$/
    if (!regex.test(value)) {
      snackbar.value = true
      snackbarText.value = t('只能为数字')
      getData()
      return
    }
    if (value === '' && !isShowKeyboard.value) {
      snackbar.value = true
      snackbarText.value = t('数据不能为空')
      getData()
      return
    }
  } else {
    if (value === '' && !isShowKeyboard.value) {
      snackbar.value = true
      snackbarText.value = t('数据不能为空')
      getData()
      return
    }
  }
  if (range) {
    if (!isBetween(Number(value), range[0], range[1])) {
      snackbar.value = true
      snackbarText.value = t('range', range)
      getData()
      return
    }
  }
  // 油机停止SOC>油机启动SOC
  if (currentInput.value == 'manual.generatorBootSOC') {
    if (sendForm.value.manual.generatorStopSOC <= Number(value)) {
      snackbar.value = true
      snackbarText.value = t('油机停止SOC一定要大于油机启动SOC')
      return
    }
  }
  if (currentInput.value == 'manual.generatorStopSOC') {
    if (Number(value) <= sendForm.value.manual.generatorBootSOC) {
      snackbar.value = true
      snackbarText.value = t('油机停止SOC一定要大于油机启动SOC')
      return
    }
  }
  let data = {
    // ...sendForm.value
  }
  // 下发日志
  setLoading.value = ElLoading.service({
    lock: true,
    text: 'Loading',
    background: 'rgba(0, 0, 0, 0.7)'
  })
  if (currentInput.value.indexOf('.') !== -1) {
    let props = currentInput.value.split('.')
    if (keyboardDialog.value) {
      data[props[0]] = {}
      data[props[0]][props[1]] = Number(keyboardInputValue.value)
      setLogFn({
        param_type: enFiled[props[1]],
        set_value: keyboardInputValue.value
      })
    } else {
      data[props[0]] = {}
      data[props[0]][props[1]] = Number(value)
      setLogFn({ param_type: enFiled[props[1]], set_value: value })
    }
  } else {
    if (keyboardDialog.value) {
      data[currentInput.value] = Number(keyboardInputValue.value)
      setLogFn({
        param_type: enFiled[currentInput.value],
        set_value: keyboardInputValue.value
      })
    } else {
      data[currentInput.value] = Number(value)
      setLogFn({ param_type: enFiled[currentInput.value], set_value: value })
    }
  }
  useParamStore()
    .updateStrategyConfigFn(
      JSON.stringify({
        name: props.modelValue,
        config: data
      })
    )
    .then((res) => {
      snackbar.value = true
      snackbarText.value = t('下发成功')
      if (keyboardDialog.value) {
        sendForm.value[currentInput.value] = keyboardInputValue.value
        showKeyboard.value = false
        keyboardDialog.value = false
      }
      dialog.value = false
      setLoading.value.close()
      getData()
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
      setLoading.value.close()
    })
}
const setLogFn = async ({ param_type, set_value }) => {
  try {
    const res = await useLogStore().storeOperationLogFn(
      JSON.stringify({
        param_type, // 参数类型
        set_value: set_value + '', // 设置值
        time: dayjs().format('YYYY-MM-DD HH:mm:ss'), // 设置时间 2025-01-01 00:00:00
        user: userInfo.value.user_name // 操作人
      })
    )
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
    setLoading.value.close()
  }
}
const handleControlClick = (value, prop) => {
  nextTick(() => {
    setLeftWidth('left')
  })
  if (isShowKeyboard.value) return
  currentInput.value = prop
  handleSendClick(value, undefined, currentInput.value)
}
/**
 * 确认下发弹框
 */
const dialog = ref(false)
const loading = ref(false)
const dialogData = ref({
  value: '',
  prop: ''
})
const handleConfirmClick = () => {
  loading.value = true
  setLogFn({
    param_type: enFiled[dialogData.value.prop],
    set_value: dialogData.value.value
  })
  useParamStore()
    .postStrategyCommandFn(
      JSON.stringify({
        paramName: dialogData.value.prop,
        value: dialogData.value.value
      })
    )
    .then((res) => {
      snackbar.value = true
      snackbarText.value = t('下发成功')
      dialog.value = false
      loading.value = false
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
      loading.value = false
    })
}
const handleSendDialogClick = (value, prop) => {
  dialogData.value.value = value
  dialogData.value.prop = prop
  dialog.value = true
}
const handleCancelClick = () => {
  dialog.value = false
  loading.value = false
}
</script>

<template>
  <div :class="[mobile && 'flex-column', 'flex', 'w-100']">
    <div :style="{ width: '100%' }">
      <v-tabs
        v-model="tab"
        color="primary"
        class="mb-4"
        @update:modelValue="handleWindowChange"
      >
        <v-tab :text="$t('策略参数设置')" :value="1" />
        <v-tab :text="$t('电池保护设置')" :value="2" />
        <v-tab :text="$t('空调参数设置')" :value="3" />
      </v-tabs>
      <v-tabs-window v-model="tab" class="w-100">
        <v-tabs-window-item :value="1" class="w-100">
          <template v-if="tab == 1">
            <!-- 策略 -->
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                {{ $t('策略模式') }}
              </div>
              <v-col :cols="4" class="px-0 flex">
                <v-radio-group
                  v-model="sendForm.currentMode"
                  inline
                  hide-details
                  @update:modelValue="
                    handleControlClick(sendForm.currentMode, 'currentMode')
                  "
                >
                  <!-- <v-radio :label="$t('手动调节')" :value="0"></v-radio> -->
                  <v-radio :label="$t('后备模式')" :value="1"></v-radio>
                  <v-radio :label="$t('光伏消纳')" :value="2"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col cols="2" class="pl-0"> </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.currentMode,
                      undefined,
                      'currentMode'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <!-- 执行 -->
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('系统启动') }}</div>
                <v-tooltip :text="$t('整个系统总开关。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-btn
                  class="mr-2"
                  @click="handleSendDialogClick(1, 'systemSwitch')"
                  >{{ $t('开') }}</v-btn
                >
                <v-btn @click="handleSendDialogClick(0, 'systemSwitch')">{{
                  $t('关')
                }}</v-btn>
              </v-col>
            </div>
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('油机启动') }}</div>
                <v-tooltip
                  :text="$t('控制油机启动和停止，仅在光伏消纳生效。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-btn
                  class="mr-2"
                  @click="handleSendDialogClick(1, 'generatorSwitch')"
                  >{{ $t('开') }}</v-btn
                >
                <v-btn @click="handleSendDialogClick(0, 'generatorSwitch')">{{
                  $t('关')
                }}</v-btn>
              </v-col>
            </div>
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('上下高压') }}</div>
                <v-tooltip :text="$t('控制电池高压开关状态。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-btn
                  class="mr-2"
                  @click="handleSendDialogClick(1, 'bmsSwitch')"
                  >{{ $t('开') }}</v-btn
                >
                <v-btn @click="handleSendDialogClick(0, 'bmsSwitch')">{{
                  $t('关')
                }}</v-btn>
              </v-col>
            </div>
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('防逆流') }}</div>
                <v-tooltip
                  :text="$t('使能后，防止光伏和储能的能量馈入电网。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-switch
                  v-model="sendForm.antiFlow"
                  :label="`${model('antiFlow')}`"
                  :false-value="0"
                  :true-value="1"
                  hide-details
                  color="primary"
                  @update:modelValue="
                    handleControlClick(sendForm.antiFlow, 'antiFlow')
                  "
                />
              </v-col>
              <v-col cols="2" class="pl-0"> </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(sendForm.antiFlow, undefined, 'antiFlow')
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row flex-wrap">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('光伏限功率设置') }}</div>
                <v-tooltip :text="$t('光伏最大允许充电功率。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.pvPowerLimit"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow($event, sendForm.pvPowerLimit, 'pvPowerLimit')
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">%</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.pvPowerLimit,
                      undefined,
                      'pvPowerLimit'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <!-- 下发 -->
            <div class="flex align-center row" v-if="sendForm.currentMode == 1">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('电网充电') }}</div>
                <v-tooltip
                  :text="$t('是否允许从电网取电进行充电。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-switch
                  v-model="sendForm.backup.allowGridChg"
                  :label="`${model('backup.allowGridChg')}`"
                  :false-value="0"
                  :true-value="1"
                  hide-details
                  color="primary"
                  @update:modelValue="
                    handleControlClick(
                      sendForm.backup.allowGridChg,
                      'backup.allowGridChg'
                    )
                  "
                />
              </v-col>
              <v-col cols="2" class="pl-0"> </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.backup.allowGridChg,
                      undefined,
                      'backup.allowGridChg'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row" v-if="sendForm.currentMode == 1">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('油机充电') }}</div>
                <v-tooltip
                  :text="$t('是否允许从柴油发电机取电充电。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-switch
                  v-model="sendForm.backup.allowGeneratorChg"
                  :label="`${model('backup.allowGeneratorChg')}`"
                  :false-value="0"
                  :true-value="1"
                  hide-details
                  color="primary"
                  @update:modelValue="
                    handleControlClick(
                      sendForm.backup.allowGeneratorChg,
                      'backup.allowGeneratorChg'
                    )
                  "
                />
              </v-col>
              <v-col cols="2" class="pl-0"> </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.backup.allowGeneratorChg,
                      undefined,
                      'backup.allowGeneratorChg'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="sendForm.currentMode == 1"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('充电功率') }}</div>
                <v-tooltip :text="$t('充电功率。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.backup.chgPower"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.backup.chgPower,
                      'backup.chgPower',
                      [0, 500]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">0 ~ 500 kW</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.backup.chgPower,
                      [0, 500],
                      'backup.chgPower'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="sendForm.currentMode == 1"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('备电保持SOC') }}</div>
                <v-tooltip
                  :text="
                    $t(
                      '后备模式时，当电池SOC小于等于该值时，自动启动柴油发电机。'
                    )
                  "
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.batteryBackupHoldSOC"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.batteryBackupHoldSOC,
                      'batteryBackupHoldSOC'
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">%</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.batteryBackupHoldSOC,
                      undefined,
                      'batteryBackupHoldSOC'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="sendForm.currentMode == 2"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('有功功率设置') }}</div>
                <v-tooltip :text="$t('设置储能有功功率。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.manual.activePower"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.manual.activePower,
                      'manual.activePower',
                      [-100, 100]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">-100 ~ 100 %</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.manual.activePower,
                      [-100, 100],
                      'manual.activePower'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="sendForm.currentMode == 2"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('无功功率设置') }}</div>
                <v-tooltip :text="$t('设置储能无功功率。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.manual.reactivePower"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.manual.reactivePower,
                      'manual.reactivePower',
                      [0, 100]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">0 ~ 100 %</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.manual.reactivePower,
                      [0, 100],
                      'manual.reactivePower'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="sendForm.currentMode == 2"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('功率因数设置') }}</div>
                <v-tooltip :text="$t('设置储能功率因素。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.manual.powerFactor"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.manual.powerFactor,
                      'manual.powerFactor',
                      [-1, 1]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">-1 ~ 1</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.manual.powerFactor,
                      [-1, 1],
                      'manual.powerFactor'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="sendForm.currentMode == 2"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('油机启动SOC') }}</div>
                <v-tooltip :text="$t('油机启动SOC')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.manual.generatorBootSOC"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.manual.generatorBootSOC,
                      'manual.generatorBootSOC',
                      [0, 99]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">0 ~ 99 %</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.manual.generatorBootSOC,
                      [0, 99],
                      'manual.generatorBootSOC'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="sendForm.currentMode == 2"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('油机停止SOC') }}</div>
                <v-tooltip :text="$t('油机停止SOC')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="4" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.manual.generatorStopSOC"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.manual.generatorStopSOC,
                      'manual.generatorStopSOC',
                      [1, 100]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">1 ~ 100 %</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.manual.generatorStopSOC,
                      [1, 100],
                      'manual.generatorStopSOC'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
          </template>
        </v-tabs-window-item>
        <v-tabs-window-item :value="2" class="w-100">
          <template v-if="tab == 2">
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('停止充电点') }}</div>
                <v-tooltip
                  :text="$t('当SOC达到设定值，储能停止充电。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="5" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.pcsparam.stopChgPoint"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.pcsparam.stopChgPoint,
                      'pcsparam.stopChgPoint',
                      [0, 100]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">0 ~ 100 %</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.pcsparam.stopChgPoint,
                      [0, 100],
                      'pcsparam.stopChgPoint'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('停止放电点') }}</div>
                <v-tooltip
                  :text="
                    $t('当SOC达到设定值，储能停止放电，离网模式下，启动油机。')
                  "
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <template v-if="userInfo?.permission_level == 4">
                <v-col cols="5" class="pl-0 flex">
                  <v-text-field
                    v-model="sendForm.pcsparam.stopDischgPoint"
                    variant="outlined"
                    label=""
                    hide-details
                    @click:control="
                      handleShow(
                        $event,
                        sendForm.pcsparam.stopDischgPoint,
                        'pcsparam.stopDischgPoint',
                        [0, 95]
                      )
                    "
                  ></v-text-field>
                </v-col>
                <v-col cols="2" class="pl-0">
                  <div class="text-body-1 py-4 px-0">0 ~ 95 %</div>
                </v-col>
                <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                  <v-btn
                    @click="
                      handleSendClick(
                        sendForm.pcsparam.stopDischgPoint,
                        [0, 95],
                        'pcsparam.stopDischgPoint'
                      )
                    "
                    >{{ $t('下发') }}</v-btn
                  >
                </v-col>
              </template>
              <template v-else>
                <v-col cols="5" class="pl-0 flex">
                  <v-text-field
                    v-model="sendForm.pcsparam.stopDischgPoint"
                    variant="outlined"
                    label=""
                    hide-details
                    @click:control="
                      handleShow(
                        $event,
                        sendForm.pcsparam.stopDischgPoint,
                        'pcsparam.stopDischgPoint',
                        [10, 95]
                      )
                    "
                  ></v-text-field>
                </v-col>
                <v-col cols="2" class="pl-0">
                  <div class="text-body-1 py-4 px-0">10 ~ 95 %</div>
                </v-col>
                <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                  <v-btn
                    @click="
                      handleSendClick(
                        sendForm.pcsparam.stopDischgPoint,
                        [10, 95],
                        'pcsparam.stopDischgPoint'
                      )
                    "
                    >{{ $t('下发') }}</v-btn
                  >
                </v-col>
              </template>
            </div>
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('充电限流值') }}</div>
                <v-tooltip
                  :text="$t('设置电池充电时的电流最大值。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="5" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.pcsparam.chgCurLimit"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.pcsparam.chgCurLimit,
                      'pcsparam.chgCurLimit',
                      [0, 5000]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">0 ~ 5000 A</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.pcsparam.chgCurLimit,
                      [0, 5000],
                      'pcsparam.chgCurLimit'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('放电限流值') }}</div>
                <v-tooltip
                  :text="$t('设置电池放电时的电流最大值。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="5" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.pcsparam.dischgCurLimit"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.pcsparam.dischgCurLimit,
                      'pcsparam.dischgCurLimit',
                      [0, 5000]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">0 ~ 5000 A</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.pcsparam.dischgCurLimit,
                      [0, 5000],
                      'pcsparam.dischgCurLimit'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('欠压保护') }}</div>
                <v-tooltip :text="$t('电池放电保护电压。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="5" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.pcsparam.underVolProtectionPoint"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.pcsparam.underVolProtectionPoint,
                      'pcsparam.underVolProtectionPoint',
                      [50, 1000]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">50 ~ 1000 V</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.pcsparam.underVolProtectionPoint,
                      [50, 1000],
                      'pcsparam.underVolProtectionPoint'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('欠压恢复') }}</div>
                <v-tooltip :text="$t('电池可放电恢复电压。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="5" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.pcsparam.underVolRecoveryPoint"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.pcsparam.underVolRecoveryPoint,
                      'pcsparam.underVolRecoveryPoint',
                      [50, 1000]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">50 ~ 1000 V</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.pcsparam.underVolRecoveryPoint,
                      [50, 1000],
                      'pcsparam.underVolRecoveryPoint'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('过压保护') }}</div>
                <v-tooltip :text="$t('电池充电电保护电压。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="5" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.pcsparam.overVolProtectionPoint"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.pcsparam.overVolProtectionPoint,
                      'pcsparam.overVolProtectionPoint',
                      [50, 1000]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">50 ~ 1000 V</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.pcsparam.overVolProtectionPoint,
                      [50, 1000],
                      'pcsparam.overVolProtectionPoint'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('过压恢复') }}</div>
                <v-tooltip :text="$t('电池可充电恢复电压。')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="5" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.pcsparam.overVolRecoveryPoint"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.pcsparam.overVolRecoveryPoint,
                      'pcsparam.overVolRecoveryPoint',
                      [50, 1000]
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="2" class="pl-0">
                <div class="text-body-1 py-4 px-0">50 ~ 1000 V</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.pcsparam.overVolRecoveryPoint,
                      [50, 1000],
                      'pcsparam.overVolRecoveryPoint'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
          </template>
        </v-tabs-window-item>
        <v-tabs-window-item :value="3" class="w-100">
          <template v-if="tab == 3">
            <!-- 空调 -->
            <div class="flex align-center row">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('空调控制模式') }}</div>
                <v-tooltip location="top">
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon>
                  </template>
                  <template #default>
                    <div>
                      {{
                        $t(
                          '温控优先：当电池柜温度或湿度达到设定的阈值后，空调立即启动。当电池柜内温度和湿度达到关闭的阈值时将继续运行五分钟以保证下一轮充放处在合适的环境。'
                        )
                      }}
                    </div>
                    <div>
                      {{
                        $t(
                          '能效优先：当电池柜温度或湿度达到设定的阈值持续五分钟后，空调才启动（湿度存在波动，五分钟是确认时间），当电池柜内温度和湿度达到关闭的阈值时，立即关闭空调。'
                        )
                      }}
                    </div>
                  </template>
                </v-tooltip>
              </div>
              <v-col :cols="3" class="px-0 flex">
                <v-radio-group
                  v-model="sendForm.airconditioner.airControlMode"
                  inline
                  hide-details
                  @update:modelValue="
                    handleControlClick(
                      sendForm.airconditioner.airControlMode,
                      'airconditioner.airControlMode'
                    )
                  "
                >
                  <v-radio :label="$t('温控优先')" :value="0"></v-radio>
                  <v-radio :label="$t('能效优先')" :value="1"></v-radio>
                </v-radio-group>
              </v-col>
              <v-col cols="1" class="pl-0"> </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.airconditioner.airControlMode,
                      undefined,
                      'airconditioner.airControlMode'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row flex-wrap">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('电池电芯温差') }}</div>
                <v-tooltip
                  :text="$t('当电池电芯温差大于该值时，空调强制启动。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="3" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.airconditioner.forceAirConBatTempDiff"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.airconditioner.forceAirConBatTempDiff,
                      'airconditioner.forceAirConBatTempDiff'
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="1" class="pl-0">
                <div class="text-body-1 py-4 px-0">℃</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.airconditioner.forceAirConBatTempDiff,
                      undefined,
                      'airconditioner.forceAirConBatTempDiff'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row flex-wrap">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('制冷设定温度') }}</div>
                <v-tooltip
                  :text="$t('当电池柜温度大于该值时，达到空调启动条件。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="3" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.airconditioner.coolingSetTemp"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.airconditioner.coolingSetTemp,
                      'airconditioner.coolingSetTemp'
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="1" class="pl-0">
                <div class="text-body-1 py-4 px-0">℃</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.airconditioner.coolingSetTemp,
                      undefined,
                      'airconditioner.coolingSetTemp'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row flex-wrap">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('制冷控制回差') }}</div>
                <v-tooltip
                  :text="
                    $t(
                      '当电池柜温度低于制冷设定温度-制冷控制回差时，达到空调关闭条件。'
                    )
                  "
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="3" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.airconditioner.coolingControlHysteresis"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.airconditioner.coolingControlHysteresis,
                      'airconditioner.coolingControlHysteresis'
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="1" class="pl-0">
                <div class="text-body-1 py-4 px-0">℃</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.airconditioner.coolingControlHysteresis,
                      undefined,
                      'airconditioner.coolingControlHysteresis'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row flex-wrap">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('制热设定温度') }}</div>
                <v-tooltip
                  :text="$t('当电池柜温度小于该值时，达到空调启动条件。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="3" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.airconditioner.heatingSetTemp"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.airconditioner.heatingSetTemp,
                      'airconditioner.heatingSetTemp'
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="1" class="pl-0">
                <div class="text-body-1 py-4 px-0">℃</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.airconditioner.heatingSetTemp,
                      undefined,
                      'airconditioner.heatingSetTemp'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div class="flex align-center row flex-wrap">
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('制热控制回差') }}</div>
                <v-tooltip
                  :text="
                    $t(
                      '当电池柜温度大于制热设定温度+制热控制回差时，达到空调关闭条件。'
                    )
                  "
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="3" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.airconditioner.heatingControlHysteresis"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.airconditioner.heatingControlHysteresis,
                      'airconditioner.heatingControlHysteresis'
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="1" class="pl-0">
                <div class="text-body-1 py-4 px-0">℃</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.airconditioner.heatingControlHysteresis,
                      undefined,
                      'airconditioner.heatingControlHysteresis'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="userInfo?.permission_level == 4"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('柜内温度过高点') }}</div>
                <v-tooltip
                  :text="$t('当电池柜内温度大于该值时，触发告警。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="3" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.airconditioner.cabinetTempHighPoint"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.airconditioner.cabinetTempHighPoint,
                      'airconditioner.cabinetTempHighPoint'
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="1" class="pl-0">
                <div class="text-body-1 py-4 px-0">℃</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.airconditioner.cabinetTempHighPoint,
                      undefined,
                      'airconditioner.cabinetTempHighPoint'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="userInfo?.permission_level == 4"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('柜内温度过低点') }}</div>
                <v-tooltip
                  :text="$t('当电池柜内温度小于该值时，触发告警。')"
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="3" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.airconditioner.cabinetTempLowPoint"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.airconditioner.cabinetTempLowPoint,
                      'airconditioner.cabinetTempLowPoint'
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="1" class="pl-0">
                <div class="text-body-1 py-4 px-0">℃</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.airconditioner.cabinetTempLowPoint,
                      undefined,
                      'airconditioner.cabinetTempLowPoint'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="userInfo?.permission_level == 4"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('柜内允许最低温度') }}</div>
                <v-tooltip
                  :text="
                    $t(
                      '系统运行或待机时，都不允许低于该值，低于该值时强制启动空调制热以保证随时能够启动系统。'
                    )
                  "
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="3" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.airconditioner.cabinetAllowMinTemp"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.airconditioner.cabinetAllowMinTemp,
                      'airconditioner.cabinetAllowMinTemp'
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="1" class="pl-0">
                <div class="text-body-1 py-4 px-0">℃</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.airconditioner.cabinetAllowMinTemp,
                      undefined,
                      'airconditioner.cabinetAllowMinTemp'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
            <div
              class="flex align-center row flex-wrap"
              v-if="userInfo?.permission_level == 4"
            >
              <div
                class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
              >
                <div class="mr-1">{{ $t('柜内允许最高温度') }}</div>
                <v-tooltip
                  :text="
                    $t(
                      '系统运行或待机时，都不允许高于该值，高于该值时强制启动空调制冷以保证随时能够启动系统。'
                    )
                  "
                  location="top"
                >
                  <template v-slot:activator="{ props }">
                    <v-icon
                      icon="mdi-help-circle"
                      size="small"
                      v-bind="props"
                    ></v-icon> </template
                ></v-tooltip>
              </div>
              <v-col cols="3" class="pl-0 flex">
                <v-text-field
                  v-model="sendForm.airconditioner.cabinetAllowMaxTemp"
                  variant="outlined"
                  label=""
                  hide-details
                  @click:control="
                    handleShow(
                      $event,
                      sendForm.airconditioner.cabinetAllowMaxTemp,
                      'airconditioner.cabinetAllowMaxTemp'
                    )
                  "
                ></v-text-field>
              </v-col>
              <v-col cols="1" class="pl-0">
                <div class="text-body-1 py-4 px-0">℃</div>
              </v-col>
              <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
                <v-btn
                  @click="
                    handleSendClick(
                      sendForm.airconditioner.cabinetAllowMaxTemp,
                      undefined,
                      'airconditioner.cabinetAllowMaxTemp'
                    )
                  "
                  >{{ $t('下发') }}</v-btn
                >
              </v-col>
            </div>
          </template>
        </v-tabs-window-item>
      </v-tabs-window>
    </div>
  </div>
  <!-- </v-card> -->

  <v-dialog v-model="dialog" width="auto" persistent>
    <v-card width="480" class="pa-4 rounded-lg">
      <v-card-title class="text-center mb-4">{{ $t('系统提示') }}</v-card-title>
      <div class="flex justify-center align-center">
        <v-icon icon="mdi-alert-circle" size="small"></v-icon>
        <div>{{ $t('确认下发该参数？') }}</div>
      </div>
      <div class="d-flex justify-center mt-4">
        <v-btn class="mt-2 mr-4 px-8" height="50" @click="handleCancelClick">{{
          $t('取消')
        }}</v-btn>
        <v-btn
          class="mt-2 px-8"
          height="50"
          :loading="loading"
          color="primary"
          @click="handleConfirmClick"
          >{{ $t('确定') }}</v-btn
        >
      </div>
    </v-card>
  </v-dialog>
  <!-- </div> -->
</template>

<style lang="scss" scoped>
.left {
  flex: 0 0 auto; /* 不可拉伸，宽度不变 */
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容显示出来 */
  text-align: right;
}
.dia-left {
  flex: 0 0 auto; /* 不可拉伸，宽度不变 */
  white-space: nowrap; /* 不换行 */
  overflow: visible; /* 允许内容显示出来 */
  text-align: right;
}
</style>
