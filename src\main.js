/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-07-08 18:15:54
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-18 09:00:33
 * @FilePath: \ems_manage\src\main.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { createApp } from 'vue'
import './style.css'
import App from './App.vue'
import router from './router'
import store from './store'

import { registerVuetify } from './plugins/vuetify'
import { registerGlobalCom } from './plugins/registerGlobalCom'
import 'maotu/dist/style.css';
import 'virtual:uno.css'

import BaseEchart from '@/components/echart/src/base-echart.vue'
import Keyboard from '@/components/keyboard/keyboardIndex.vue'
import GgTitle from '@/components/gg-title'
import Empty from '@/components/empty'

const app = createApp(App)

registerGlobalCom(app)
registerVuetify(app)

app.component('BaseEchart', BaseEchart)
app.component('Keyboard', Keyboard)
app.component('GgTitle', GgTitle)
app.component('Empty', Empty)

app.use(router)
app.use(store)

app.mount('#app').$nextTick(() => {
  const d = document.getElementById('_loading_');
  d?.setAttribute('class', 'loading_ hide');
});
