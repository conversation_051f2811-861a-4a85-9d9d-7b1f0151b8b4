/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-11-04 17:27:34
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-02-20 16:49:43
 * @FilePath: \ems_manage\src\utils\index.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
export const isBetween = (n, start, end) => {
  if (end === undefined) {
    return n >= start;
  }
  return n >= start && n <= end;
}

export const fileToBase64 = (file) => {
  // 返回一个新的Promise对象，用于异步读取文件
  return new Promise((resolve, reject) => {
    // 创建一个FileReader对象用于读取文件
    const reader = new FileReader()

    // 当文件读取成功时，触发onload事件
    reader.onload = (event) => {
      // 将读取到的文件内容作为Promise的resolve值
      resolve(event.target.result)
    }

    // 当文件读取失败时，触发onerror事件
    reader.onerror = reject

    // 读取文件为DataURL格式
    reader.readAsDataURL(file)
  })
}

export const generateUUID = () => {
  let uuid = '',
    random
  for (let i = 0; i < 32; i++) {
    random = (Math.random() * 16) | 0
    if (i === 8 || i === 12 || i === 16 || i === 20) {
      uuid += '-'
    }
    uuid += (i === 12 ? 4 : i === 16 ? (random & 3) | 8 : random).toString(16)
  }
  return uuid
}

export const setLeftWidth = (name) => {
  // 输入验证
  if (!name || typeof name !== 'string' || !name.trim()) {
    console.warn('Invalid class name provided:', name)
    return
  }

  const lefts = document.querySelectorAll(`.${name}`)

  // 如果没有找到任何元素，记录日志并返回
  if (lefts.length === 0) {
    console.warn(`No elements found with class name: ${name}`)
    return
  }

  let maxWidth = Math.max(...Array.from(lefts).map((left) => left.offsetWidth))

  // 将最大宽度应用到所有左侧部分
  lefts.forEach((left) => {
    left.style.width = `${maxWidth}px`
  })
}

export const handleExport = (data, fileName, suffix = '.xlsx') => {
  let blob = new Blob([data]);
  let url = URL.createObjectURL(blob);
  // 重命名文件名称
  let link = document.createElement("a");
  link.setAttribute("href", url);
  link.setAttribute(
    "download",
    `${fileName}${suffix}`
  );
  link.click();
}

export const sortFn = (a, b) => {
  // 如果 a.value 和 b.value 都不是 -1，则按正常顺序排序
  if (a.display_priority != '-1' && b.display_priority != '-1') {
    return a.display_priority - b.display_priority;
  }

  // 如果 a.value 是 -1 并且 b.value 不是，则 b 应该排在前面
  if (a.display_priority == '-1' && b.display_priority != '-1') {
    return 1; // a 排在 b 后面
  }

  // 如果 b.value 是 -1 并且 a.value 不是，则 a 应该排在前面
  if (a.display_priority != '-1' && b.display_priority == '-1') {
    return -1; // a 排在 b 前面
  }

  // 如果两者都是 -1，则保持原样或者也可以进一步处理
  return 0;
}
