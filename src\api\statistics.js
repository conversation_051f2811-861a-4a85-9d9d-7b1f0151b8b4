/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-24 14:26:47
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-12-02 09:14:49
 * @FilePath: \ems_manage\src\api\statistics.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from './index'

export const getPowerData = (queryInfo) => {
  return request({
    url: '/powerData',
    method: 'get',
    params: queryInfo
  })
}

export const exportData = (data) => {
  return request({
    url: '/exportPowerStatistics',
    method: 'post',
    data,
    responseType: "blob",
  })
}

export const analyseExport = (data) => {
  return request({
    url: '/analyseExport',
    method: 'post',
    data,
    responseType: "blob",
  })
}

export const clearEnergy = () => {
  return request({
    url: '/clearEnergy',
    method: 'get'
  })
}