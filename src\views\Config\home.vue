<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-25 18:41:41
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-22 10:02:19
 * @FilePath: \ems_manage\src\views\Config\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { ref, toRefs, watch, getCurrentInstance, computed } from 'vue'
import draggable from 'vuedraggable'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'
import { useDeviceStore } from '@/store/module/device'
import { useI18n } from 'vue-i18n'
import { generateUUID } from '@/utils'

import Line from '../../assets/img/line.webp'

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { homeConfig, isModule, moduleData, pointData, homeCardData } = toRefs(
  useConfigStore()
)
const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const { treeData } = toRefs(useDeviceStore())

const getData = () => {
  useConfigStore().getWebConfigFn({ key: 'homeCard' })
}
getData()
const rightData = computed(() => {
  return homeCardData.value
    .filter((item) => item.is_display == '1')
    .sort((a, b) => a.sort - b.sort)
})
const bottomData = computed(() => {
  return homeCardData.value.filter((item) => item.is_display == '0')
})

const list1 = ref([])
list1.value = homeConfig.value.top

/**
 * 删除、添加卡片
 */
const handleDeleteClick = async (index, type) => {
  if (type == 'bottom') {
    let index1 = homeCardData.value?.findIndex(
      (item) => item.card_id == bottomData.value[index].card_id
    )
    if (index1 !== -1) homeCardData.value.splice(index1, 1)
    await setHomeCardFn()
    snackbar.value = true
    snackbarText.value = t('删除成功')
  } else if (type == 'right') {
    let info = rightData.value[index]
    let index1 = homeCardData.value?.findIndex(
      (item) => item.card_id == info.card_id
    )
    if (index1 !== -1)
      homeCardData.value.splice(index1, 1, { ...info, is_display: '0' })
    await setHomeCardFn()
    // editCardFn([
    //   {
    //     ...info,
    //     is_display: '0'
    //   }
    // ])
  }
}
const editCardFn = (card_infos) => {
  Promise.all(
    card_infos.map((item) =>
      useConfigStore().homeCardEditFn(JSON.stringify(item))
    )
  )
    .then((res) => {
      let isErr = res.some((item1) => {
        if (item1?.msg) {
          snackbar.value = true
          snackbarText.value = item1.msg
          loading.value = false
          return true
        }
      })
      if (!isErr) getData()
    })
    .catch((err) => {
      console.error(err)
    })
}
const handleAddClick = (index) => {
  let info = bottomData.value[index]
  let index1 = homeCardData.value?.findIndex(
    (item) => item.card_id == info.card_id
  )
  if (index1 !== -1)
    homeCardData.value.splice(index1, 1, { ...info, is_display: '1' })
  setHomeCardFn()
}
// 移动卡片
const moveFn = (evt) => {
  rightData.value.forEach((riItem, riIndex) => {
    let index1 = homeCardData.value?.findIndex(
      (item) => item.card_id == riItem.card_id
    )
    if (index1 !== -1)
      homeCardData.value.splice(index1, 1, { ...riItem, sort: riIndex + '' })
  })
  setHomeCardFn()
}

/**
 * 保存
 */
const handleSaveClick = () => {
  homeConfig.value.top = list1.value
  snackbar.value = true
  snackbarText.value = t('保存成功')
}

/**
 * 添加新卡片
 */
const handleAddNewClick = () => {
  isModule.value = false
  form.value = {
    title: '',
    deviceId: undefined,
    moduleId: undefined,
    pointId: undefined,
    is_display: '0',
    project: [],
    formula: undefined,
    unit: undefined,
    chart_type: 'line',
    chart_color: '',
    sort: 0
  }
  dialogTitle.value = t('添加新卡片')
  dialog.value = true
}
const dialog = ref(false)
const dialogTitle = ref(t('添加新卡片'))
const form = ref({
  title: ''
})
const loading = ref(false)
const submit = async () => {
  const { valid } = await proxy.$refs.addForm.validate()
  if (!valid) return
  loading.value = true
  if (dialogTitle.value == t('添加新卡片')) {
    homeCardData.value.push({
      title: form.value.title,
      is_display: form.value.is_display,
      formula: form.value.formula,
      unit: form.value.unit,
      chart_type: form.value.chart_type,
      chart_color: form.value.chart_color,
      device: form.value.deviceId,
      project: form.value.pointId.map((item) => {
        return {
          point_id: item
        }
      }),
      card_id: generateUUID(),
      sort: form.value.sort
    })
  } else {
    let index = homeCardData.value?.findIndex(
      (item) => item.card_id == form.value.card_id
    )
    if (index !== -1)
      homeCardData.value.splice(index, 1, {
        title: form.value.title,
        is_display: form.value.is_display,
        formula: form.value.formula,
        unit: form.value.unit,
        chart_type: form.value.chart_type,
        chart_color: form.value.chart_color,
        device: form.value.deviceId,
        project: form.value.pointId.map((item) => {
          return {
            point_id: item
          }
        }),
        card_id: form.value.card_id,
        sort: form.value.sort
      })
  }
  try {
    await setHomeCardFn()
    snackbar.value = true
    snackbarText.value =
      dialogTitle.value == t('添加新卡片') ? t('添加成功') : t('修改成功')
    loading.value = false
    dialog.value = false
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
    dialog.value = false
  }
}
const setHomeCardFn = async () => {
  let api = 'setWebConfigFn'
  const res = await useConfigStore()[api](
    JSON.stringify({
      key: 'homeCard',
      config: JSON.stringify(homeCardData.value)
    })
  )
  getData()
}
const handleCancelClick = () => {
  loading.value = false
  dialog.value = false
}
// 选择设备
const handleDeviceChange = (e) => {
  if (!e) return
  form.value.moduleId = undefined
  form.value.pointId = undefined
  useConfigStore().getTreePointDataFn({ deviceId: e, dataType: '1' })
}
// 选择模块
const pointDataOptions = computed(() => {
  if (isModule.value) {
    return pointData.value.find((item) => item.id == form.value.moduleId)
      ?.children
  } else {
    return pointData.value
  }
})
const handleModuleChange = (e) => {
  if (!e) return
  form.value.pointId = undefined
}
const handleEditClick = (index) => {
  let info = bottomData.value[index]
  form.value = {
    title: info.title,
    is_display: info.is_display,
    formula: info.formula,
    unit: info.unit,
    chart_type: info.chart_type,
    chart_color: info.chart_color,
    deviceId: info.device,
    pointId: info.project.map((item) => {
      return item.point_id
    }),
    card_id: info.card_id,
    sort: info.sort
  }
  dialogTitle.value = t('修改卡片')
  dialog.value = true
}

const isShowColor = ref(false)

/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value, prop) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  form.value[currentInput.value] = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}

defineExpose({
  getData
})
</script>

<template>
  <div class="w-100 overflow-auto d-flex flex-column">
    <draggable
      class="flex w-100 overflow-auto"
      :list="rightData"
      item-key="card_id"
      @change="moveFn"
      style="display: flex; flex: 1 1 auto"
    >
      <template #item="{ element, index }">
        <v-col cols="3">
          <v-card
            :subtitle="$t('显示实时数据曲线')"
            height="230px"
            elevation="4"
            class="rounded-lg cursor-move"
          >
            <template #title>
              <div class="flex w-100 justify-space-between align-center">
                <div class="text-body-1">{{ element.title }}</div>
                <v-tooltip :text="$t('删除卡片')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      icon="mdi-close"
                      variant="plain"
                      v-bind="props"
                      @click="handleDeleteClick(index, 'right')"
                    ></v-btn>
                  </template>
                </v-tooltip>
              </div>
            </template>
            <div class="flex justify-center h-50 align-center">
              <img :src="Line" style="width: 60px; height: 60px" />
            </div>
          </v-card>
        </v-col>
      </template>
    </draggable>

    <div class="px-2 mt-4">
      <v-card class="pa-4 w-100 rounded-lg no-scrollbar mb-4" elevation="4">
        <div class="d-flex justify-between align-center">
          <div class="text-h6">{{ $t('配置管理') }}</div>
        </div>
      </v-card>
    </div>
    <v-row style="margin: 0 !important">
      <v-col cols="3" v-for="(item, index) in bottomData" :key="item.card_id">
        <v-card
          :subtitle="$t('显示实时数据曲线')"
          height="300px"
          elevation="4"
          class="rounded-lg"
        >
          <template #title>
            <div class="flex w-100 justify-space-between align-center">
              <div class="text-body-1">{{ item.title }}</div>
              <div>
                <v-tooltip :text="$t('添加卡片')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      icon="mdi-plus"
                      variant="plain"
                      v-bind="props"
                      @click="handleAddClick(index)"
                    ></v-btn>
                  </template>
                </v-tooltip>
                <v-tooltip :text="$t('修改卡片')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      icon="mdi-pencil"
                      variant="plain"
                      v-bind="props"
                      @click="handleEditClick(index)"
                    ></v-btn>
                  </template>
                </v-tooltip>
                <v-tooltip :text="$t('删除')" location="top">
                  <template v-slot:activator="{ props }">
                    <v-btn
                      icon="mdi-delete"
                      variant="plain"
                      v-bind="props"
                      @click="handleDeleteClick(index, 'bottom')"
                    ></v-btn>
                  </template>
                </v-tooltip>
              </div>
            </div>
          </template>
          <div class="flex justify-center h-50 align-center">
            <img :src="Line" style="width: 60px; height: 60px" />
          </div>
        </v-card>
      </v-col>
      <v-col cols="3">
        <v-card
          height="300px"
          class="rounded-lg"
          elevation="4"
          @click="handleAddNewClick"
        >
          <v-tooltip :text="$t('添加新卡片')" location="top">
            <template v-slot:activator="{ props }">
              <v-btn
                icon="mdi-plus"
                variant="plain"
                v-bind="props"
                size="60"
                class="h-100 w-100 plus"
              ></v-btn>
            </template>
          </v-tooltip>
        </v-card>
      </v-col>
    </v-row>

    <v-dialog v-model="dialog" width="auto">
      <v-card width="740" class="pa-4 rounded-lg">
        <v-card-title class="text-center mb-6">{{ dialogTitle }}</v-card-title>
        <v-sheet class="mx-auto w-full">
          <v-form fast-fail @submit.prevent="submit" ref="addForm">
            <v-text-field
              v-model="form.title"
              :rules="[(v) => !!v || $t('标题必填')]"
              variant="outlined"
              :label="$t('标题')"
              :placeholder="$t('标题')"
              clearable
              @click:control="handleShow($event, form.title, 'title')"
            ></v-text-field>
            <v-select
              v-model="form.deviceId"
              item-value="id"
              clearable
              :label="$t('选择设备')"
              :items="treeData"
              variant="outlined"
              class="w-100 mr-4 mt-2"
              @update:modelValue="handleDeviceChange"
            ></v-select>
            <v-select
              v-model="form.moduleId"
              item-value="id"
              clearable
              :label="$t('选择模块')"
              :items="moduleData"
              variant="outlined"
              class="w-100 mr-4 mt-2"
              @update:modelValue="handleModuleChange"
              v-if="isModule"
            ></v-select>
            <v-select
              v-model="form.pointId"
              item-value="id"
              clearable
              multiple
              :label="$t('选择属性')"
              :items="pointDataOptions"
              variant="outlined"
              class="w-100 mr-4 mt-2"
            ></v-select>
            <v-text-field
              v-model="form.formula"
              :label="$t('计算公式')"
              :placeholder="$t('x+y+z,x为第一个属性值,y为第二个属性值...')"
              variant="outlined"
              clearable
              append-inner-icon="mdi-calculator-variant"
            ></v-text-field>
            <v-text-field
              v-model="form.unit"
              :label="$t('单位')"
              :placeholder="$t('单位')"
              variant="outlined"
              clearable
            ></v-text-field>
            <v-select
              v-model="form.chart_type"
              item-value="id"
              clearable
              :label="$t('选择图表类型')"
              :items="[
                { id: 'line', title: $t('折线图') },
                { id: 'bar', title: $t('柱状图') }
              ]"
              variant="outlined"
              class="w-100 mr-4 mt-2"
            ></v-select>
            <v-menu
              v-model="isShowColor"
              location="bottom"
              :close-on-content-click="false"
            >
              <template v-slot:activator="{ props }">
                <v-text-field
                  v-model="form.chart_color"
                  :label="$t('图表颜色')"
                  variant="outlined"
                  v-bind="props"
                  clearable
                  append-inner-icon="mdi-palette"
                ></v-text-field>
              </template>

              <v-color-picker v-model="form.chart_color"></v-color-picker>
            </v-menu>
            <div class="d-flex justify-center">
              <v-btn
                class="mt-2 mr-4 px-8"
                height="50"
                @click="handleCancelClick"
                >{{ $t('取消') }}</v-btn
              >
              <v-btn
                class="mt-2 px-8"
                type="submit"
                height="50"
                :loading="loading"
                color="primary"
                >{{ $t('确定') }}</v-btn
              >
            </div>
          </v-form>
        </v-sheet>
      </v-card>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
:deep(.plus) {
  border-radius: 0 !important;
  .v-icon {
    font-size: 50px;
  }
}
</style>
