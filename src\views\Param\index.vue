<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-08-07 15:15:16
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-04-23 19:39:16
 * @FilePath: \ems_manage\src\views\Param\index.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import {
  ref,
  computed,
  toRefs,
  onMounted,
  getCurrentInstance,
  watch
} from 'vue'
import { useParamStore } from '@/store/module/param'
import { useI18n } from 'vue-i18n'
import { useDisplay } from 'vuetify'
import { useUserStore } from '@/store/module/user'
import { useGlobalStore } from '@/store/global'
import { isBetween } from '@/utils'

import { ElDivider } from 'element-plus'

const {
  snackbar,
  snackbarText,
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  keyboardRange,
  isShowKeyboard
} = toRefs(useGlobalStore())
const { userInfo } = toRefs(useUserStore())
const { proxy } = getCurrentInstance()
const { mobile } = useDisplay()
const { t } = useI18n()
const { params } = toRefs(useParamStore())
const getData = () => {
  useParamStore()
    .powerParamsFn()
    .then(() => {
      form.value = {
        active_power: params.value.active_power,
        reactive_power: params.value.reactive_power,
        power_factor: params.value.power_factor,
        high_voltage_main_switch: params.value.high_voltage_main_switch,
        oil_engine_switch: params.value.oil_engine_switch,
        system_switch: params.value.system_switch,
        policy_enabling: params.value.policy_enabling,
        policy_enabling: params.value.policy_enabling,
        mains_switch: params.value.mains_switch,
        double_division_control: params.value.double_division_control
      }
    })
}
getData()

const form = ref({
  active_power: '0',
  reactive_power: '0',
  power_factor: '0',
  high_voltage_main_switch: '0',
  oil_engine_switch: '0',
  system_switch: '0',
  policy_enabling: '0',
  mains_switch: '0',
  double_division_control: '1'
})

const model = computed(() =>
  form.value.policy_enabling == '0' ? t('不生效') : t('生效')
)
const model1 = computed(() =>
  form.value.system_switch == '0' ? t('关机') : t('开机')
)
const model2 = computed(() => {
  return (value) => (value == '0' ? t('关') : t('开'))
})

onMounted(() => {
  const lefts = document.querySelectorAll('.left')
  let maxWidth = 0

  // 计算所有左侧部分的最大宽度
  lefts.forEach((left) => {
    const width = left.offsetWidth
    if (width > maxWidth) {
      maxWidth = width
    }
  })

  // 将最大宽度应用到所有左侧部分
  lefts.forEach((left) => {
    left.style.width = `${maxWidth}px`
  })
})

/**
 * 键盘
 */
keyboardMode.value = 'di_git'
const handleShow = (e, value, prop, range) => {
  if (form.value.policy_enabling == '1') return
  if (isShowKeyboard.value) return
  currentInput.value = prop
  keyboardDialog.value = true
  keyboardInputValue.value = value
  keyboardRange.value = range
}
confirmCall.value = () => {
  // if (keyboardInputValue.value == '') {
  //   snackbar.value = true
  //   snackbarText.value = t('数据不能为空')
  //   return
  // }
  handleSendClick(keyboardInputValue.value, undefined, currentInput.value)
}

const handleSendClick = (value, range, prop) => {
  currentInput.value = prop
  if (value) {
    const regex = /^-?\d+(\.\d+)?$/
    if (!regex.test(value)) {
      snackbar.value = true
      snackbarText.value = t('只能为数字')
      getData()
      loading.value = false
      dialog.value = false
      return
    }
    if (value === '' && isShowKeyboard.value) {
      snackbar.value = true
      snackbarText.value = t('数据不能为空')
      getData()
      loading.value = false
      dialog.value = false
      return
    }
  } else {
    if (value === '' && isShowKeyboard.value) {
      snackbar.value = true
      snackbarText.value = t('数据不能为空')
      getData()
      loading.value = false
      dialog.value = false
      return
    }
  }
  if (range) {
    if (!isBetween(Number(value), range[0], range[1])) {
      snackbar.value = true
      snackbarText.value = t('range', range)
      getData()
      loading.value = false
      dialog.value = false
      return
    }
  }
  let data = {
    user: userInfo.value.user_name
  }
  if (keyboardDialog.value) {
    data[currentInput.value] = keyboardInputValue.value
  } else {
    data[currentInput.value] = value
  }
  useParamStore()
    .powerSetFn(JSON.stringify(data))
    .then((res) => {
      snackbar.value = true
      snackbarText.value = t('下发成功')
      if (keyboardDialog.value) {
        form.value[currentInput.value] = keyboardInputValue.value
        showKeyboard.value = false
        keyboardDialog.value = false
      }
      loading.value = false
      dialog.value = false
      getData()
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
      loading.value = false
    })
}

const handleControlClick = (value, prop) => {
  if (isShowKeyboard.value) return
  currentInput.value = prop
  handleSendClick(value, undefined, currentInput.value)
}
/**
 * 策略不生效时自动下发一次
 */
watch(
  () => form.value.policy_enabling,
  () => {
    if (isShowKeyboard.value) return
    if (form.value.policy_enabling == '0') {
      handleSendClick()
    }
  }
)
/**
 * 确认下发弹框
 */
const dialog = ref(false)
const loading = ref(false)
const dialogData = ref({
  value: '',
  prop: ''
})
const handleConfirmClick = () => {
  loading.value = true
  handleSendClick(dialogData.value.value, undefined, dialogData.value.prop)
}
const handleSendDialogClick = (value, prop) => {
  dialogData.value.value = value
  dialogData.value.prop = prop
  dialog.value = true
}
</script>

<template>
  <div class="pa-6 h-100 overflow-hidden d-flex w-100">
    <v-card
      class="pa-4 h-100 w-100 rounded-lg px-4 overflow-auto no-scrollbar"
      elevation="4"
    >
      <div class="d-flex justify-between align-center px-4 py-2">
        <div class="text-h6">{{ $t('参数设置') }}</div>
      </div>
      <div class="w-100 mt-6 flex flex-wrap h-100">
        <v-col cols="12" lg="6" md="12" sm="12" class="pa-0">
          <div class="flex align-center row flex-wrap">
            <div
              class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
            >
              <div class="mr-1">{{ $t('有功功率') }}</div>
              <v-tooltip :text="$t('设置储能有功功率。')" location="top">
                <template v-slot:activator="{ props }">
                  <v-icon
                    icon="mdi-help-circle"
                    size="small"
                    v-bind="props"
                  ></v-icon> </template
              ></v-tooltip>
            </div>
            <v-col cols="3" class="pl-0 flex">
              <v-text-field
                v-model="form.active_power"
                variant="outlined"
                label=""
                hide-details
                @click:control="
                  handleShow($event, form.active_power, 'active_power')
                "
                :disabled="params.policy_enabling == '1'"
              ></v-text-field>
            </v-col>
            <v-col cols="2" class="pl-0">
              <div class="text-body-1 py-4 px-0">kW</div>
            </v-col>
            <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
              <v-btn
                :disabled="params.policy_enabling == '1'"
                @click="
                  handleSendClick(form.active_power, undefined, 'active_power')
                "
                >{{ $t('下发') }}</v-btn
              >
            </v-col>
          </div>
          <div class="flex align-center row flex-wrap">
            <div
              class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
            >
              <div class="mr-1">{{ $t('无功功率') }}</div>
              <v-tooltip :text="$t('设置储能无功功率。')" location="top">
                <template v-slot:activator="{ props }">
                  <v-icon
                    icon="mdi-help-circle"
                    size="small"
                    v-bind="props"
                  ></v-icon> </template
              ></v-tooltip>
            </div>
            <v-col cols="3" class="pl-0 flex">
              <v-text-field
                v-model="form.reactive_power"
                variant="outlined"
                label=""
                hide-details
                @click:control="
                  handleShow($event, form.reactive_power, 'reactive_power')
                "
                :disabled="params.policy_enabling == '1'"
              ></v-text-field>
            </v-col>
            <v-col cols="2" class="pl-0">
              <div class="text-body-1 py-4 px-0">kvar</div>
            </v-col>
            <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
              <v-btn
                :disabled="params.policy_enabling == '1'"
                @click="
                  handleSendClick(
                    form.reactive_power,
                    undefined,
                    'reactive_power'
                  )
                "
                >{{ $t('下发') }}</v-btn
              >
            </v-col>
          </div>
          <div class="flex align-center row flex-wrap">
            <div
              class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
            >
              <div class="mr-1">{{ $t('功率因数') }}</div>
              <v-tooltip :text="$t('设置储能功率因素。')" location="top">
                <template v-slot:activator="{ props }">
                  <v-icon
                    icon="mdi-help-circle"
                    size="small"
                    v-bind="props"
                  ></v-icon> </template
              ></v-tooltip>
            </div>
            <v-col cols="3" class="pl-0 flex">
              <v-text-field
                v-model="form.power_factor"
                variant="outlined"
                label=""
                hide-details
                @click:control="
                  handleShow($event, form.power_factor, 'power_factor')
                "
                :disabled="params.policy_enabling == '1'"
              ></v-text-field>
            </v-col>
            <v-col cols="2" class="pl-0"> </v-col>
            <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
              <v-btn
                :disabled="params.policy_enabling == '1'"
                @click="
                  handleSendClick(form.power_factor, undefined, 'power_factor')
                "
                >{{ $t('下发') }}</v-btn
              >
            </v-col>
          </div>
        </v-col>
        <el-divider
          :direction="mobile ? 'horizontal' : 'vertical'"
          height="100%"
          :class="{ 'mr-8': !mobile }"
        />
        <v-col cols="12" lg="5" md="12" sm="12" class="pa-0">
          <div class="flex align-center row">
            <div
              class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
            >
              <div class="mr-1">{{ $t('系统开关机') }}</div>
              <v-tooltip
                :text="$t('系统按照指定模式工作或停止运行。')"
                location="top"
              >
                <template v-slot:activator="{ props }">
                  <v-icon
                    icon="mdi-help-circle"
                    size="small"
                    v-bind="props"
                  ></v-icon> </template
              ></v-tooltip>
            </div>
            <v-col cols="4" class="pl-0 flex">
              <v-switch
                v-model="form.system_switch"
                :label="`${model1}`"
                false-value="0"
                true-value="1"
                hide-details
                color="primary"
                @update:modelValue="
                  handleControlClick(form.system_switch, 'system_switch')
                "
              />
            </v-col>
            <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
              <v-btn
                @click="
                  handleSendClick(
                    form.system_switch,
                    undefined,
                    'system_switch'
                  )
                "
                >{{ $t('下发') }}</v-btn
              >
            </v-col>
          </div>
          <!-- 高压总开关 -->
          <div class="flex align-center row">
            <div
              class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
            >
              <div class="mr-1">{{ $t('高压总开关') }}</div>
              <v-tooltip :text="$t('控制电池高压开关状态。')" location="top">
                <template v-slot:activator="{ props }">
                  <v-icon
                    icon="mdi-help-circle"
                    size="small"
                    v-bind="props"
                  ></v-icon> </template
              ></v-tooltip>
            </div>
            <v-col cols="4" class="pl-0 flex">
              <!-- <v-switch
                v-model="form.high_voltage_main_switch"
                :label="`${model2(form.high_voltage_main_switch)}`"
                false-value="0"
                true-value="1"
                hide-details
                color="primary"
                @update:modelValue="
                  handleControlClick(
                    form.high_voltage_main_switch,
                    'high_voltage_main_switch'
                  )
                "
              /> -->
              <v-btn
                class="mr-2"
                @click="handleSendDialogClick('1', 'high_voltage_main_switch')"
                >{{ $t('开') }}</v-btn
              >
              <v-btn
                @click="handleSendDialogClick('0', 'high_voltage_main_switch')"
                >{{ $t('关') }}</v-btn
              >
            </v-col>
          </div>
          <!-- 油机开关 -->
          <div class="flex align-center row">
            <div
              class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
            >
              <div class="mr-1">{{ $t('油机开关') }}</div>
              <v-tooltip :text="$t('手动开关油机信号。')" location="top">
                <template v-slot:activator="{ props }">
                  <v-icon
                    icon="mdi-help-circle"
                    size="small"
                    v-bind="props"
                  ></v-icon> </template
              ></v-tooltip>
            </div>
            <v-col cols="4" class="pl-0 flex">
              <!-- <v-switch
                v-model="form.oil_engine_switch"
                :label="`${model2(form.oil_engine_switch)}`"
                false-value="0"
                true-value="1"
                hide-details
                color="primary"
                @update:modelValue="
                  handleControlClick(
                    form.oil_engine_switch,
                    'oil_engine_switch'
                  )
                "
              /> -->
              <v-btn
                @click="handleSendDialogClick('1', 'oil_engine_switch')"
                class="mr-2"
                >{{ $t('执行') }}</v-btn
              >
              <!-- <v-btn @click="handleSendDialogClick('0', 'oil_engine_switch')">{{
                $t('关')
              }}</v-btn> -->
            </v-col>
          </div>
          <!-- 市电开关 -->
          <div class="flex align-center row">
            <div
              class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
            >
              <div class="mr-1">{{ $t('市电开关') }}</div>
              <v-tooltip :text="$t('手动开关市电信号。')" location="top">
                <template v-slot:activator="{ props }">
                  <v-icon
                    icon="mdi-help-circle"
                    size="small"
                    v-bind="props"
                  ></v-icon> </template
              ></v-tooltip>
            </div>
            <v-col cols="4" class="pl-0 flex">
              <v-btn
                @click="handleSendDialogClick('1', 'mains_switch')"
                class="mr-2"
                >{{ $t('执行') }}</v-btn
              >
              <!-- <v-btn @click="handleSendDialogClick('0', 'mains_switch')">{{
                $t('关')
              }}</v-btn> -->
            </v-col>
          </div>
          <!-- 双分控制 -->
          <div class="flex align-center row">
            <div
              class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
            >
              <div class="mr-1">{{ $t('双分控制') }}</div>
              <v-tooltip
                :text="$t('油机信号和市电信号都断开。')"
                location="top"
              >
                <template v-slot:activator="{ props }">
                  <v-icon
                    icon="mdi-help-circle"
                    size="small"
                    v-bind="props"
                  ></v-icon> </template
              ></v-tooltip>
            </div>
            <v-col cols="4" class="pl-0 flex">
              <v-btn
                @click="handleSendDialogClick('1', 'double_division_control')"
                class="mr-2"
                >{{ $t('执行') }}</v-btn
              >
            </v-col>
          </div>
          <div class="flex align-center row">
            <div
              class="text-body-1 pb-2 pt-2 pr-4 flex justify-end align-center left"
            >
              <div class="mr-1">{{ $t('策略启用') }}</div>
              <v-tooltip :text="$t('运行策略是否生效。')" location="top">
                <template v-slot:activator="{ props }">
                  <v-icon
                    icon="mdi-help-circle"
                    size="small"
                    v-bind="props"
                  ></v-icon> </template
              ></v-tooltip>
            </div>
            <v-col cols="4" class="pl-0 flex">
              <v-switch
                v-model="form.policy_enabling"
                :label="`${model}`"
                false-value="0"
                true-value="1"
                hide-details
                color="primary"
                @update:modelValue="
                  handleControlClick(form.policy_enabling, 'policy_enabling')
                "
              />
            </v-col>
            <v-col cols="1" class="pl-0" v-if="isShowKeyboard">
              <v-btn
                @click="
                  handleSendClick(
                    form.policy_enabling,
                    undefined,
                    'policy_enabling'
                  )
                "
                >{{ $t('下发') }}</v-btn
              >
            </v-col>
          </div>
        </v-col>
      </div>
    </v-card>

    <v-dialog v-model="dialog" width="auto">
      <v-card width="480" class="pa-4 rounded-lg">
        <v-card-title class="text-center mb-6">{{
          $t('系统提示')
        }}</v-card-title>
        <div class="flex justify-center align-center">
          <v-icon icon="mdi-alert-circle" size="small"></v-icon>
          <div>{{ $t('确认下发该参数？') }}</div>
        </div>
        <div class="d-flex justify-center mt-4">
          <v-btn class="mt-2 mr-4 px-8" height="50" @click="dialog = false">{{
            $t('取消')
          }}</v-btn>
          <v-btn
            class="mt-2 px-8"
            height="50"
            :loading="loading"
            color="primary"
            @click="handleConfirmClick"
            >{{ $t('确定') }}</v-btn
          >
        </div>
      </v-card>
    </v-dialog>
  </div>
</template>

<style lang="scss" scoped>
.left {
  flex: 0 0 auto;
  /* 不可拉伸，宽度不变 */
  white-space: nowrap;
  /* 不换行 */
  overflow: visible;
  /* 允许内容显示出来 */
  text-align: right;
}

.right {
  width: 20%;
  flex: 0 0 auto;
  /* 不可拉伸，宽度不变 */
}

:deep(.el-divider--vertical) {
  border-left: 1px #0093b6 solid;
  display: inline-block;
  height: 90%;
  margin: 0 8px;
  position: relative;
  vertical-align: middle;
  width: 1px;
}

:deep(.el-divider--horizontal) {
  border-top: 1px #0093b6 solid;
}
</style>
