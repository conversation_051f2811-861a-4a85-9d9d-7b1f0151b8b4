<!--
 * HMI优化卡片组件
 * 针对10寸触摸屏优化的卡片组件
-->
<template>
  <v-card
    :class="[
      'hmi-card',
      variant === 'elevated' ? 'hmi-card-elevated' : '',
      variant === 'outlined' ? 'hmi-card-outlined' : '',
      variant === 'flat' ? 'hmi-card-flat' : '',
      clickable ? 'hmi-card-clickable hmi-touch-friendly' : '',
      size === 'small' ? 'hmi-card-small' : '',
      size === 'large' ? 'hmi-card-large' : ''
    ]"
    :elevation="computedElevation"
    :variant="variant"
    :color="color"
    :height="height"
    :width="width"
    :max-height="maxHeight"
    :max-width="maxWidth"
    :min-height="minHeight"
    :min-width="minWidth"
    v-bind="$attrs"
    @click="handleClick"
  >
    <!-- 卡片标题 -->
    <v-card-title
      v-if="title || $slots.title"
      :class="[
        'hmi-card-title',
        titleClass
      ]"
    >
      <slot name="title">
        <div class="d-flex align-center justify-space-between">
          <div class="d-flex align-center">
            <v-icon
              v-if="titleIcon"
              :icon="titleIcon"
              :color="titleIconColor"
              class="me-2"
            ></v-icon>
            <span>{{ title }}</span>
          </div>
          <div v-if="$slots.actions" class="hmi-card-actions">
            <slot name="actions"></slot>
          </div>
        </div>
      </slot>
    </v-card-title>

    <!-- 卡片副标题 -->
    <v-card-subtitle
      v-if="subtitle || $slots.subtitle"
      :class="[
        'hmi-card-subtitle',
        subtitleClass
      ]"
    >
      <slot name="subtitle">{{ subtitle }}</slot>
    </v-card-subtitle>

    <!-- 卡片内容 -->
    <v-card-text
      v-if="$slots.default"
      :class="[
        'hmi-card-content',
        contentClass
      ]"
    >
      <slot></slot>
    </v-card-text>

    <!-- 卡片操作区 -->
    <v-card-actions
      v-if="$slots.footer"
      :class="[
        'hmi-card-footer',
        footerClass
      ]"
    >
      <slot name="footer"></slot>
    </v-card-actions>
  </v-card>
</template>

<script setup>
import { computed } from 'vue'
import { useDisplay } from 'vuetify'

const props = defineProps({
  title: {
    type: String,
    default: ''
  },
  subtitle: {
    type: String,
    default: ''
  },
  titleIcon: {
    type: String,
    default: ''
  },
  titleIconColor: {
    type: String,
    default: 'primary'
  },
  variant: {
    type: String,
    default: 'elevated',
    validator: (value) => ['elevated', 'outlined', 'flat'].includes(value)
  },
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  elevation: {
    type: [Number, String],
    default: 4
  },
  color: {
    type: String,
    default: ''
  },
  clickable: {
    type: Boolean,
    default: false
  },
  height: {
    type: [Number, String],
    default: undefined
  },
  width: {
    type: [Number, String],
    default: undefined
  },
  maxHeight: {
    type: [Number, String],
    default: undefined
  },
  maxWidth: {
    type: [Number, String],
    default: undefined
  },
  minHeight: {
    type: [Number, String],
    default: undefined
  },
  minWidth: {
    type: [Number, String],
    default: undefined
  },
  titleClass: {
    type: String,
    default: ''
  },
  subtitleClass: {
    type: String,
    default: ''
  },
  contentClass: {
    type: String,
    default: ''
  },
  footerClass: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['click'])

const { mobile } = useDisplay()

const computedElevation = computed(() => {
  if (props.variant === 'flat') return 0
  if (props.variant === 'outlined') return 0
  return props.elevation
})

const handleClick = (event) => {
  if (props.clickable) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
.hmi-card {
  border-radius: 12px !important;
  transition: all 0.3s ease !important;
  
  /* 基础样式 */
  &.hmi-card-elevated {
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
  }
  
  &.hmi-card-outlined {
    border: 2px solid rgba(0, 0, 0, 0.12) !important;
  }
  
  &.hmi-card-flat {
    background: transparent !important;
  }
}

/* 可点击卡片 */
.hmi-card-clickable {
  cursor: pointer !important;
  
  &:hover {
    transform: translateY(-2px) !important;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
  }
  
  &:active {
    transform: translateY(0) !important;
  }
}

/* 尺寸变体 */
.hmi-card-small {
  .hmi-card-title {
    font-size: 16px !important;
    padding: 12px 16px 8px !important;
  }
  
  .hmi-card-subtitle {
    font-size: 13px !important;
    padding: 0 16px 8px !important;
  }
  
  .hmi-card-content {
    font-size: 14px !important;
    padding: 8px 16px !important;
  }
  
  .hmi-card-footer {
    padding: 8px 16px 12px !important;
  }
}

.hmi-card-large {
  .hmi-card-title {
    font-size: 22px !important;
    padding: 24px 24px 12px !important;
  }
  
  .hmi-card-subtitle {
    font-size: 16px !important;
    padding: 0 24px 12px !important;
  }
  
  .hmi-card-content {
    font-size: 18px !important;
    padding: 12px 24px !important;
  }
  
  .hmi-card-footer {
    padding: 12px 24px 24px !important;
  }
}

/* 卡片标题 */
.hmi-card-title {
  font-size: 18px !important;
  font-weight: 600 !important;
  color: rgba(0, 0, 0, 0.87) !important;
  padding: 20px 20px 12px !important;
  line-height: 1.3 !important;
  
  .v-icon {
    font-size: 24px !important;
  }
}

/* 卡片副标题 */
.hmi-card-subtitle {
  font-size: 14px !important;
  font-weight: 500 !important;
  color: rgba(0, 0, 0, 0.6) !important;
  padding: 0 20px 12px !important;
  line-height: 1.4 !important;
}

/* 卡片内容 */
.hmi-card-content {
  font-size: 16px !important;
  font-weight: 400 !important;
  color: rgba(0, 0, 0, 0.8) !important;
  padding: 12px 20px !important;
  line-height: 1.5 !important;
}

/* 卡片操作区 */
.hmi-card-footer {
  padding: 12px 20px 20px !important;
  
  :deep(.v-btn) {
    margin-right: 8px !important;
    
    &:last-child {
      margin-right: 0 !important;
    }
  }
}

/* 卡片操作按钮 */
.hmi-card-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  
  :deep(.v-btn) {
    min-width: auto !important;
    padding: 8px 12px !important;
  }
  
  :deep(.v-icon) {
    font-size: 20px !important;
  }
}

/* 移动端优化 */
@media screen and (max-width: 1024px) {
  .hmi-card {
    border-radius: 8px !important;
  }
  
  .hmi-card-title {
    font-size: 16px !important;
    padding: 16px 16px 8px !important;
    
    .v-icon {
      font-size: 20px !important;
    }
  }
  
  .hmi-card-subtitle {
    font-size: 13px !important;
    padding: 0 16px 8px !important;
  }
  
  .hmi-card-content {
    font-size: 14px !important;
    padding: 8px 16px !important;
  }
  
  .hmi-card-footer {
    padding: 8px 16px 16px !important;
  }
  
  .hmi-card-small {
    .hmi-card-title {
      font-size: 14px !important;
      padding: 12px 12px 6px !important;
    }
    
    .hmi-card-content {
      font-size: 13px !important;
      padding: 6px 12px !important;
    }
  }
  
  .hmi-card-large {
    .hmi-card-title {
      font-size: 18px !important;
      padding: 20px 20px 10px !important;
    }
    
    .hmi-card-content {
      font-size: 16px !important;
      padding: 10px 20px !important;
    }
  }
}
</style>
