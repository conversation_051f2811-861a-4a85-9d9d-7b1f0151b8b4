<script setup>
import {
  ref,
  computed,
  watch,
  toRefs,
  reactive,
  onUnmounted,
  getCurrentInstance,
  nextTick
} from 'vue'
import { useDeviceStore } from '@/store/module/device'
import { isEmpty } from 'lodash-es'
import { useI18n } from 'vue-i18n'
import { useDisplay } from 'vuetify'
import { useGlobalStore } from '@/store/global'
import { useConfigStore } from '@/store/module/config'
import { useRouter, useRoute } from 'vue-router'

import Alarm from './alarm.vue'
import List from './list.vue'
import BatteryList from './batteryList.vue'
import Data from './data.vue'

const { proxy } = getCurrentInstance()
const {
  showKeyboard,
  currentInput,
  confirmCall,
  keyboardDialog,
  keyboardInputValue,
  keyboardMode,
  isShowKeyboard
} = toRefs(useGlobalStore())
const { mobile } = useDisplay()
const { t } = useI18n()
const { treeData, deviceData } = toRefs(useDeviceStore())
const { cycleTime } = toRefs(useConfigStore())

const isUnmounted = ref(false)
const deviceListTime = ref(null)
useDeviceStore().getDeviceListFn()
deviceListTime.value = setInterval(() => {
  if (isUnmounted.value) {
    // 如果组件已卸载，则不再执行逻辑
    clearInterval(time.value)
    return
  }
  useDeviceStore().getDeviceListFn()
}, cycleTime.value)

const route = useRoute()
const router = useRouter()
const tab = ref(1)
const handleTabChange = () => {
  if (tab.value == 3) {
    nextTick(() => {
      proxy.$refs.alarmRef.getData()
    })
  }
}
const treeDataCom = computed(() =>
  treeData.value.filter((item) => item.device_hide == 0)
)
const items = computed(() => {
  return [
    {
      id: 0,
      title: t('采集器'),
      children: treeData.value
    }
  ]
})

const search = ref(null)
const caseSensitive = ref(false)
const filteredItems = computed(() => {
  const searchValue = caseSensitive.value
    ? search.value
    : search.value?.toLowerCase()
  const filterItems = (items) => {
    return items.filter((item) => {
      if (item.children) {
        item.children = filterItems(item.children)
      }
      return (
        item.title.includes(searchValue) ||
        (item.children && item.children.length)
      )
    })
  }

  return search.value
    ? filterItems(JSON.parse(JSON.stringify(items.value)))
    : items.value
})
const activated = ref([0])
const time = ref(null)
const isBattery = ref(false)
watch(activated, () => {
  moduleItem.isModule = false
  moduleItem.key = ''
  if (activated.value[0] != 0) tab.value = 1
  clearInterval(time.value)
  useDeviceStore()
    .getDeviceListFn()
    .then(() => {
      isBattery.value = false
      if (currentNode.value?.id !== 0) {
        useDeviceStore().getDeviceDataFn({ deviceId: currentNode.value?.id })
      }
      time.value = setInterval(() => {
        if (isUnmounted.value) {
          // 如果组件已卸载，则不再执行逻辑
          clearInterval(time.value)
          return
        }
        if (currentNode.value?.id !== 0)
          useDeviceStore().getDeviceDataFn({
            deviceId: currentNode.value?.id
          })
      }, cycleTime.value)
    })
})

const currentNode = computed(() => {
  let node = null
  const filterItems = (items) => {
    return items.filter((item) => {
      if (activated.value[0] == item.id) {
        localStorage.setItem('currentNode', JSON.stringify(item))
        node = item
      }
      if (item.children && item.children.length) {
        filterItems(item.children)
      }
    })
  }
  filterItems(items.value)

  return node ? node : JSON.parse(localStorage.getItem('currentNode'))
})

const currentData = computed(() => {
  return moduleItem.isModule
    ? deviceData.value[currentNode.value.id][moduleItem.key]
    : deviceData.value[currentNode.value.id]
})

const moduleItem = reactive({
  isModule: false,
  key: ''
})
const cellData = ref()
const handleDeviceItemClick = (type, key, item) => {
  const noCellFn = () => {
    if (!type) {
      deviceId.value = key
      activated.value = [key]
    }
    moduleItem.isModule = type
    moduleItem.key = key
  }
  if (isEmpty(item)) {
    noCellFn()
  } else {
    if (!isEmpty(item.cellData)) {
      cellData.value = item.cellData
      isBattery.value = true
    } else {
      noCellFn()
    }
  }
}

onUnmounted(() => {
  isUnmounted.value = true
  if (time.value) {
    clearInterval(time.value)
    clearInterval(deviceListTime.value)
  }
})

const deviceId = ref(0)
const handleDeviceChange = (e) => {
  if (e < 0) return
  activated.value = [e]
}
const handleBackClick = () => {
  if (currentData.value?.status || currentData.value?.analog) {
    if (moduleItem.isModule) {
      activated.value = [deviceId.value]
    } else {
      activated.value = [0]
    }
  } else {
    activated.value = [0]
    deviceId.value = 0
  }
}

const handleBatteryBackClick = () => {
  isBattery.value = false
}

/**
 * 键盘
 */
keyboardMode.value = 'en'
const handleShow = (e, value) => {
  if (isShowKeyboard.value) return
  keyboardDialog.value = true
  keyboardInputValue.value = value
}
confirmCall.value = () => {
  search.value = keyboardInputValue.value
  showKeyboard.value = false
  keyboardDialog.value = false
}
</script>

<template>
  <div
    class="w-100 px-4 d-flex"
    :class="[mobile && 'flex-column', !mobile && 'h-100']"
  >
    <v-col cols="12" lg="3" md="3" sm="12" xs="12" class="h-100" v-if="!mobile">
      <v-card elevation="4" class="rounded-lg h-full">
        <div class="d-flex justify-between align-center px-4 py-2">
          <div class="text-h6">{{ $t('设备概览') }}</div>
        </div>
        <v-sheet class="px-4 bg-primary-lighten-2">
          <v-text-field
            v-model="search"
            clear-icon="mdi-close-circle-outline"
            :label="$t('搜索')"
            clearable
            dark
            flat
            hide-details
            solo-inverted
            @click:control="handleShow($event, search)"
          ></v-text-field>
          <v-checkbox
            v-model="caseSensitive"
            :label="$t('区分大小写搜索')"
            dark
            hide-details
          ></v-checkbox>
        </v-sheet>
        <v-treeview
          :items="filteredItems"
          active-strategy="single-independent"
          activatable
          item-value="id"
          open-all
          v-model:activated="activated"
          class="h-80% overflow-auto no-scrollbar"
        ></v-treeview>
      </v-card>
    </v-col>
    <div class="px-4 mt-2" v-else>
      <v-card class="px-4 w-100 rounded-lg no-scrollbar" elevation="4">
        <div class="d-flex justify-between align-center">
          <div class="text-h6">{{ $t('设备概览') }}</div>
          <div class="d-flex align-center mt-4">
            <v-select
              v-model="deviceId"
              item-value="id"
              clearable
              :label="$t('选择设备')"
              :items="[
                {
                  id: 0,
                  title: $t('采集器')
                },
                ...treeDataCom
              ]"
              variant="outlined"
              class="w-200px"
              @update:modelValue="handleDeviceChange"
            ></v-select>
          </div></div
      ></v-card>
    </div>

    <v-col cols="12" lg="9" md="12" sm="12" xs="12" class="h-100">
      <div class="my-2 flex justify-between">
        <gg-title
          >{{ currentNode?.title
          }}{{ moduleItem.isModule ? '_' + moduleItem.key : '' }}</gg-title
        >
        <div class="mr-2">
          {{ currentData?.analog ? currentData?.analog[0].time : '' }}
        </div>
      </div>
      <v-card
        elevation="4"
        class="rounded-lg"
        style="height: calc(100% - 44px)"
      >
        <v-tabs
          v-model="tab"
          style="height: 48px"
          bg-color="secondary"
          show-arrows
          @update:modelValue="handleTabChange"
        >
          <v-tab :value="1">{{
            !currentData?.status ? $t('设备列表') : $t('设备详情')
          }}</v-tab>
          <v-tab :value="3" v-if="currentNode?.id == 0">{{
            $t('故障告警')
          }}</v-tab>
        </v-tabs>
        <v-tabs-window v-model="tab" style="height: calc(100% - 48px)">
          <v-tabs-window-item
            :value="1"
            class="h-full overflow-auto"
            v-if="!isBattery"
          >
            <Data
              v-model:currentData="currentData"
              v-model:deviceName="currentNode.title"
              v-if="currentData?.status || currentData?.analog"
            ></Data>
            <list
              v-model:deviceId="currentNode.id"
              @deviceItemClick="handleDeviceItemClick"
              v-else
            ></list>
            <empty v-if="isEmpty(currentData) && currentNode?.id !== 0" />
          </v-tabs-window-item>
          <v-tabs-window-item :value="1" class="h-full overflow-auto" v-else
            ><BatteryList
              :cellData="cellData"
              @backClick="handleBatteryBackClick"
          /></v-tabs-window-item>
          <v-tabs-window-item :value="3" class="overflow-auto no-scrollbar">
            <Alarm ref="alarmRef"></Alarm>
          </v-tabs-window-item>
        </v-tabs-window>
      </v-card>
    </v-col>
  </div>

  <v-fab
    color="primary"
    icon="mdi-undo-variant"
    v-if="currentNode?.id !== 0 && !isBattery"
    size="x-large"
    @click="handleBackClick"
    style="bottom: 80px; right: 20px; position: fixed"
  ></v-fab>
</template>

<style lang="scss" scoped>
:deep(.v-window__container) {
  height: 100% !important;
}
:deep(.v-img) {
  height: 600px !important;
}
</style>
