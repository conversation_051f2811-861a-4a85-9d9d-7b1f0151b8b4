<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-10 17:56:39
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String // #C4EBAD
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    version="1.1"
    width="15.999666213989258"
    height="12.856000900268555"
    viewBox="0 0 15.999666213989258 12.856000900268555"
  >
    <g>
      <path
        d="M15.9317,7.3912L14.5901,1.452C14.3454,0.625386,13.6055,0.0433038,12.7445,0L3.39655,0C2.53516,0.0481309,1.78384,0.601335,1.48215,1.4096L1.48215,1.4464C1.48215,1.4896,0.411747,5.8032,0.0981473,7.3904C-0.0913322,8.01199,-0.00388092,8.68507,0.338147,9.2376C0.680192,9.6908,1.19443,9.98237,1.75895,10.0432L14.2517,10.0432C14.827,9.98633,15.3465,9.67491,15.6677,9.1944C15.9848,8.63403,16.0791,7.97477,15.9317,7.348L15.9317,7.3912ZM1.77735,9.1512C1.47356,9.10725,1.19711,8.95149,1.00215,8.7144C0.814081,8.3733,0.778362,7.96877,0.903747,7.6C1.23015,6.1168,2.19655,2.136,2.30055,1.704C2.46364,1.22816,2.89979,0.899377,3.40215,0.8736L12.7445,0.8736C13.2165,0.9056,13.6181,1.2272,13.7533,1.68L15.0949,7.5944C15.1797,7.9728,15.1301,8.3688,14.9533,8.7144C14.7714,8.97573,14.4779,9.13725,14.1597,9.1512L1.77735,9.1512ZM8.61495,11.6928L11.2853,12.2408L11.2853,12.856L4.80535,12.856L4.80535,12.2712L7.38375,11.6928L7.38375,10.536L8.61495,10.536L8.61495,11.6928ZM2.86695,1.6432L3.56295,1.1936L7.18695,1.1936L7.71015,1.6616L7.71015,3.7112L7.15015,4.2712L2.87335,4.2712L2.41175,3.6552L2.87335,1.6496L2.86695,1.6432ZM2.05575,5.3352L2.86775,4.628L7.09575,4.628L7.71175,5.3296L7.71175,7.9016L7.05895,8.5176L2.05495,8.5176L1.51895,7.816L2.05495,5.3168L2.05495,5.3352L2.05575,5.3352ZM13.2237,1.6432L13.5869,3.6552L13.1253,4.2712L8.81735,4.2712L8.25735,3.7352L8.25735,1.6432L8.80455,1.1936L12.5341,1.1936L13.2237,1.6432ZM13.9373,5.3352L14.4733,7.8344L13.9373,8.536L8.93415,8.536L8.28215,7.92L8.28215,5.3296L8.89735,4.628L13.1253,4.628L13.9373,5.3296L13.9373,5.3352Z"
        :fill="fill"
        style="mix-blend-mode: passthrough"
      />
    </g>
  </svg>
</template>
