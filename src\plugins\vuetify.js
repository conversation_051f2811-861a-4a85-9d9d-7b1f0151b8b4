// Vuetify
import 'vuetify/styles'
import { createVuetify } from 'vuetify'
import * as components from 'vuetify/components'
import * as directives from 'vuetify/directives'
import '@mdi/font/css/materialdesignicons.css'

import { createVueI18nAdapter } from 'vuetify/locale/adapters/vue-i18n'
import { useI18n } from 'vue-i18n'
import { i18n } from '@/locale'

const vuetify = createVuetify({
  components,
  directives,
  variations: {
    colors: ['primary', 'secondary'],
    lighten: 2,
    darken: 2,
  },
  theme: {
    themes: {
      light: {
        dark: false,
        colors: {
          'primary': '#0093b6',
          'secondary': '#0093b6',
          'success': '#28c79c',
          'info': '#16B1FF',
          'warning': '#ffaa43',
          'error': '#ff6b6b',
          'background': '#fff',
          'surface': '#fff'
        },

        variables: {
          'code-color': '#d400ff',
          'overlay-scrim-background': '#2E263D',
          'tooltip-background': '#1A0E33',
          'overlay-scrim-opacity': 0.5,
          'hover-opacity': 0.04,
          'focus-opacity': 0.1,
          'selected-opacity': 0.08,
          'activated-opacity': 0.16,
          'pressed-opacity': 0.14,
          'dragged-opacity': 0.1,
          'disabled-opacity': 0.4,
          'border-color': '#2E263D',
          'border-opacity': 0.12,
          'table-header-color': '#F6F7FB',
          'high-emphasis-opacity': 0.9,
          'medium-emphasis-opacity': 0.7,

          // 👉 shadows
          'shadow-key-umbra-color': '#2E263D',
          'shadow-xs-opacity': '0.16',
          'shadow-sm-opacity': '0.18',
          'shadow-md-opacity': '0.20',
          'shadow-lg-opacity': '0.22',
          'shadow-xl-opacity': '0.24',
        },
      },

      // dark: {
      //   dark: true,
      //   colors: {
      //     'primary': '#397dac',
      //     'secondary': '#195b97',
      //     'success': '#56CA00',
      //     'success-darken-1': '#4DB600',
      //     'on-success': '#fff',
      //     'info': '#16B1FF',
      //     'info-darken-1': '#149FE6',
      //     'on-info': '#fff',
      //     'warning': '#FFB400',
      //     'warning-darken-1': '#E6A200',
      //     'on-warning': '#fff',
      //     'error': '#FF4C51',
      //     'error-darken-1': '#E64449',
      //     'on-error': '#fff',
      //     'background': '#28243D',
      //     'on-background': '#E7E3FC',
      //     'surface': '#312d4b',
      //     'on-surface': '#E7E3FC',
      //     'grey-50': '#2A2E42',
      //     'grey-100': '#2F3349',
      //     'grey-200': '#4A5072',
      //     'grey-300': '#5E6692',
      //     'grey-400': '#7983BB',
      //     'grey-500': '#8692D0',
      //     'grey-600': '#AAB3DE',
      //     'grey-700': '#B6BEE3',
      //     'grey-800': '#CFD3EC',
      //     'grey-900': '#E7E9F6',
      //     'perfect-scrollbar-thumb': '#4a5072',
      //     'skin-bordered-background': '#312d4b',
      //     'skin-bordered-surface': '#312d4b',
      //     'expansion-panel-text-custom-bg': '#373350',
      //     'track-bg': '#474360',
      //     'chat-bg': '#373452',
      //   },

      //   variables: {
      //     'code-color': '#d400ff',
      //     'overlay-scrim-background': '#312D4B',
      //     'tooltip-background': '#F7F4FF',
      //     'overlay-scrim-opacity': 0.5,
      //     'hover-opacity': 0.04,
      //     'focus-opacity': 0.1,
      //     'selected-opacity': 0.08,
      //     'activated-opacity': 0.16,
      //     'pressed-opacity': 0.14,
      //     'disabled-opacity': 0.4,
      //     'dragged-opacity': 0.1,
      //     'border-color': '#E7E3FC',
      //     'border-opacity': 0.12,
      //     'table-header-color': '#3D3759',
      //     'high-emphasis-opacity': 0.9,
      //     'medium-emphasis-opacity': 0.7,

      //     // 👉 Shadows
      //     'shadow-key-umbra-color': '#131120',
      //     'shadow-xs-opacity': '0.20',
      //     'shadow-sm-opacity': '0.22',
      //     'shadow-md-opacity': '0.24',
      //     'shadow-lg-opacity': '0.26',
      //     'shadow-xl-opacity': '0.28',
      //   },
      // },
    }
  },
  locale: {
    adapter: createVueI18nAdapter({ i18n, useI18n }),
  },
})

export const registerVuetify = (app) => {
  app.use(i18n)
  app.use(vuetify)
}