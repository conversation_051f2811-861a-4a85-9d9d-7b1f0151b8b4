<script setup>
import { ref, toRefs, getCurrentInstance } from 'vue'
import { useConfigStore } from '@/store/module/config'
import { useGlobalStore } from '@/store/global'
import { useDeviceStore } from '@/store/module/device'
import { generateUUID } from '@/utils'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()
const { proxy } = getCurrentInstance()
const { deviceConfigData } = toRefs(useConfigStore())
const { snackbar, snackbarText } = toRefs(useGlobalStore())
const { treeData } = toRefs(useDeviceStore())
const headers = ref([
  { title: t('设备名称'), key: 'deviceName' },
  { title: t('模块类型'), key: 'moduleType' },
  { title: t('配置'), key: 'config' },
  { title: t('操作'), key: 'action' }
])
const typeData = ref([{ id: 'BatCell', title: 'BatCell' }])

const getData = () => {
  useConfigStore()
    .getWebConfigFn({
      key: 'device'
    })
    .catch((error) => {
      snackbar.value = true
      snackbarText.value = error
    })
}

const dialog = ref(false)
const dialogTitle = ref(t('添加配置'))
const loading = ref(false)
const form = ref({
  deviceId: '',
  moduleType: '',
  deviceType: '',
  deviceName: '',
  config: {
    packNumber: ''
  }
})
const handleAddClick = () => {
  dialogTitle.value = t('添加配置')
  form.value = {
    deviceId: '',
    moduleType: '',
    deviceType: '',
    deviceName: '',
    config: {
      packNumber: ''
    }
  }
  dialog.value = true
}
const handleEditClick = (item) => {
  dialogTitle.value = t('修改配置')
  form.value = {
    deviceId: item.deviceId,
    moduleType: item.moduleType,
    deviceType: treeData.value.find((item1) => item1.id == item.id),
    deviceName: item.deviceName,
    config: item.config,
    id: item.id
  }
  dialog.value = true
}
const handleRemoveClick = async (item) => {
  let index = deviceConfigData.value.findIndex((item1) => item1.id == item.id)
  if (index !== -1) deviceConfigData.value.splice(index, 1)
  await setDeviceConfig()
  snackbar.value = true
  snackbarText.value = t('删除成功')
  getData()
}
const handleDeviceChange = (e) => {
  if (!e) return
  let value = treeData.value.find((item) => item.id == e)
  form.value.deviceType = value.device_class
  form.value.deviceName = value.device_name
}
const submit = async () => {
  const { valid } = await proxy.$refs.FormRef.validate()
  if (!valid) return
  loading.value = true
  if (dialogTitle.value === t('添加配置')) {
    deviceConfigData.value.push({
      id: generateUUID(),
      deviceId: form.value.deviceId,
      deviceName: form.value.deviceName,
      moduleType: form.value.moduleType,
      config: form.value.config
    })
  } else {
    let index = deviceConfigData.value?.findIndex(
      (item) => item.card_id == form.value.card_id
    )
    deviceConfigData.value.splice(index, 1, {
      id: form.value.id,
      deviceId: form.value.deviceId,
      deviceName: form.value.deviceName,
      moduleType: form.value.moduleType,
      config: form.value.config
    })
  }
  try {
    await setDeviceConfig()
    snackbar.value = true
    snackbarText.value =
      dialogTitle.value === t('添加配置') ? t('添加成功') : t('修改成功')
    loading.value = false
    dialog.value = false
    getData()
  } catch (error) {
    snackbar.value = true
    snackbarText.value = error
    loading.value = false
  }
}
const setDeviceConfig = async () => {
  const res = await useConfigStore().setWebConfigFn(
    JSON.stringify({
      key: 'device',
      config: JSON.stringify(deviceConfigData.value)
    })
  )
}
const handleCancelClick = () => {
  dialog.value = false
}

defineExpose({
  getData
})
</script>

<template>
  <div class="w-100 px-2">
    <v-card class="pa-4 w-100 rounded-lg no-scrollbar" elevation="4">
      <v-btn height="48px" color="primary" @click="handleAddClick">{{
        $t('添加配置')
      }}</v-btn>
      <v-data-table
        :headers="headers"
        :items="deviceConfigData"
        :hide-default-footer="true"
      >
        <template v-slot:item.action="{ item }">
          <v-btn @click="handleEditClick(item)">{{ $t('修改') }}</v-btn>
          <v-btn class="ml-2" @click="handleRemoveClick(item)">{{
            $t('删除')
          }}</v-btn>
        </template>
      </v-data-table>

      <v-dialog v-model="dialog" width="auto">
        <v-card width="740" class="pa-4 rounded-lg">
          <v-card-title class="text-center mb-6">{{
            dialogTitle
          }}</v-card-title>
          <v-sheet class="mx-auto w-full">
            <v-form fast-fail @submit.prevent="submit" ref="FormRef">
              <v-select
                v-model="form.deviceId"
                item-value="id"
                clearable
                :label="$t('选择设备')"
                :placeholder="$t('选择设备')"
                :items="treeData"
                variant="outlined"
                class="w-100 mr-4 mt-2"
                @update:modelValue="handleDeviceChange"
              ></v-select>
              <v-select
                v-model="form.moduleType"
                item-value="id"
                clearable
                :label="$t('模块类型')"
                :placeholder="$t('模块类型')"
                :items="typeData"
                variant="outlined"
                class="w-100 mr-4 mt-2"
              ></v-select>
              <!-- <v-text-field
                v-model="form.moduleType"
                label="模块类型"
                placeholder="模块类型"
                variant="outlined"
                clearable
              ></v-text-field> -->
              <v-text-field
                v-model="form.config.packNumber"
                :label="$t('包总数')"
                :placeholder="$t('包总数')"
                variant="outlined"
                clearable
              ></v-text-field>
              <div class="d-flex justify-center">
                <v-btn
                  class="mt-2 mr-4 px-8"
                  height="50"
                  @click="handleCancelClick"
                  >{{ $t('取消') }}</v-btn
                >
                <v-btn
                  class="mt-2 px-8"
                  type="submit"
                  height="50"
                  :loading="loading"
                  color="primary"
                  >{{ $t('确定') }}</v-btn
                >
              </div>
            </v-form>
          </v-sheet>
        </v-card>
      </v-dialog>
    </v-card>
  </div>
</template>

<style lang="scss" scoped></style>
