<!--
 * HMI优化按钮组件
 * 针对10寸触摸屏优化的按钮组件
-->
<template>
  <v-btn
    :class="[
      'hmi-button',
      'hmi-touch-friendly',
      size === 'small' ? 'hmi-btn-small' : '',
      size === 'large' ? 'hmi-btn-large' : '',
      variant === 'primary' ? 'hmi-btn-primary' : '',
      variant === 'secondary' ? 'hmi-btn-secondary' : '',
      variant === 'success' ? 'hmi-btn-success' : '',
      variant === 'warning' ? 'hmi-btn-warning' : '',
      variant === 'error' ? 'hmi-btn-error' : ''
    ]"
    :size="computedSize"
    :color="computedColor"
    :variant="computedVariant"
    :disabled="disabled"
    :loading="loading"
    :prepend-icon="prependIcon"
    :append-icon="appendIcon"
    v-bind="$attrs"
    @click="handleClick"
  >
    <slot></slot>
  </v-btn>
</template>

<script setup>
import { computed } from 'vue'
import { useDisplay } from 'vuetify'

const props = defineProps({
  size: {
    type: String,
    default: 'default',
    validator: (value) => ['small', 'default', 'large'].includes(value)
  },
  variant: {
    type: String,
    default: 'default',
    validator: (value) => ['default', 'primary', 'secondary', 'success', 'warning', 'error'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  prependIcon: {
    type: String,
    default: ''
  },
  appendIcon: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['click'])

const { mobile } = useDisplay()

const computedSize = computed(() => {
  if (mobile.value) {
    return props.size === 'small' ? 'small' : props.size === 'large' ? 'large' : 'default'
  }
  return props.size === 'small' ? 'default' : 'large'
})

const computedColor = computed(() => {
  switch (props.variant) {
    case 'primary':
      return 'primary'
    case 'secondary':
      return 'secondary'
    case 'success':
      return 'success'
    case 'warning':
      return 'warning'
    case 'error':
      return 'error'
    default:
      return undefined
  }
})

const computedVariant = computed(() => {
  return props.variant === 'default' ? 'elevated' : 'elevated'
})

const handleClick = (event) => {
  if (!props.disabled && !props.loading) {
    emit('click', event)
  }
}
</script>

<style lang="scss" scoped>
.hmi-button {
  font-weight: 600 !important;
  letter-spacing: 0.5px !important;
  transition: all 0.2s ease !important;
  
  /* 基础尺寸 */
  min-height: 48px !important;
  min-width: 120px !important;
  font-size: 16px !important;
  
  &:hover:not(:disabled) {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15) !important;
  }
  
  &:active:not(:disabled) {
    transform: translateY(0);
  }
  
  /* 图标优化 */
  :deep(.v-icon) {
    font-size: 20px !important;
  }
}

.hmi-btn-small {
  min-height: 40px !important;
  min-width: 100px !important;
  font-size: 14px !important;
  
  :deep(.v-icon) {
    font-size: 18px !important;
  }
}

.hmi-btn-large {
  min-height: 56px !important;
  min-width: 140px !important;
  font-size: 18px !important;
  
  :deep(.v-icon) {
    font-size: 24px !important;
  }
}

/* 颜色变体 */
.hmi-btn-primary {
  background: linear-gradient(135deg, #0093b6 0%, #007a9a 100%) !important;
  color: white !important;
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #007a9a 0%, #006684 100%) !important;
  }
}

.hmi-btn-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
  color: white !important;
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #5a6268 0%, #495057 100%) !important;
  }
}

.hmi-btn-success {
  background: linear-gradient(135deg, #28c79c 0%, #20a085 100%) !important;
  color: white !important;
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #20a085 0%, #1a8a6e 100%) !important;
  }
}

.hmi-btn-warning {
  background: linear-gradient(135deg, #ffaa43 0%, #e6932b 100%) !important;
  color: white !important;
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #e6932b 0%, #cc7d14 100%) !important;
  }
}

.hmi-btn-error {
  background: linear-gradient(135deg, #ff6b6b 0%, #e55555 100%) !important;
  color: white !important;
  
  &:hover:not(:disabled) {
    background: linear-gradient(135deg, #e55555 0%, #cc3f3f 100%) !important;
  }
}

/* 移动端优化 */
@media screen and (max-width: 1024px) {
  .hmi-button {
    min-height: 44px !important;
    min-width: 110px !important;
    font-size: 15px !important;
  }
  
  .hmi-btn-small {
    min-height: 36px !important;
    min-width: 90px !important;
    font-size: 13px !important;
  }
  
  .hmi-btn-large {
    min-height: 52px !important;
    min-width: 130px !important;
    font-size: 17px !important;
  }
}

/* 禁用状态 */
.hmi-button:disabled {
  opacity: 0.6 !important;
  cursor: not-allowed !important;
  transform: none !important;
  box-shadow: none !important;
}

/* 加载状态 */
.hmi-button.v-btn--loading {
  pointer-events: none;
  
  :deep(.v-btn__loader) {
    .v-progress-circular {
      width: 20px !important;
      height: 20px !important;
    }
  }
}
</style>
