<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-23 14:13:48
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String // #C4EBAD
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    :fill="props.fill"
    version="1.1"
    width="234.01524353027344"
    height="150.21820068359375"
    viewBox="0 0 234.01524353027344 150.21820068359375"
  >
    <g>
      <g
        transform="matrix(0.9076363444328308,0.4197574257850647,-0.4197574257850647,0.9076363444328308,4.387509002149272,-19.93954735812349)"
      >
        <path
          d="M104.00604821777344,113.167C88.83934821777343,113.167,74.50604821777344,107.167,63.839348217773434,96.5C53.17268821777344,85.8333,47.33935521777344,71.5,47.50602149777344,56.5C47.50602149777344,41.3333,53.50601821777344,27.1667,64.17264821777344,16.5C74.83934821777343,5.83334,88.83934821777343,0,103.83934821777343,0L104.17264821777343,0C135.33934821777342,0,160.67254821777345,25.3333,160.67254821777345,56.5C160.67254821777345,87.5,135.33934821777342,113,104.00604821777344,113.167ZM103.83934821777343,3.33334C89.67264821777343,3.33334,76.50604821777344,8.83334,66.50604821777344,18.8333C56.506028217773434,28.8333,50.83935821777344,42.3333,50.83935821777344,56.5C50.83935821777344,70.6667,56.17268821777344,84,66.33934821777343,94.1667C76.33934821777343,104.333,89.83934821777343,109.833,104.00604821777344,109.833C133.33934821777342,109.667,157.33954821777343,85.6667,157.33954821777343,56.5C157.33954821777343,27.1667,133.50604821777344,3.33334,104.17264821777343,3.33334L103.83934821777343,3.33334Z"
          :fill="props.fill"
          fill-opacity="1"
          style="mix-blend-mode: passthrough"
        />
      </g>
      <g
        transform="matrix(-0.0006461822777055204,0.9999997615814209,-0.9999997615814209,-0.0006461822777055204,237.95153812232252,-153.50595303271658)"
      >
        <path
          d="M231.19146870117189,44.01630505859375L230.00806870117188,42.14593505859375L228.82476870117188,44.01630505859375L201.54371870117188,87.13653505859375L200.27837870117187,89.13653505859375L259.73786870117186,89.13653505859375L258.47246870117186,87.13653505859375L231.19146870117189,44.01630505859375ZM230.00806870117188,45.88666505859375L256.10586870117186,87.13653505859375L203.91038870117188,87.13653505859375L230.00806870117188,45.88666505859375Z"
          fill-rule="evenodd"
          :fill="props.fill"
          fill-opacity="1"
        />
      </g>
      <g
        transform="matrix(-0.6002175211906433,0.7998367547988892,-0.7998367547988892,-0.6002175211906433,181.28261884819813,3.6053226654230457)"
      >
        <line
          x1="89.74028778076172"
          y1="46.10791015625"
          x2="124.13033294677734"
          y2="46.10791015625"
          fill-opacity="0"
          stroke-opacity="1"
          :stroke="props.fill"
          fill="none"
          stroke-width="2"
        />
      </g>
      <g
        transform="matrix(-0.5196922421455383,-0.8543534874916077,0.8543535470962524,-0.5196921229362488,45.80837346139515,237.77111330751677)"
      >
        <line
          x1="89.74028778076172"
          y1="105.00909423828125"
          x2="127.2790641784668"
          y2="105.00909423828125"
          fill-opacity="0"
          stroke-opacity="1"
          :stroke="props.fill"
          fill="none"
          stroke-width="2"
        />
      </g>
      <g
        transform="matrix(0.9998856782913208,-0.007685639895498753,0.007685589604079723,0.9998857378959656,-0.5834197443605262,0.28967276230504524)"
      >
        <line
          x1="36.55348205566406"
          y1="75.45458984375"
          x2="69.50087356567383"
          y2="75.45458984375"
          fill-opacity="0"
          stroke-opacity="1"
          :stroke="props.fill"
          fill="none"
          stroke-width="2"
        />
      </g>
      <g
        transform="matrix(0.9633679986000061,0.2681834399700165,-0.2681834399700165,0.9633679986000061,5.8806160706117225,-32.2043853052146)"
      >
        <path
          d="M177.49097978515624,118.590828125C146.15757978515626,118.590828125,120.82427978515625,93.090528125,120.82427978515625,61.923828125C120.82427978515625,30.757128125,146.32427978515625,5.423828125,177.49097978515624,5.423828125C208.65757978515626,5.423828125,233.99127978515625,30.923828125,233.99127978515625,62.090528125C233.99127978515625,93.257128125,208.65757978515626,118.590828125,177.49097978515624,118.590828125ZM177.65757978515626,8.757168125C148.32427978515625,8.757168125,124.32429978515626,32.590528125,124.32429978515626,61.923828125C124.32429978515626,91.257128125,148.15767978515626,115.256828125,177.49097978515624,115.256828125C206.82427978515625,115.256828125,230.82427978515625,91.423828125,230.82427978515625,62.090528125C230.65727978515625,32.757128125,206.82427978515625,8.757158125,177.65757978515626,8.757168125Z"
          :fill="props.fill"
          fill-opacity="1"
          style="mix-blend-mode: passthrough"
        />
      </g>
    </g>
  </svg>
</template>
