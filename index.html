<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>EMS</title>
  </head>
  <body>
    <div id="app"></div>
    <div class="loading_" id="_loading_">
      <div class="la-ball-climbing-dot">
        <div></div>
        <div></div>
        <div></div>
        <div></div>
      </div>
    </div>
    <style>
      .loading_ {
        width: 100%;
        height: 100%;
        position: fixed;
        background: rgb(var(--theme-background));
        z-index: 999;
        left: 0;
        top: 0;
      }
      .loading_.hide {
        display: none;
      }
      .la-ball-climbing-dot {
        width: 84px;
        height: 64px;
        position: absolute;
        left: 50%;
        top: 50%;
        margin-left: -42px;
        margin-top: -32px;
        transition: 0.3s;
      }
      .la-ball-climbing-dot > div {
        position: relative;
        box-sizing: border-box;
      }

      .la-ball-climbing-dot > div {
        display: inline-block;
        float: none;
        background-color: #0093b6;
        border: 0 solid #0093b6;
      }

      .la-ball-climbing-dot > div:nth-child(1) {
        position: absolute;
        bottom: 32%;
        left: 8%;
        width: 44px;
        height: 44px;
        border-radius: 100%;
        transform-origin: center bottom;
        animation: jumpA 0.6s ease-in-out infinite;
      }

      .la-ball-climbing-dot > div:not(:nth-child(1)) {
        position: absolute;
        top: 0;
        right: 0;
        width: 44px;
        height: 4px;
        border-radius: 0;
        transform: translate(60%, 0);
        animation: steps 1.8s linear infinite;
      }

      .la-ball-climbing-dot > div:not(:nth-child(1)):nth-child(2) {
        animation-delay: 0ms;
      }

      .la-ball-climbing-dot > div:not(:nth-child(1)):nth-child(3) {
        animation-delay: -600ms;
      }

      .la-ball-climbing-dot > div:not(:nth-child(1)):nth-child(4) {
        animation-delay: -1200ms;
      }

      @keyframes steps {
        0% {
          top: 0;
          right: 0;
          opacity: 0;
        }
        50% {
          opacity: 1;
        }
        100% {
          top: 100%;
          right: 100%;
          opacity: 0;
        }
      }

      @keyframes jumpA {
        0% {
          transform: scale(1, 0.7);
        }
        20% {
          transform: scale(0.7, 1.2);
        }
        40% {
          transform: scale(1, 1);
        }
        50% {
          bottom: 125%;
        }
        46% {
          transform: scale(1, 1);
        }
        80% {
          transform: scale(0.7, 1.2);
        }
        90% {
          transform: scale(0.7, 1.2);
        }
        100% {
          transform: scale(1, 0.7);
        }
      }
    </style>
    <script type="module" src="/src/main.js"></script>
  </body>
</html>
