<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-10 17:02:30
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String // #C4EBAD
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    version="1.1"
    width="16"
    height="16"
    viewBox="0 0 16 16"
  >
    <defs>
      <clipPath id="master_svg0_278_5243">
        <rect x="0" y="0" width="16" height="16" rx="0" />
      </clipPath>
    </defs>
    <g clip-path="url(#master_svg0_278_5243)">
      <g>
        <path
          d="M7.99999734375,3.63787841796875L2.29052734375,8.60376841796875L2.29052734375,13.70947841796875C2.29052734375,14.28037841796875,2.86147434375,14.85137841796875,3.43241734375,14.85137841796875L12.56762734375,14.85137841796875C13.13852734375,14.85137841796875,13.70942734375,14.28037841796875,13.70942734375,13.70947841796875L13.70942734375,8.60376841796875L7.99999734375,3.63787841796875ZM9.71284734375,13.13850841796875L6.28715734375,13.13850841796875L6.28715734375,10.28376841796875C6.28715734375,9.65311841796875,6.79840734375,9.14187841796875,7.42905734375,9.14187841796875L8.57094734375,9.14187841796875C9.201597343749999,9.14187841796875,9.71283734375,9.65311841796875,9.71284734375,10.28376841796875L9.71284734375,13.13850841796875Z"
          :fill="fill"
          style="mix-blend-mode: passthrough"
        />
      </g>
      <g>
        <path
          d="M15.9557359375,8.17789060546875C15.8717359375,8.41315060546875,15.6491359375,8.57039060546875,15.3993359375,8.57094060546875L14.5418359375,8.57094060546875L7.9999859375,2.88126060546875L1.4580859375,8.57094060546875L0.6004059375,8.57094060546875C0.3506739375,8.57031060546875,0.1281789375,8.41308060546875,0.0441957375,8.17789060546875C-0.0426920625,7.94178060546875,0.0251450375,7.67655060546875,0.2147219375,7.51115060546875L7.1726159375,1.45536360546875C7.4023459375,1.25661760546875,7.6962159375,1.14767050746875,7.9999859375,1.14862680434875C8.2966159375,1.14862680434875,8.5932459375,1.25115360546875,8.8273559375,1.45536360546875L10.8547359375,3.21957060546875L10.8547359375,2.2905206054687497C10.8545359375,1.97509960546875,11.1102359375,1.7193406054687501,11.4256359375,1.71957460546875L11.9966359375,1.71957460546875C12.3120359375,1.7193406054687501,12.5678359375,1.97509960546875,12.5675359375,2.2905206054687497L12.5675359375,4.7103106054687505L15.7852359375,7.51094060546875C15.9747359375,7.67494060546875,16.0423359375,7.94084060546875,15.9557359375,8.17789060546875Z"
          :fill="fill"
          style="mix-blend-mode: passthrough"
        />
      </g>
    </g>
  </svg>
</template>
