/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-27 15:11:45
 * @LastEditors: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @LastEditTime: 2025-08-27 20:06:28
 * @FilePath: \ems_manage\src\store\global.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { defineStore } from 'pinia'
import { ref } from 'vue'
import {
  getLanguageList,
  getLanguageAll,
  getLanguage,
  setLanguageNo,
  setLanguage
} from '@/api/global'
import { useIndexedDB } from '@/hook/useIndexedDB'

export const useGlobalStore = defineStore(
  'global',
  () => {
    const snackbar = ref(false)
    const snackbarText = ref('')

    /**
     * 语言
     */
    const langFileNameArr = ref([])
    const languageCont = ref()
    const getLanguageListFn = async () => {
      try {
        const res = await getLanguageList()
        let newLanguages = res.data.languages.map((item) => {
          return {
            title: item[0],
            value: item[0],
            uuid: item[1]
          }
        })
        if (!langFileNameArr.value.length) {
          langFileNameArr.value = newLanguages
          await getLanguageAllFn()
        } else {
          // 找出新增的语言文件
          const existingUuids = new Set(
            langFileNameArr.value.map((item) => item.uuid)
          )
          const newFiles = newLanguages.filter(
            (item1) => !existingUuids.has(item1.uuid)
          )

          if (newFiles.length > 0) {
            langFileNameArr.value = newLanguages
            // 获取新增的语言文件内容
            await Promise.all(newFiles.map((file) => getLanguageFn(file.value)))
          }
        }
        getLanguageCont()
      } catch (error) {
        console.log(error)
        snackbar.value = true
        snackbarText.value = error
      }
    }
    const getLanguageCont = () => {
      let lang = localStorage.getItem('lang') || 'en'
      if (lang == 'en') return
      const { openDB } = useIndexedDB()
      openDB('languageDB', 1).then((db) => {
        const transaction = db.transaction(['language'], 'readonly')
        const objectStore = transaction.objectStore('language')
        let getKey = lang == 'zhHans' ? 'zh_CN' : lang
        let getData = objectStore.get(getKey)
        getData.onsuccess = () => {
          languageCont.value = getData.result.content
        }
      })
    }
    const stringToLanguageObject = (file, content) => {
      let [fileName] = file.split('.')
      let languageContArr = content.split('\r\n')
      let languageCont = {}
      languageContArr.forEach((item) => {
        let [key, value] = item.split('=')
        if (key) languageCont[key] = value
      })
      return {
        fileName,
        content: languageCont
      }
    }
    const saveLanguageToIndexedDB = async ({ fileName, content }) => {
      const { openDB } = useIndexedDB()
      try {
        const resDB = await openDB('languageDB', 1, (db) => {
          if (!db.objectStoreNames.contains('language')) {
            const objectStore = db.createObjectStore('language', {
              keyPath: 'fileName'
            })
          }
        })

        const tx = resDB.transaction('language', 'readwrite')
        const store = tx.objectStore('language')

        const isExist = await new Promise((resolve) => {
          const request = store.get(fileName)
          request.onsuccess = () => resolve(request.result)
          request.onerror = () => resolve(null)
        })

        if (!isExist) {
          store.add({ fileName, content })
        } else {
          store.put({ fileName, content })
        }

        return tx.complete
      } catch (error) {
        console.error('保存语言文件到IndexedDB失败:', error)
        throw error
      }
    }
    const getLanguageAllFn = async () => {
      try {
        const res = await getLanguageAll()
        let data = res.data
        const allLanguageObj = []
        for (let key in data) {
          let languageObj = stringToLanguageObject(key, data[key])
          allLanguageObj.push(languageObj)
        }

        await Promise.all(
          allLanguageObj.map((item) => saveLanguageToIndexedDB(item))
        )
      } catch (error) {
        snackbar.value = true
        snackbarText.value = error
      }
    }
    const getLanguageFn = async (file) => {
      try {
        const res = await getLanguage({ fileName: file })
        let data = res.data[file]
        let languageObj = stringToLanguageObject(file, data)
        saveLanguageToIndexedDB(languageObj)
      } catch (error) {
        snackbar.value = true
        snackbarText.value = error
      }
    }

    /**
     * 键盘
     */
    const showKeyboard = ref(false)
    const currentInput = ref(null)
    const confirmCall = ref(null)
    const keyboardDialog = ref(false)
    const keyboardInput = ref(null)
    const keyboardInputValue = ref('')
    const keyboardMode = ref('di_gt')
    const keyboardRange = ref([])
    const isShowKeyboard = ref(true)

    /**
     * 拖拽面板
     */
    const showPanel = ref(false)
    const panelType = ref('')

    /**
     * 收益
     */
    const legendSelected = ref({})

    return {
      snackbar,
      snackbarText,
      showKeyboard,
      currentInput,
      confirmCall,
      keyboardDialog,
      keyboardInput,
      keyboardInputValue,
      keyboardMode,
      keyboardRange,
      isShowKeyboard,
      showPanel,
      panelType,
      legendSelected,
      langFileNameArr,
      getLanguageListFn,
      getLanguageAllFn,
      languageCont,
      getLanguageFn
    }
  },
  {
    persist: [
      {
        omit: ['languageCont']
      }
    ]
  }
)
