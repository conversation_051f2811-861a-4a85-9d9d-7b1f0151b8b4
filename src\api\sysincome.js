/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2025-07-30 11:15:45
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2025-07-30 12:00:15
 * @FilePath: \ems_manage\src\api\sysincome.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { request } from './index'

// 获取系统收入配置
export const getSysincomeConfig = () => {
  return request({
    url: '/sysincome/config',
    method: 'get'
  })
}

// 设置系统收入场景
export const setSysincomeScenario = (data) => {
  return request({
    url: '/sysincome/scenario',
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}

// 设置系统收入区域 
export const setSysincomeRegion = (data) => {
  return request({
    url: '/sysincome/region',
    method: 'put',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}

// 获取最新系统收入数据 
export const getSysincomeData = () => {
  return request({
    url: '/sysincome/data',
    method: 'post',
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}

// 按天查询收入数据
export const getSysincomeDay = (data) => {
  return request({
    url: '/sysincome/day',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}

// 按月查询收入数据
export const getSysincomeMonth = (data) => {
  return request({
    url: '/sysincome/month',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}

// 按年查询收入数据 
export const getSysincomeYear = (data) => {
  return request({
    url: '/sysincome/year',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}

// 获取单位时间记录 
export const getSysincomeUnitTime = (data) => {
  return request({
    url: '/sysincome/unitTime',
    method: 'post',
    data,
    headers: {
      'Content-Type': 'application/json;'
    }
  })
}