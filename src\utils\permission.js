/*
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-18 16:20:59
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-09-18 16:25:53
 * @FilePath: \ems_manage\src\utils\permission.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import { useUserStore } from '@/store/module/user'

export const checkRole = (level) => useUserStore().userInfo.permission_level == level

