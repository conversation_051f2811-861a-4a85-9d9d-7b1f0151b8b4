<!--
 * @Author: gg-guang <EMAIL>
 * @Date: 2024-09-27 15:33:04
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-12-01 15:39:25
 * @FilePath: \ems_manage\src\views\Dashboard\coms\info-card.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
import { onUnmounted, ref, toRefs } from 'vue'
import FlipClock from '@/components/flip-clock/index.vue'
import dayjs from '@/utils/date'
import { useConfigStore } from '@/store/module/config'

const { localTime } = toRefs(useConfigStore())
const currentDate = ref()
const time = ref(null)

const scheduleNextUpdate = () => {
  // let localDate = localTime.value.split(' ')[0]
  const now = localTime.value ? dayjs(localTime.value) : dayjs()
  currentDate.value = now.format('YYYY-MM-DD')
  const nextMidnight = now.add(1, 'day').startOf('day') // 下一个午夜

  // 计算从现在到下一个午夜的毫秒数
  const timeToNextMidnight = nextMidnight.diff(now, 'millisecond')

  // 设置一次性定时器
  time.value = setTimeout(() => {
    currentDate.value = dayjs().format('YYYY-MM-DD') // 更新日期
    scheduleNextUpdate() // 递归调用以设置新的定时器
  }, timeToNextMidnight)
}

scheduleNextUpdate()

onUnmounted(() => {
  clearTimeout(time.value)
})
</script>

<template>
  <v-card height="170px" elevation="4" class="rounded-lg">
    <div class="d-flex flex-no-wrap justify-space-between h-full">
      <div>
        <v-card-title> {{ $t('实时时间') }} </v-card-title>
        <div
          class="ml-4"
          style="font-size: 30px; font-weight: 600; color: rgba(0, 0, 0, 0.7)"
        >
          {{ currentDate }}
        </div>
        <FlipClock class="ml-3" :date="localTime"></FlipClock>
      </div>
      <div class="d-flex align-center mr-4">
        <img
          src="../../../assets/img/time.webp"
          style="width: 100px; height: 100px"
        />
      </div>
    </div>
  </v-card>
</template>

<style lang="scss" scoped></style>
