<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-23 10:20:11
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String, // #C4EBAD
  backFill: String
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    fill="none"
    version="1.1"
    width="16"
    height="12.73038101196289"
    viewBox="0 0 16 12.73038101196289"
  >
    <defs>
      <clipPath id="master_svg0_233_5669">
        <rect x="0" y="0" width="16" height="12.73038101196289" rx="0" />
      </clipPath>
    </defs>
    <g clip-path="url(#master_svg0_233_5669)">
      <g>
        <rect
          x="5.4976806640625"
          y="6.125244140625"
          width="6.14616584777832"
          height="4.275579929351807"
          rx="0"
          :fill="backFill"
        />
      </g>
      <g>
        <path
          d="M13.4164,9.90400607421875L12.118,9.90400607421875L12.118,8.163436074218751L12.9836,8.163436074218751C13.4598,8.163436074218751,13.8492,7.20626607421875,13.8492,5.98784607421875C13.8492,4.76975607421875,13.4598,3.81258607421875,12.9836,3.81258607421875L12.118,3.81258607421875L12.118,2.50729607421875C12.118,2.24604607421875,11.9449,2.0719960742187498,11.6852,2.0719960742187498C11.4256,2.0719960742187498,11.2525,2.24606607421875,11.2525,2.50730607421875L11.2525,2.94228607421875L9.9541,2.94228607421875L9.9541,2.24604607421875L10.2569,1.94137607421875C10.3434,1.85450607421875,10.3869,1.76763607421875,10.3869,1.63700607421875L10.3869,1.2016990742187499C10.6466,1.2016990742187499,10.8197,1.02795407421875,10.8197,0.76671207421875C10.8197,0.50546007421875,10.6466,0.33172607421875,10.3869,0.33172607421875L6.92459,0.33172607421875C6.66492,0.33172607421875,6.49181,0.50578307421875,6.49181,0.76671207421875C6.49181,1.02795407421875,6.66492,1.2016990742187499,6.92459,1.2016990742187499L6.92459,1.63731607421875C6.92459,1.76794607421875,6.96771,1.85481607421875,7.05458,1.94168607421875L7.35738,2.24636607421875L7.35738,2.94261607421875L6.05902,2.94261607421875L6.05902,1.63697607421875C6.05902,1.37573607421875,5.10688,1.20167707421875,3.89508,1.20167707421875C2.68328,1.20167707421875,1.73114,1.37605607421875,1.73114,1.63729607421875L1.73114,2.94258607421875L0.865572,2.94258607421875C0.389662,2.94226607421875,0,4.290686074218749,0,5.98783607421875C0,7.68499607421875,0.389662,9.03372607421875,0.865572,9.03372607421875L1.73114,9.03372607421875L1.73114,9.90401607421875L0.432802,9.90401607421875C0.173099,9.90400607421875,0,10.07775607421875,0,10.33902607421875C0,10.60022607421875,0.17311,10.77402607421875,0.432792,10.77402607421875L13.4164,10.77402607421875C13.6761,10.77402607421875,13.8492,10.59992607421875,13.8492,10.33902607421875C13.8492,10.07776607421875,13.6761,9.90401607421875,13.4164,9.90400607421875L13.4164,9.90400607421875ZM10.3869,8.16342607421875L9.52132,8.16342607421875L9.52132,6.42283607421875L10.3869,6.42283607421875L10.3869,8.16342607421875ZM7.79016,8.16342607421875L7.79016,6.42283607421875L8.65573,6.42283607421875L8.65573,8.16342607421875L7.79016,8.16342607421875ZM6.92459,8.16342607421875L6.05902,8.16342607421875L6.05902,6.42283607421875L6.92459,6.42283607421875L6.92459,8.16342607421875ZM6.05902,9.03372607421875L11.2525,9.03372607421875L11.2525,9.90401607421875L6.05902,9.90401607421875L6.05902,9.03372607421875Z"
          :fill="fill"
        />
      </g>
    </g>
  </svg>
</template>
