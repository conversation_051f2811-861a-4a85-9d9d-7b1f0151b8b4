import { leftAsideStore } from 'maotu'

const register_config = [
  {
    id: 'flow-ac',
    title: '电网',
    type: 'custom-svg',
    thumbnail: '/svgs/ac.svg',
    props: {
      fill: {
        type: 'color',
        val: '#3FB1E3',
        title: '填充色'
      },
      backFill: {
        type: 'color',
        val: '#FFF',
        title: '背景色'
      },
    }
  },
  {
    id: 'flow-cell',
    title: '电池',
    type: 'custom-svg',
    thumbnail: '/svgs/cell.svg',
    props: {
      fill: {
        type: 'color',
        val: '#C4EBAD',
        title: '填充色'
      },
      backFill: {
        type: 'color',
        val: '#FFF',
        title: '背景色'
      },
    }
  },
  {
    id: 'flow-device',
    title: '设备',
    type: 'custom-svg',
    thumbnail: '/svgs/device.svg',
    props: {
      fill: {
        type: 'color',
        val: '#96DEE8',
        title: '填充色'
      },
      backFill: {
        type: 'color',
        val: '#FFF',
        title: '背景色'
      },
    }
  },
  {
    id: 'flow-diesel',
    title: '柴油机',
    type: 'custom-svg',
    thumbnail: '/svgs/diesel.svg',
    props: {
      fill: {
        type: 'color',
        val: '#626C91',
        title: '填充色'
      },
      backFill: {
        type: 'color',
        val: '#FFF',
        title: '背景色'
      },
    }
  },
  {
    id: 'flow-load',
    title: '负载',
    type: 'custom-svg',
    thumbnail: '/svgs/load.svg',
    props: {
      fill: {
        type: 'color',
        val: '#6BE6C1',
        title: '填充色'
      },
      backFill: {
        type: 'color',
        val: '#FFF',
        title: '背景色'
      },
    }
  },
  {
    id: 'flow-pv',
    title: '光伏',
    type: 'custom-svg',
    thumbnail: '/svgs/pv.svg',
    props: {
      fill: {
        type: 'color',
        val: '#F8B52F',
        title: '填充色'
      },
      backFill: {
        type: 'color',
        val: '#FFF',
        title: '背景色'
      },
    }
  },
]
const register_config2 = [
  {
    id: 'Chunengbianliuqi',
    title: '储能变流器',
    type: 'custom-svg',
    thumbnail: '/svgs/PCS.svg',
    props: {
      fill: {
        type: 'color',
        val: '#1296db',
        title: '填充色'
      },
    }
  },
  {
    id: 'Guangfunibianqi',
    title: '光伏逆变器',
    type: 'custom-svg',
    thumbnail: '/svgs/pvinverter.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      },
    }
  },
  {
    id: 'Pcsgui',
    title: 'pcs柜',
    type: 'custom-svg',
    thumbnail: '/svgs/ac.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      },
    }
  },
  {
    id: 'Dianwang',
    title: '电网',
    type: 'custom-svg',
    thumbnail: '/svgs/dianwang.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      },
    }
  },
  {
    id: 'guangfu',
    title: '光伏',
    type: 'custom-svg',
    thumbnail: '/svgs/guangfu.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      },
    }
  },
  {
    id: 'fangzi',
    title: '负载',
    type: 'custom-svg',
    thumbnail: '/svgs/fangzi.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      },
    }
  },
  {
    id: 'dianchi',
    title: '电池',
    type: 'custom-svg',
    thumbnail: '/svgs/dianchi.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      },
      outline: {
        type: 'color',
        val: '#000',
        title: '描边色'
      }
    }
  },
  {
    id: 'deviceName',
    title: '设备名称',
    type: 'custom-svg',
    thumbnail: '/svgs/deviceName.svg',
    display: true,
    config: {
      can_zoom: true,
      have_anchor: true,
      actual_rect: true
    },
    props: {
      text: {
        title: '文字内容',
        type: 'input',
        val: '文字'
      },
      fontFamily: {
        title: '字体',
        type: 'select',
        val: 'Microsoft YaHei',
        options: [
          {
            value: 'Microsoft YaHei',
            label: '微软雅黑'
          },
          {
            value: 'NSimSun',
            label: '新宋体'
          }
        ]
      },
      fontSize: {
        title: '文字大小',
        type: 'number',
        val: 15
      },
      fill: {
        title: '文字颜色',
        type: 'color',
        val: '#FFF200FF'
      }
    },
  },
  {
    id: 'duanluqi',
    title: '断路器',
    type: 'custom-svg',
    thumbnail: '/svgs/open.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      }
    }
  },
  {
    id: 'DuanluqiClose',
    title: '断路器-闭合',
    type: 'custom-svg',
    thumbnail: '/svgs/close.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      },
      backFill: {
        type: 'color',
        val: '#000',
        title: '背景色'
      }
    }
  },
  {
    id: 'ThreePhase',
    title: '三相变压器',
    type: 'custom-svg',
    thumbnail: '/svgs/ThreePhase.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      }
    }
  },
  {
    id: 'ThreePhase1',
    title: '三相变压器-one',
    type: 'custom-svg',
    thumbnail: '/svgs/ThreePhase1.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      }
    }
  },
  {
    id: 'ThreePhase2',
    title: '三相变压器-two',
    type: 'custom-svg',
    thumbnail: '/svgs/ThreePhase2.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      }
    }
  },
  {
    id: 'transformer',
    title: '变压器',
    type: 'custom-svg',
    thumbnail: '/svgs/transformer.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      }
    }
  },
  {
    id: 'Generator',
    title: '柴油机',
    type: 'custom-svg',
    thumbnail: '/svgs/generator.svg',
    props: {
      fill: {
        type: 'color',
        val: '#000',
        title: '填充色'
      },
      backFill: {
        type: 'color',
        val: '#fff',
        title: '背景色'
      }
    }
  },
  {
    id: 'Circle',
    title: '圆形',
    type: 'custom-svg',
    thumbnail: '/svgs/circle.svg',
    props: {
      fill: {
        type: 'color',
        val: '#fff',
        title: '填充色'
      },
      opacity: {
        type: 'number',
        val: 1,
        title: '填充透明度'
      },
      borderFill: {
        type: 'color',
        val: '#000',
        title: '描边色'
      }
    }
  },
]

// for (const key in modulesFiles) {
//   //根据路径获取svg文件名
//   const name = key.split("/").pop().split(".")[0];
//   console.log(modulesFiles[key])
//   if (name == '电阻') {
//     register_config.push({
//       id: name,
//       title: name,
//       type: 'svg',
//       thumbnail: 'data:image/svg+xml;utf8,' + encodeURIComponent(modulesFiles[key]),
//       svg: modulesFiles[key],
//       props: {
//         stroke: {
//           type: 'color',
//           val: '#FF0000',
//           title: '颜色'
//         }
//       }
//     })
//   }
//   else {
//     register_config.push({
//       id: name,
//       title: name,
//       type: 'svg',
//       thumbnail: 'data:image/svg+xml;utf8,' + encodeURIComponent(modulesFiles[key]),
//       svg: modulesFiles[key],
//       props: {
//         fill: {
//           type: 'color',
//           val: '#FF0000',
//           title: '填充色'
//         }
//       }
//     })
//   }
// }

export function registerMaotuCustomSvg() {
  // const modulesFiles = import.meta.glob("../assets/img/test/**.svg", { eager: true, query: 'raw' })
  // for (const key in modulesFiles) {
  //   //根据路径获取svg文件名
  //   const name = key.split("/").pop().split(".")[0];
  //   if (name == '电阻') {
  //     register_config.push({
  //       id: name,
  //       title: name,
  //       type: 'svg',
  //       thumbnail: 'data:image/svg+xml;utf8,' + encodeURIComponent(modulesFiles[key]),
  //       svg: modulesFiles[key],
  //       props: {
  //         stroke: {
  //           type: 'color',
  //           val: '#FF0000',
  //           title: '颜色'
  //         }
  //       }
  //     })
  //   }
  //   else {
  //     register_config.push({
  //       id: name,
  //       title: name,
  //       type: 'svg',
  //       thumbnail: 'data:image/svg+xml;utf8,' + encodeURIComponent(modulesFiles[key]),
  //       svg: modulesFiles[key],
  //       props: {
  //         fill: {
  //           type: 'color',
  //           val: '#FF0000',
  //           title: '填充色'
  //         }
  //       }
  //     })
  //   }
  // }
  // leftAsideStore.registerConfig('拓补图', register_config)
  leftAsideStore.registerConfig('设备', register_config2)
  leftAsideStore.registerConfig('图片', [
    {
      id: '50DC',
      title: '50DC ( 520)左侧面.30',
      type: 'img',
      thumbnail: '/imgs/50DC.png', //支持远程地址,也支持svg，只是不能变色之类的了，不过会保留矢量属性
      props: {}
    },
    {
      id: '125AC',
      title: '125 IP20 左侧面.33',
      type: 'img',
      thumbnail: '/imgs/125AC.png', //支持远程地址,也支持svg，只是不能变色之类的了，不过会保留矢量属性
      props: {}
    },
    {
      id: '150STS',
      title: '150（520）左侧面.36',
      type: 'img',
      thumbnail: '/imgs/150STS.png', //支持远程地址,也支持svg，只是不能变色之类的了，不过会保留矢量属性
      props: {}
    },
    {
      id: 'battery',
      title: '电池',
      type: 'img',
      thumbnail: '/imgs/battery.svg', //支持远程地址,也支持svg，只是不能变色之类的了，不过会保留矢量属性
      props: {}
    },
    {
      id: 'diesel_engine',
      title: '柴油机',
      type: 'img',
      thumbnail: '/imgs/diesel_engine.svg', //支持远程地址,也支持svg，只是不能变色之类的了，不过会保留矢量属性
      props: {}
    },
    {
      id: 'load',
      title: '负载',
      type: 'img',
      thumbnail: '/imgs/load.svg', //支持远程地址,也支持svg，只是不能变色之类的了，不过会保留矢量属性
      props: {}
    },
    {
      id: 'power_grid',
      title: '电网',
      type: 'img',
      thumbnail: '/imgs/power_grid.svg', //支持远程地址,也支持svg，只是不能变色之类的了，不过会保留矢量属性
      props: {}
    },
    {
      id: 'transf',
      title: '变压器',
      type: 'img',
      thumbnail: '/imgs/transformer.svg', //支持远程地址,也支持svg，只是不能变色之类的了，不过会保留矢量属性
      props: {}
    },
  ]);
}