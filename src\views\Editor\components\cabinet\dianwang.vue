<!--
 * @Author: error: error: git config user.name & please set dead value or install git && error: git config user.email & please set dead value or install git & please set dead value or install git
 * @Date: 2024-07-25 16:03:52
 * @LastEditors: gg-guang <EMAIL>
 * @LastEditTime: 2024-10-10 17:56:39
 * @FilePath: \ems_manage\src\views\Editor\components\ac.vue
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
-->
<script setup>
const props = defineProps({
  fill: String // #C4EBAD
})
</script>

<template>
  <svg
    xmlns="http://www.w3.org/2000/svg"
    xmlns:xlink="http://www.w3.org/1999/xlink"
    version="1.1"
    width="16"
    height="16"
    viewBox="0 0 16 16"
  >
    <defs>
      <clipPath id="master_svg0_278_5169">
        <rect x="0" y="0" width="16" height="16" rx="0" />
      </clipPath>
    </defs>
    <g clip-path="url(#master_svg0_278_5169)">
      <g>
        <path
          d="M4.97344720703125,5.1499950000000005L2.85938720703125,5.1499950000000005C2.60938720703125,5.1499950000000005,2.40626220703125,4.948435,2.40626220703125,4.696875C2.40626220703125,4.446875,2.60782520703125,4.2437450000000005,2.85938720703125,4.2437450000000005L4.97188720703125,4.2437450000000005C5.22188720703125,4.2437450000000005,5.4250172070312495,4.445315,5.4250172070312495,4.696875C5.4250172070312495,4.945315,5.22344720703125,5.1499950000000005,4.97344720703125,5.1499950000000005ZM4.57188720703125,6.143755L3.25625720703125,6.143755C3.04063720703125,6.143755,2.86563720703125,5.968755,2.86563720703125,5.753125C2.86563720703125,5.537505,3.04063720703125,5.362505,3.25625720703125,5.362505L4.57188720703125,5.362505C4.7875072070312505,5.362505,4.9625072070312495,5.537505,4.9625072070312495,5.753125C4.96563720703125,5.970315,4.79063720703125,6.143755,4.57188720703125,6.143755ZM13.07183720703125,5.106255L10.95938720703125,5.106255C10.70938720703125,5.106255,10.50626720703125,4.904685000000001,10.50626720703125,4.653125C10.50626720703125,4.403125,10.70782720703125,4.200005,10.95938720703125,4.200005L13.07193720703125,4.200005C13.32193720703125,4.200005,13.52503720703125,4.401565,13.52503720703125,4.653125C13.52503720703125,4.901565,13.32193720703125,5.106255,13.07183720703125,5.106255ZM12.67343720703125,6.100005L11.35782720703125,6.100005C11.14219720703125,6.100005,10.96719720703125,5.925005,10.96719720703125,5.709375C10.96719720703125,5.493755,11.14219720703125,5.318755,11.35782720703125,5.318755L12.67343720703125,5.318755C12.88903720703125,5.318755,13.06403720703125,5.493755,13.06403720703125,5.709375C13.06403720703125,5.925005,12.88903720703125,6.100005,12.67343720703125,6.100005ZM13.29843720703125,13.578075L11.95625720703125,13.578075C11.95625720703125,13.553175,11.95313720703125,13.529675,11.94844720703125,13.506275L10.03438720703125,2.423435L11.51094720703125,2.423435L11.51094720703125,3.276565C11.51094720703125,3.554685,11.73751720703125,3.784375,12.01876720703125,3.784375C12.29693720703125,3.784375,12.52653720703125,3.557815,12.52653720703125,3.276565L12.52653720703125,1.785937C12.52653720703125,1.748437,12.52343720703125,1.709375,12.51563720703125,1.675C12.45623720703125,1.3875,12.20153720703125,1.171875,11.89844720703125,1.171875L4.03594720703125,1.171875C3.7328272070312503,1.171875,3.48125720703125,1.3875,3.42188720703125,1.6734369999999998C3.4140772070312497,1.709375,3.41094720703125,1.745312,3.41094720703125,1.784375L3.41094720703125,3.274995C3.41094720703125,3.553125,3.63751720703125,3.782815,3.91875720703125,3.782815C4.19688720703125,3.782815,4.42657720703125,3.556245,4.42657720703125,3.274995L4.42657720703125,2.421875L5.90313720703125,2.421875L3.97188720703125,13.578075L2.71563720703125,13.578075C2.36876220703125,13.578075,2.09063720703125,13.857775,2.09063720703125,14.203075C2.09063720703125,14.549975,2.37032420703125,14.828075,2.71563720703125,14.828075L13.29843720703125,14.828075C13.64533720703125,14.828075,13.92343720703125,14.548475,13.92343720703125,14.203075C13.92343720703125,13.857775,13.64533720703125,13.578075,13.29843720703125,13.578075ZM9.37813720703125,5.1374949999999995L9.73907720703125,7.223435L7.28594720703125,5.140625L9.33438720703125,5.140625C9.35157720703125,5.140625,9.36407720703125,5.139065,9.37813720703125,5.1374949999999995ZM9.95157720703125,8.454685L10.32501720703125,10.623435L7.01719720703125,8.454685L9.95157720703125,8.454685ZM6.48438720703125,5.637495L8.74219720703125,7.556245L6.15469720703125,7.556245L6.48438720703125,5.637495ZM5.93594720703125,8.817185L9.16251720703125,10.931245L5.57032720703125,10.931245L5.93594720703125,8.817185ZM10.53594720703125,11.828075L10.79375720703125,13.318775L7.44844720703125,11.828075L10.53594720703125,11.828075ZM8.91094720703125,2.423435C8.91094720703125,2.426565,8.91250720703125,2.4328149999999997,8.91250720703125,2.437495L9.22500720703125,4.2437450000000005L6.72657720703125,4.2437450000000005L7.04063720703125,2.424995L8.91094720703125,2.424995L8.91094720703125,2.423435ZM5.40313720703125,11.898475L9.16875720703125,13.576575L5.1125172070312495,13.576575L5.40313720703125,11.898475Z"
          :fill="fill"
          style="mix-blend-mode: passthrough"
        />
      </g>
    </g>
  </svg>
</template>
